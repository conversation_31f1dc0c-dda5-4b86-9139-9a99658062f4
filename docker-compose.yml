#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

version: '3'

# Common build arguments
x-build-args: &build-args
  BUILDKIT_INLINE_CACHE: 1

# Common build configuration
x-common-build: &common-build
  context: .
  dockerfile: Dockerfile
  args:
    <<: *build-args

# -----------------------------------------------------------------------
# We don't support docker compose for production environments.
# If you choose to use this type of deployment make sure to
# create you own docker environment file (docker/.env) with your own
# unique random secure passwords and SECRET_KEY.
# -----------------------------------------------------------------------
x-superset-user: &superset-user root
x-superset-depends-on: &superset-depends-on
  - db
  - redis
x-superset-volumes:
  # /app/pythonpath_docker will be appended to the PYTHONPATH in the final container
  &superset-volumes
  - ./docker:/app/docker
  - ./superset:/app/superset
  - ./superset-frontend:/app/superset-frontend
  - superset_home:/app/superset_home
  - ./tests:/app/tests

services:
  nginx:
    image: nginx:latest
    container_name: superset_nginx
    restart: unless-stopped
    ports:
      - "80:80"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - app_network

  redis:
    image: redis:7
    container_name: superset_cache
    restart: unless-stopped
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis:/data
    networks:
      - app_network

  db:
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    image: postgres:15
    container_name: superset_db
    networks:
      - app_network
    restart: unless-stopped
    ports:
      - "127.0.0.1:5432:5432"
    volumes:
      - db_home:/var/lib/postgresql/data
      - ./docker/docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d

  superset:
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    build:
      <<: *common-build
    container_name: superset_app
    command: [ "/app/docker/docker-bootstrap.sh", "app" ]
    restart: unless-stopped
    ports:
      - 8088:8088
      # When in cypress-mode ->
      - 8081:8081
    extra_hosts:
      - "host.docker.internal:host-gateway"
    user: *superset-user
    depends_on:
      superset-init:
        condition: service_completed_successfully
    volumes: *superset-volumes
    networks:
      - app_network
    environment:
      CYPRESS_CONFIG: "${CYPRESS_CONFIG:-}"
      SUPERSET_LOG_LEVEL: "${SUPERSET_LOG_LEVEL:-info}"

  superset-websocket:
    container_name: superset_websocket
    build: ./superset-websocket
    ports:
      - 8080:8080
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - redis
    # Mount everything in superset-websocket into container and
    # then exclude node_modules and dist with bogus volume mount.
    # This is necessary because host and container need to have
    # their own, separate versions of these files. .dockerignore
    # does not seem to work when starting the service through
    # docker compose.
    #
    # For example, node_modules may contain libs with native bindings.
    # Those bindings need to be compiled for each OS and the container
    # OS is not necessarily the same as host OS.
    volumes:
      - ./superset-websocket:/home/<USER>
      - /home/<USER>/node_modules
      - /home/<USER>/dist

      # Mounting a config file that contains a dummy secret required to boot up.
      # do not use this docker compose in production
      - ./docker/superset-websocket/config.json:/home/<USER>/config.json
    environment:
      - PORT=8080
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_SSL=false

  superset-init:
    build:
      <<: *common-build
    container_name: superset_init
    command: [ "/app/docker/docker-init.sh" ]
    networks:
      - app_network
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    depends_on:
      db:
        condition: service_started
      redis:
        condition: service_started
    user: *superset-user
    volumes: *superset-volumes
    environment:
      CYPRESS_CONFIG: "${CYPRESS_CONFIG:-}"
      SUPERSET_LOAD_EXAMPLES: "${SUPERSET_LOAD_EXAMPLES:-no}"
      SUPERSET_LOG_LEVEL: "${SUPERSET_LOG_LEVEL:-info}"
    healthcheck:
      disable: true

  superset-node:
    build:
      context: .
      target: superset-node
      args:
        # This prevents building the frontend bundle since we'll mount local folder
        # and build it on startup while firing docker-frontend.sh in dev mode, where
        # it'll mount and watch local files and rebuild as you update them
        DEV_MODE: "true"
        BUILD_TRANSLATIONS: ${BUILD_TRANSLATIONS:-false}
    environment:
      # set this to false if you have perf issues running the npm i; npm run dev in-docker
      # if you do so, you have to run this manually on the host, which should perform better!
      BUILD_SUPERSET_FRONTEND_IN_DOCKER: false
      NPM_RUN_PRUNE: false
      SCARF_ANALYTICS: "${SCARF_ANALYTICS:-}"
      # configuring the dev-server to use the host.docker.internal to connect to the backend
      superset: "http://superset:8088"
    ports:
      - "127.0.0.1:9000:9000"  # exposing the dynamic webpack dev server
    container_name: superset_node
    command: [ "/app/docker/docker-frontend.sh" ]
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    depends_on:
      db:
        condition: service_started
      redis:
        condition: service_started
      superset:
        condition: service_started

    networks:
      - app_network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes: *superset-volumes

  superset-worker:
    build:
      <<: *common-build
    container_name: superset_worker
    command: [ "/app/docker/docker-bootstrap.sh", "worker" ]
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    environment:
      CELERYD_CONCURRENCY: 2
      CYPRESS_CONFIG: "${CYPRESS_CONFIG:-}"
      SUPERSET_LOG_LEVEL: "${SUPERSET_LOG_LEVEL:-info}"
    restart: unless-stopped
    depends_on:
      superset-init:
        condition: service_completed_successfully
    user: *superset-user
    volumes: *superset-volumes
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: [ "CMD-SHELL", "celery -A superset.tasks.celery_app:app inspect ping -d celery@$$HOSTNAME" ]
    # Bump memory limit if processing selenium / thumbnails on superset-worker
    # mem_limit: 2038m
    # mem_reservation: 128M

  superset-worker-beat:
    build:
      <<: *common-build
    container_name: superset_worker_beat
    command: [ "/app/docker/docker-bootstrap.sh", "beat" ]
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    restart: unless-stopped
    depends_on:
      - superset-worker
    user: *superset-user
    volumes: *superset-volumes
    healthcheck:
      disable: true
    environment:
      CYPRESS_CONFIG: "${CYPRESS_CONFIG:-}"
      SUPERSET_LOG_LEVEL: "${SUPERSET_LOG_LEVEL:-info}"

  superset-tests-worker:
    build:
      <<: *common-build
    container_name: superset_tests_worker
    command: [ "/app/docker/docker-bootstrap.sh", "worker" ]
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    profiles:
      - optional
    environment:
      DATABASE_HOST: localhost
      DATABASE_DB: test
      REDIS_CELERY_DB: 2
      REDIS_RESULTS_DB: 3
      REDIS_HOST: localhost
      CELERYD_CONCURRENCY: 8
      SUPERSET_LOG_LEVEL: "${SUPERSET_LOG_LEVEL:-info}"
    network_mode: host
    depends_on:
      superset-init:
        condition: service_completed_successfully
    user: *superset-user
    volumes: *superset-volumes
    healthcheck:
      test: [ "CMD-SHELL", "celery inspect ping -A superset.tasks.celery_app:app -d celery@$$HOSTNAME" ]

  ollama:
    build:
      context: .
      dockerfile: superset-llm/Dockerfile.ollama
      args:
        MODEL: llama3.1:8b
    container_name: ollama_server
    mem_limit: 15g
    ports:
      - "11434:11434"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
    networks:
      - app_network

  llm_flask_app:
    build:
      context: .
      dockerfile: superset-llm/Dockerfile.flask
    container_name: llm_flask_app
    ports:
      - "8111:8111"
    environment:
      - FLASK_APP=superset-llm/app.py
      - FLASK_ENV=development
      - IS_DOCKER=true
      - OLLAMA_API_HOST=http://ollama:11434 # Access Ollama via service name
    volumes:
      - .:/app
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - ollama
      - db
      - mongodb
    networks:
      - app_network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
    user: root

  vectordb:
    hostname: vectordb
    image: ankane/pgvector
    ports:
      - 5433:5432
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
    networks:
      - app_network
    environment:
      - POSTGRES_DB=vectordb
      - POSTGRES_USER=testuser
      - POSTGRES_PASSWORD=testpwd
      - POSTGRES_HOST_AUTH_METHOD=trust
    volumes:
      - ./docker/docker-entrypoint-initdb.d/vector_init.sql:/docker-entrypoint-initdb.d/init.sql

  qdrant:
    image: qdrant/qdrant
    container_name: qdrant
    restart: unless-stopped
    ports:
      - "6333:6333"
      - "6334:6334"
    networks:
      - app_network
    volumes:
      - qdrant_data:/qdrant/storage

  mongodb:
    image: mongo:6.0
    container_name: mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    networks:
      - app_network
    environment:
      - MONGO_INITDB_ROOT_USERNAME=adminuser
      - MONGO_INITDB_ROOT_PASSWORD=adminuser
      - MONGO_INITDB_DATABASE=file_data
    command: [ "mongod", "--bind_ip", "0.0.0.0" ]
    volumes:
      - mongodb_data:/data/db
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Airflow services
  airflow-webserver:
    image: apache/airflow:2.7.1
    container_name: airflow_webserver
    depends_on:
      airflow-init:
        condition: service_completed_successfully
    environment:
      - AIRFLOW__CORE__EXECUTOR=LocalExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://superset:superset@db:5432/airflow
      - AIRFLOW__CORE__FERNET_KEY=46BKJoQYlPPOexq0OhDZnIlNepKFf87WFwLbfzqDDho=
      - AIRFLOW__CORE__LOAD_EXAMPLES=False
      - AIRFLOW__WEBSERVER__SECRET_KEY=supersecretkey
      - PYTHONPATH=/opt/airflow
    volumes:
      - ./airflow/dags:/opt/airflow/dags
      - ./airflow/plugins:/opt/airflow/plugins
      - ./airflow/logs:/opt/airflow/logs
      - ./airflow/config:/opt/airflow/config
      - ./src:/opt/airflow/src
    ports:
      - "8090:8080"  # Changed from 8080:8080 to 8090:8080
    command: ["airflow", "webserver"]
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - app_network
    restart: unless-stopped

  airflow-scheduler:
    image: apache/airflow:2.7.1
    container_name: airflow_scheduler
    depends_on:
      airflow-init:
        condition: service_completed_successfully
    environment:
      - AIRFLOW__CORE__EXECUTOR=LocalExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://superset:superset@db:5432/airflow
      - AIRFLOW__CORE__FERNET_KEY=46BKJoQYlPPOexq0OhDZnIlNepKFf87WFwLbfzqDDho=
      - AIRFLOW__CORE__LOAD_EXAMPLES=False
      - PYTHONPATH=/opt/airflow
    volumes:
      - ./airflow/dags:/opt/airflow/dags
      - ./airflow/plugins:/opt/airflow/plugins
      - ./airflow/logs:/opt/airflow/logs
      - ./airflow/config:/opt/airflow/config
      - ./src:/opt/airflow/src
    command: ["airflow", "scheduler"]
    networks:
      - app_network
    restart: unless-stopped

  airflow-init:
    image: apache/airflow:2.7.1
    container_name: airflow_init
    environment:
      - AIRFLOW__CORE__EXECUTOR=LocalExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://superset:superset@db:5432/airflow
      - AIRFLOW__CORE__FERNET_KEY=46BKJoQYlPPOexq0OhDZnIlNepKFf87WFwLbfzqDDho=
      - AIRFLOW__CORE__LOAD_EXAMPLES=False
      - _AIRFLOW_DB_MIGRATE=true
      - _AIRFLOW_WWW_USER_CREATE=true
      - _AIRFLOW_WWW_USER_USERNAME=airflow
      - _AIRFLOW_WWW_USER_PASSWORD=airflow
      - PYTHONPATH=/opt/airflow
    volumes:
      - ./airflow/dags:/opt/airflow/dags
      - ./airflow/plugins:/opt/airflow/plugins
      - ./airflow/logs:/opt/airflow/logs
      - ./airflow/config:/opt/airflow/config
    command: ["airflow", "db", "init"]
    depends_on:
      db:
        condition: service_started
    networks:
      - app_network

  # New API service using Dockerfile.fin
  fin_api:
    build:
      context: .
      dockerfile: Dockerfile.fin
    container_name: trading_fin_api
    restart: unless-stopped
    ports:
      - "5001:5000"  # Use a different port to avoid conflicts
    environment:
      - MONGO_HOST=mongodb
      - MONGO_PORT=27017
      - UPSTOX_API_KEY=${UPSTOX_API_KEY}
      - UPSTOX_API_SECRET=${UPSTOX_API_SECRET}
      - UPSTOX_AUTH_CODE=${UPSTOX_AUTH_CODE}
      - UPSTOX_REDIRECT_URI=${UPSTOX_REDIRECT_URI}
    volumes:
      - ./src:/app/src
    depends_on:
      - mongodb
      - db
      - ollama
      - qdrant
    networks:
      - app_network

networks:
  app_network:
    driver: bridge

volumes:
  superset_home:
    external: false
  db_home:
    external: false
  redis:
    external: false
  ollama:
    external: false
  vectordb:
    external: false
  qdrant_data:
    external: false
  mongodb_data:
    external: false
