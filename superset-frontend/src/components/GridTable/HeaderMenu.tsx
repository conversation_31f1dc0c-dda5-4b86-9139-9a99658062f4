/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
import { useCallback } from 'react';
import { styled, t } from '@superset-ui/core';
import type { Column, ColumnPinnedType, GridApi } from 'ag-grid-community';

import Icons from 'src/components/Icons';
import { Dropdown, DropdownProps } from 'src/components/Dropdown';
import { Menu } from 'src/components/Menu';
import copyTextToClipboard from 'src/utils/copy';
import { PIVOT_COL_ID } from './constants';
import { CaretDownOutlined } from '@ant-design/icons';

const IconMenuItem = styled(Menu.Item)`
  display: flex;
  align-items: center;
`;
const IconEmpty = styled.span`
  width: 20px;
`;

type Params = {
  colId: string;
  column?: Column;
  api: GridApi;
  pinnedLeft?: boolean;
  pinnedRight?: boolean;
  invisibleColumns: Column[];
  isMain?: boolean;
  onVisibleChange: DropdownProps['onVisibleChange'];
};

const HeaderMenu: React.FC<Params> = ({
  colId,
  api,
  pinnedLeft,
  pinnedRight,
  invisibleColumns,
  isMain,
  onVisibleChange,
}: Params) => {
  const pinColumn = useCallback(
    (pinLoc: ColumnPinnedType) => {
      api.setColumnsPinned([colId], pinLoc);
    },
    [api, colId],
  );

  // Main menu items for grid-level actions
  const getMainMenuItems = () => [
    {
      key: 'copy-data',
      icon: <Icons.CopyOutlined iconSize="m" />,
      label: t('Copy the current data'),
      onClick: () => {
        copyTextToClipboard(
          () =>
            new Promise((resolve, reject) => {
              const data = api.getDataAsCsv({
                columnKeys: api
                  .getAllDisplayedColumns()
                  .map(c => c.getColId())
                  .filter(id => id !== colId),
                suppressQuotes: true,
                columnSeparator: '\t',
              });
              if (data) {
                resolve(data);
              } else {
                reject();
              }
            }),
        );
      },
    },
    {
      key: 'download-csv',
      icon: <Icons.DownloadOutlined iconSize="m" />,
      label: t('Download to CSV'),
      onClick: () => {
        api.exportDataAsCsv({
          columnKeys: api
            .getAllDisplayedColumns()
            .map(c => c.getColId())
            .filter(id => id !== colId),
        });
      },
    },
    { key: 'divider-1', type: 'divider' },
    {
      key: 'autosize-all',
      icon: <Icons.ColumnWidthOutlined iconSize="m" />,
      label: t('Autosize all columns'),
      onClick: () => {
        api.autoSizeAllColumns();
      },
    },
    ...(invisibleColumns.length > 0 ? [getUnhideSubmenu()] : []),
    { key: 'divider-2', type: 'divider' },
    {
      key: 'reset-columns',
      icon: <IconEmpty className="anticon" />,
      label: t('Reset columns'),
      onClick: () => {
        api.setColumnsVisible(invisibleColumns, true);
        const columns = api.getColumns();
        if (columns) {
          const pinnedColumns = columns.filter(
            c => c.getColId() !== PIVOT_COL_ID && c.isPinned(),
          );
          api.setColumnsPinned(pinnedColumns, null);
          api.moveColumns(columns, 0);
          const firstColumn = columns.find(
            c => c.getColId() !== PIVOT_COL_ID,
          );
          if (firstColumn) {
            api.ensureColumnVisible(firstColumn, 'start');
          }
        }
      },
    },
  ];

  // Column-specific menu items
  const getColumnMenuItems = () => [
    {
      key: 'copy-column',
      icon: <Icons.CopyOutlined iconSize="m" />,
      label: t('Copy'),
      onClick: () => {
        copyTextToClipboard(
          () =>
            new Promise((resolve, reject) => {
              const data = api.getDataAsCsv({
                columnKeys: [colId],
                suppressQuotes: true,
              });
              if (data) {
                resolve(data);
              } else {
                reject();
              }
            }),
        );
      },
    },
    ...(pinnedLeft || pinnedRight ? [{
      key: 'unpin',
      icon: <Icons.UnlockOutlined iconSize="m" />,
      label: t('Unpin'),
      onClick: () => pinColumn(null),
    }] : []),
    ...(!pinnedLeft ? [{
      key: 'pin-left',
      icon: <Icons.VerticalRightOutlined iconSize="m" />,
      label: t('Pin Left'),
      onClick: () => pinColumn('left'),
    }] : []),
    ...(!pinnedRight ? [{
      key: 'pin-right',
      icon: <Icons.VerticalLeftOutlined iconSize="m" />,
      label: t('Pin Right'),
      onClick: () => pinColumn('right'),
    }] : []),
    { key: 'divider-1', type: 'divider' },
    {
      key: 'autosize-column',
      icon: <Icons.ColumnWidthOutlined iconSize="m" />,
      label: t('Autosize Column'),
      onClick: () => {
        api.autoSizeColumns([colId]);
      },
    },
    {
      key: 'hide-column',
      icon: <Icons.EyeInvisibleOutlined iconSize="m" />,
      label: t('Hide Column'),
      disabled: api.getColumns()?.length === invisibleColumns.length + 1,
      onClick: () => {
        api.setColumnsVisible([colId], false);
      },
    },
    ...(invisibleColumns.length > 0 ? [getUnhideSubmenu()] : []),
  ];

  // Unhide submenu items
  const getUnhideSubmenu = () => ({
    key: 'unhide-submenu',
    icon: <Icons.EyeOutlined iconSize="m" />,
    label: t('Unhide'),
    type: 'submenu',
    children: [
      ...(invisibleColumns.length > 1 ? [{
        key: 'unhide-all',
        label: <b>{t('All %s hidden columns', invisibleColumns.length)}</b>,
        onClick: () => {
          api.setColumnsVisible(invisibleColumns, true);
        },
      }] : []),
      ...invisibleColumns.map(c => ({
        key: `unhide-${c.getColId()}`,
        label: c.getColDef().headerName,
        onClick: () => {
          api.setColumnsVisible([c.getColId()], true);
        },
      })),
    ],
  });

  // Render menu items as JSX elements
  const renderMenuItems = (items: any[]) => {
    return items.map(item => {
      if (item.type === 'divider') {
        return <Menu.Divider key={item.key} />;
      }
      
      if (item.type === 'submenu') {
        return (
          <Menu.SubMenu
            key={item.key}
            title={
              <>
                {item.icon}
                {item.label}
              </>
            }
          >
            {renderMenuItems(item.children)}
          </Menu.SubMenu>
        );
      }

      return (
        <IconMenuItem
          key={item.key}
          onClick={item.onClick}
          disabled={item.disabled}
        >
          {item.icon} {item.label}
        </IconMenuItem>
      );
    });
  };

  const menuItems = isMain ? getMainMenuItems() : getColumnMenuItems();

  return (
    <Dropdown
      placement={isMain ? "bottomLeft" : "bottomRight"}
      trigger={['click']}
      onVisibleChange={onVisibleChange}
      overlay={
        <Menu style={{ width: isMain ? 250 : 180 }} mode="vertical">
          {renderMenuItems(menuItems)}
        </Menu>
      }
    >
       <CaretDownOutlined />
    </Dropdown>
  );
};

export default HeaderMenu;