export const modelHelper = () => {
  try {
    const GPT = localStorage.getItem('gpt') || 'GPT-4o-Mini';
    let result = JSON.parse(
      localStorage.getItem('modelPreference') as string,
    ) || [GPT, 'gpt-4o-mini'];
    return result;
  } catch (error) {
    return false;
  }
};

export const getModelProvider = (model: string) => {
  try {
    let Provider =
      JSON.parse(localStorage.getItem('modelsProviders') as string) || {};

    let providerName;

    Object.entries(Provider)?.forEach(([provider, models]: any) => {
      Object.entries(models)?.forEach(x => {
        if (x?.includes(model)) {
          providerName = provider;
        }
      });
    });

    return providerName || 'ollama';
  } catch (error) {
    return false;
  }
};

export const getModels = () => {
  try {
    const models = JSON.parse(
      localStorage.getItem('modelsProviders') as string,
    );
    return models;
  } catch (error) {
    return false;
  }
};

export const getModelLableOrName = (model: string, type: 'label' | 'name') => {
  try {
    let Provider =
      JSON.parse(localStorage.getItem('modelsProviders') as string) || {};

    let modelName;

    Object.entries(Provider)?.forEach(([_, models]: any) => {
      Object.entries(models)?.forEach(([lable, name]) => {
        if (type == 'label') {
          if (name == model) {
            modelName = lable;
          }
        } else {
          if (lable == model) {
            modelName = name;
          }
        }
      });
    });

    return modelName || 'gpt-4o-mini';
  } catch (error) {
    return false;
  }
};

export const clearModelCache = () => {
  localStorage.removeItem('modelPreference');
  localStorage.removeItem('modelsProviders');
  localStorage.removeItem('gpt');
};
