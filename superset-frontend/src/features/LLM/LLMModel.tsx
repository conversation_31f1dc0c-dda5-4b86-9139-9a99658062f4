import axios from 'axios';
import {
  CloseOutlined,
  FontSizeOutlined,
  WechatWorkOutlined,
} from '@ant-design/icons';
import useBreakpoint from 'antd/lib/grid/hooks/useBreakpoint';
import toast from 'react-hot-toast';
import {
  Button,
  Flex,
  Modal,
  Tooltip,
  Dropdown,
  Popconfirm,
  Slider,
  Popover,
} from 'antd-v5';
import { MdDeleteOutline } from 'react-icons/md';
import { useRef, useState, useEffect } from 'react';
import ChatApp from 'src/dashboard/components/ChatApp/ChatApp';
import { RiResetRightLine } from 'react-icons/ri';
import { AiOutlineFullscreen, AiOutlineFullscreenExit } from 'react-icons/ai';
import Lottie from 'lottie-react';
// @ts-ignore
import groovyWalkAnimation from './SettingUp.json';
// @ts-ignore
import SettingUP_1 from './SettingUp_1.json';
import { BiInfoCircle } from 'react-icons/bi';
import { CgMoreVertical } from 'react-icons/cg';

import GemmaSVG from './AiIcons/gemini.svg';
import ChatGPTSVG from './AiIcons/openai.svg';
import MetaSVG from './AiIcons/ollama.svg';
import DeepSickSVG from './AiIcons/deepseek.svg';
import MistralSVG from './AiIcons/mistral.svg';
import { modelHelper } from 'src/utils/modelHelper';

interface ChatProps {
  id: string;
  IsDashboard: boolean;
}

interface ModalSizeState {
  width: string | number;
  height: string | number;
}

// Define resize stages enum
enum ResizeStage {
  DEFAULT,
  HALF_SCREEN,
  FULL_SCREEN,
}

const iconProps = {
  css: { width: 20, height: 20, color: '#FFF' },
};

const modelIconMap = {
  'gpt-4o-mini': <ChatGPTSVG {...iconProps} />,
  'gemma2-9b-it': <GemmaSVG {...iconProps} />,
  'gemma2:9b': <GemmaSVG {...iconProps} />,
  'llama3.1:8b': <MetaSVG {...iconProps} />,
  'llama3-70b-8192': <MetaSVG {...iconProps} />,
  'llama3-8b-8192': <MetaSVG {...iconProps} />,
  'deepseek-r1:8b': <DeepSickSVG {...iconProps} />,
  'deepseek-r1-distill-llama-70b': <DeepSickSVG {...iconProps} />,
  'mistral-small:22b': <MistralSVG {...iconProps} />,
};

const FloatingButtonWithDialog = (props: ChatProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const screen = useBreakpoint();
  const [modelWidth, setModelWidth] = useState<{
    added: number;
    isAdded: boolean;
  }>({ added: 0, isAdded: false });
  const [IsDelete, setIsDelete] = useState<boolean>(false);
  const [hovered, setHovered] = useState(false);
  const [IsModelConnecting, setIsModelConnecting] = useState<boolean>(false);
  const [resizeStage, setResizeStage] = useState<ResizeStage>(
    ResizeStage.DEFAULT,
  );
  const [originalSize, setOriginalSize] = useState<ModalSizeState | null>(null);
  const [showModelInfo, setshowModelInfo] = useState<boolean>(false);
  const [isResetLoading, setIsResetLoading] = useState<boolean>(false);
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  const [fontSizeValue, setFontSizeValue] = useState<number>(14);
  const [saveChildState, setSaveChildState] = useState<{
    isTyping: Record<string, boolean>;
    isLoading: Record<string, boolean>;
    filePath: string[];
  }>({
    isTyping: {},
    isLoading: {},
    filePath: [],
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const modalRef = useRef<any>(null);
  const model = modelHelper();

  // Default modal size based on screen breakpoints
  const getDefaultModalSize = (): ModalSizeState => {
    return {
      width: screen.xxl ? '33%' : screen.xl ? '38%' : screen.md ? '55%' : '75%',
      height: window.innerHeight > 768 ? '700px' : '85vh',
    };
  };

  // Half screen size
  const getHalfScreenSize = (): ModalSizeState => {
    return {
      width: '50%',
      height: window.innerHeight > 768 ? '700px' : '85vh',
    };
  };

  // Full screen size
  const getFullScreenSize = (): ModalSizeState => {
    return {
      width: '95%',
      height: window.innerHeight > 768 ? '700px' : '85vh',
    };
  };

  const [modalSize, setModalSize] = useState<ModalSizeState>(
    getDefaultModalSize(),
  );

  // Update default size when screen size changes
  useEffect(() => {
    if (resizeStage === ResizeStage.DEFAULT) {
      setModalSize(getDefaultModalSize());
    }
  }, [screen.xxl, screen.xl, screen.md, modelWidth.added, resizeStage]);

  const styles = {
    floatingButton: {
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      backgroundColor: '#20A7C9',
      color: '#fff',
      borderRadius: '50%',
      width: '50px',
      height: '50px',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      fontSize: '30px',
      border: 'none',
      cursor: 'pointer',
      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
      transform: hovered ? 'scale(1.1) rotate(10deg)' : 'scale(1) rotate(0deg)',
      boxShadow: hovered
        ? '0 8px 16px rgba(32, 167, 201, 0.4)'
        : '0 4px 8px rgba(0, 0, 0, 0.1)',
    } as React.CSSProperties,
    resizeHandle: {
      position: 'absolute',
      bottom: '0',
      right: '0',
      width: '15px',
      height: '15px',
      cursor: 'nwse-resize',
      display: 'flex',
      justifyContent: 'flex-end',
      alignItems: 'flex-end',
    } as React.CSSProperties,
    resizeIcon: {
      width: '8px',
      height: '8px',
      borderRight: '2px solid #888',
      borderBottom: '2px solid #888',
    } as React.CSSProperties,
    deleteButton: {
      backgroundColor: 'rgb(167 167 167 / 23%)',
      border: 'none',
      color: 'black',
    } as React.CSSProperties,
    modelTitle: {
      fontSize: '14px',
      margin: 0,
      fontWeight: 'bold',
      color: '#FFF',
      display: 'flex',
      alignItems: 'center',
      gap: '5px',
    } as React.CSSProperties,
    headerButton: {
      borderRadius: '40%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      transition: 'all 0.2s',
      color: '#FFF',
      // backgroundColor: 'black',
    } as React.CSSProperties,
    menuItem: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      padding: '8px 12px',
    } as React.CSSProperties,
  };

  const toggleDialog = () => {
    if (!isDialogOpen) handlerConnectToAI();
    setIsDialogOpen(!isDialogOpen);
  };

  const handlerConnectToAI = async () => {
    try {
      setIsModelConnecting(true);
      const controller = new AbortController();
      const signal = controller.signal;

      abortControllerRef.current = controller;

      const result = await axios.post(
        `${process.env.REACT_APP_LLM_URL}${props.IsDashboard ? 'dashboard' : 'dataset'}/connect_${props.IsDashboard ? 'dashboard' : 'dataset'}`,
        props.IsDashboard
          ? { dashboard_id: props.id }
          : { dataset_id: props.id },
        { signal },
      );

      if (result.status != 200) toast.error('Connection failed');
      setIsModelConnecting(false);
    } catch (error) {
      setIsModelConnecting(false);
      console.log(error);
      toast.dismiss();
      if (error.message !== 'canceled') toast.error('Connection failed');
    }
  };

  // @ts-ignore
  const handleShowHistory = () => {
    if (modelWidth.isAdded) setModelWidth({ added: 0, isAdded: false });
    else setModelWidth({ added: 15, isAdded: true });
  };

  const handleCancel = () => {
    setIsDialogOpen(false);
    toast.dismiss();
    // abortControllerRef.current && abortControllerRef.current.abort();
  };

  const handleClearEmbeddings = async () => {
    try {
      setIsResetLoading(true);
      const result = await axios.delete(
        `${process.env.REACT_APP_LLM_URL}${props.IsDashboard ? 'dashboard' : 'dataset'}/clear_embeddings?${props.IsDashboard ? 'dashboard_id' : 'dataset_id'}=${props.id}`,
      );
      if (result.status === 200) {
        toast.success('Reset RAG successfully');
        handlerConnectToAI();
      }
      setIsResetLoading(false);
      setDropdownOpen(false);
    } catch (error) {
      setDropdownOpen(false);
      setIsResetLoading(false);
      console.log(error);
      toast.error('Failed to reset RAG');
    }
  };

  const cycleResize = () => {
    if (!originalSize && resizeStage === ResizeStage.DEFAULT) {
      setOriginalSize(modalSize);
    }
    switch (resizeStage) {
      case ResizeStage.DEFAULT:
        setModalSize(getHalfScreenSize());
        setResizeStage(ResizeStage.HALF_SCREEN);
        break;
      case ResizeStage.HALF_SCREEN:
        setModalSize(getFullScreenSize());
        setResizeStage(ResizeStage.FULL_SCREEN);
        break;
      case ResizeStage.FULL_SCREEN:
        if (originalSize) {
          setModalSize(originalSize);
        } else {
          setModalSize(getDefaultModalSize());
        }
        setResizeStage(ResizeStage.DEFAULT);
        break;
    }
  };

  const menu = (
    <div className="antd5-dropdown-menu" style={{ minWidth: '220px' }}>
      <div className="antd5-dropdown-menu-item-group">
        <Popconfirm
          title="Clear Chat History"
          description="Are you sure you want to clear all chat history?"
          onConfirm={() => {
            setIsDelete(true);
            setDropdownOpen(false);
          }}
          onCancel={e => e?.stopPropagation()}
          okText="Yes"
          cancelText="No"
          okButtonProps={{
            loading: IsDelete,
          }}
          cancelButtonProps={{
            style: { backgroundColor: 'white' },
          }}
          color="white"
        >
          <div className="antd5-dropdown-menu-item" style={styles.menuItem}>
            <MdDeleteOutline size={17} />
            <span>Clear History</span>
          </div>
        </Popconfirm>
      </div>

      {/* Reset RAG Item with confirmation */}
      <div className="antd5-dropdown-menu-item-group">
        <Popconfirm
          title="Reset RAG System"
          description="Are you sure you want to reset the RAG system?"
          onConfirm={handleClearEmbeddings}
          onCancel={e => e?.stopPropagation()}
          okText="Yes"
          cancelText="No"
          okButtonProps={{
            loading: isResetLoading,
          }}
          cancelButtonProps={{
            style: { backgroundColor: 'white' },
          }}
          color="white"
        >
          <div className="antd5-dropdown-menu-item" style={styles.menuItem}>
            <RiResetRightLine size={17} />
            <span>Reset RAG</span>
          </div>
        </Popconfirm>
      </div>

      {/* Toggle Model Info Item */}
      <div className="antd5-dropdown-menu-item-group">
        <Popover
          content={() => (
            <Slider
              min={14}
              max={20}
              onChange={value => setFontSizeValue(value)}
              value={fontSizeValue}
            />
          )}
          title="Font Size"
          trigger="click"
          color="white"
        >
          <div className="antd5-dropdown-menu-item" style={styles.menuItem}>
            <FontSizeOutlined size={17} />
            <span>Toggle Font Size</span>
          </div>
        </Popover>
      </div>

      <div className="antd5-dropdown-menu-divider"></div>

      {/* Toggle Model Info Item */}
      <div
        className="antd5-dropdown-menu-item"
        style={styles.menuItem}
        onClick={() => {
          setshowModelInfo(prev => !prev);
          setDropdownOpen(false);
        }}
      >
        <BiInfoCircle size={17} />
        <span>{showModelInfo ? 'Hide' : 'Show'} Answer Reasoning</span>
      </div>
    </div>
  );

  const getModelIcon = () =>
    modelIconMap[model[1]?.toLowerCase() as keyof typeof modelIconMap] || '';

  return (
    <div>
      <style>{`
      .chat-modal {
        position:absolute !important;
        bottom: 8% !important;
        right: 1% !important;
        top: unset !important;
      }
      .antd5-dropdown .antd5-dropdown-menu {
        background-color: white;
        border-radius: 8px;
      } 
      .antd5-dropdown-menu-item {
        cursor: pointer;
        transition: background-color 0.3s;
      }
      .antd5-dropdown-menu-item:hover {
        background-color: #f5f5f5;
      }
      .antd5-dropdown-menu-item-group {
        margin: 0;
      }
      .antd5-dropdown-menu-divider {
        margin: 4px 0;
        height: 1px;
        background-color: #f0f0f0;
      }
      .btn-hover:hover {
        background-color: #282828 !important;
      }
      `}</style>
      <Tooltip title="Ask AI" placement="left">
        <Button
          onClick={toggleDialog}
          style={styles.floatingButton}
          onMouseEnter={() => setHovered(true)}
          onMouseLeave={() => setHovered(false)}
        >
          <WechatWorkOutlined />
        </Button>
      </Tooltip>
      {/* Chat Model */}
      <Modal
        className="chat-modal"
        closeIcon={null}
        open={isDialogOpen}
        onCancel={handleCancel}
        destroyOnClose={true}
        forceRender={true}
        width={modalSize.width}
        styles={{
          body: {
            height: modalSize.height,
            padding: 0,
          },
          content: {
            backgroundColor: 'white',
            overflowY: 'hidden',
            position: 'relative',
            overflow: 'auto',
            padding: 0,
            borderRadius: '12px',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
          },
        }}
        style={{
          margin: 0,
          transition: 'all .2s ease-in-out',
        }}
        footer={null}
        mask={false}
        panelRef={modalRef}
      >
        {IsModelConnecting ? (
          <>
            <Flex
              align="center"
              justify="center"
              gap="small"
              key="Skeleton"
              vertical
              style={{
                height: '100%',
                textAlign: 'center',
                borderRadius: '12px',
                backgroundColor: '#fafafa',
              }}
            >
              <Lottie
                style={{ height: '50%', width: '50%' }}
                animationData={SettingUP_1}
                color="black"
              />
            </Flex>
          </>
        ) : (
          <>
            <Flex
              justify="space-between"
              style={{
                padding: '8px 16px',
                borderTopRightRadius: '12px',
                borderTopLeftRadius: '12px',
                borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
                background:
                  '-webkit-linear-gradient(207deg, hsla(195, 86%, 50%, 1) 54%, hsla(236, 93%, 65%, 1) 100%)',
              }}
            >
              <Flex
                align="center"
                justify="space-between"
                style={{ width: '100%' }}
              >
                <Flex align="center" gap="8px">
                  <div style={styles.modelTitle}>
                    {getModelIcon()}
                    {model[0]?.toLocaleUpperCase()}
                  </div>
                  <Tooltip
                    title={
                      resizeStage === ResizeStage.FULL_SCREEN
                        ? 'Exit Fullscreen'
                        : 'Fullscreen'
                    }
                  >
                    <Button
                      type="text"
                      icon={
                        resizeStage === ResizeStage.FULL_SCREEN ? (
                          <AiOutlineFullscreenExit size={13} />
                        ) : (
                          <AiOutlineFullscreen size={13} />
                        )
                      }
                      onClick={cycleResize}
                      size="small"
                      className="btn-hover"
                      style={styles.headerButton}
                    />
                  </Tooltip>
                </Flex>

                <Flex align="center" gap="6px">
                  <Dropdown
                    dropdownRender={() => menu}
                    placement="bottomRight"
                    trigger={['click']}
                    open={dropdownOpen}
                    onOpenChange={visible => setDropdownOpen(visible)}
                  >
                    <Button
                      type="text"
                      icon={<CgMoreVertical size={16} />}
                      size="small"
                      onClick={() => setDropdownOpen(!dropdownOpen)}
                      className="btn-hover"
                      style={styles.headerButton}
                    />
                  </Dropdown>
                  <Tooltip title="Close" placement="bottom">
                    <Button
                      type="text"
                      icon={<CloseOutlined />}
                      onClick={handleCancel}
                      size="small"
                      style={styles.headerButton}
                      className="btn-hover"
                    />
                  </Tooltip>
                </Flex>
              </Flex>
            </Flex>
            <main
              style={{
                display: 'flex',
                justifyContent: 'space-evenly',
                // height: 'calc(100% - 49px)',
                height: '93%',
                position: 'relative',
              }}
            >
              <ChatApp
                id={props.id}
                IsModelOpen={isDialogOpen}
                IsDelete={IsDelete}
                setIsDelete={setIsDelete}
                IsDashboard={props.IsDashboard}
                showModelInfo={showModelInfo}
                fontSizeValue={fontSizeValue}
                setSaveChildState={setSaveChildState}
                saveChildState={saveChildState}
              />
            </main>
          </>
        )}
      </Modal>
    </div>
  );
};

export default FloatingButtonWithDialog;
