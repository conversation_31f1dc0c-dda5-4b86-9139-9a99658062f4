import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { css, styled, t } from '@superset-ui/core';
import DeleteComponentButton from 'src/dashboard/components/DeleteComponentButton';
import { Draggable } from 'src/dashboard/components/dnd/DragDroppable';
import HoverMenu from 'src/dashboard/components/menu/HoverMenu';
import ResizableContainer from 'src/dashboard/components/resizable/ResizableContainer';
import WithPopoverMenu from 'src/dashboard/components/menu/WithPopoverMenu';
import { componentShape } from 'src/dashboard/util/propShapes';
import { ROW_TYPE, COLUMN_TYPE } from 'src/dashboard/util/componentTypes';
import {
  GRID_BASE_UNIT,
  GRID_MIN_COLUMN_COUNT,
  GRID_MIN_ROW_UNITS,
} from 'src/dashboard/util/constants';
import { Empty, Flex, Upload } from 'antd-v5';
import { ResizeCallback, ResizeStartCallback } from 're-resizable';
import { UploadProps } from 'antd-v5/lib';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import { updateComponents } from 'src/dashboard/actions/dashboardLayout';
import toast from 'react-hot-toast';

const ImagePreviewStyles = styled.div`
  ${() => css`
    &.dashboard-file-preview {
      overflow: hidden;
      .dashboard-component-chart-holder {
        overflow-y: auto;
        overflow-x: hidden;
      }
      .dashboard--editing & {
        cursor: move;
      }
    }
  `}
`;

type Props = {
  id: string;
  parentId: string;
  component: any;
  parentComponent: any;
  index: number;
  depth: number;
  editMode: boolean;
  handleComponentDrop: ((...args: any[]) => any) | null | undefined;
  deleteComponent: Function;
  availableColumnCount: number;
  columnWidth: number;
  onResize: ResizeCallback | undefined;
  onResizeStart: ResizeStartCallback | undefined;
  onResizeStop: ResizeCallback | undefined;
};

const ImagePreview = ({
  id,
  parentId,
  component,
  parentComponent,
  index,
  depth,
  editMode,
  handleComponentDrop,
  deleteComponent,
  availableColumnCount,
  columnWidth,
  onResize,
  onResizeStart,
  onResizeStop,
}: Props) => {
  const [isFocused, setFocused] = useState(false);
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>();
  const dispatch = useDispatch();

  useEffect(() => {
    if (imageUrl && imageUrl.length) {
      dispatch(
        updateComponents({
          [component.id]: {
            ...component,
            meta: {
              ...component.meta,
              imagePath: imageUrl,
            },
          },
        }),
      );
    }
  }, [imageUrl]);

  const handleChangeFocus = (nextFocus: boolean) => {
    setFocused(!!nextFocus);
  };

  const handleDeleteComponent = () => {
    deleteComponent(id, parentId);
  };

  const handleChange: UploadProps['onChange'] = info => {
    if (info.file.status === 'uploading') {
      setLoading(true);
      return;
    }
    if (info.file.status === 'done') {
      const response = info.file.response;
      const uploadedImageUrl = response?.file_path;

      if (uploadedImageUrl) {
        setLoading(false);
        setImageUrl(uploadedImageUrl);
      } else {
        toast.error('Failed to upload image');
      }
    }
  };

  const widthMultiple =
    parentComponent.type === COLUMN_TYPE
      ? parentComponent.meta.width || GRID_MIN_COLUMN_COUNT
      : component.meta.width || GRID_MIN_COLUMN_COUNT;

  return (
    <>
      <Draggable
        component={component}
        parentComponent={parentComponent}
        orientation={parentComponent.type === ROW_TYPE ? 'column' : 'row'}
        index={index}
        depth={depth}
        onDrop={handleComponentDrop}
        disableDragDrop={isFocused}
        editMode={editMode}
      >
        {({ dragSourceRef }) => (
          <WithPopoverMenu
            onChangeFocus={handleChangeFocus}
            editMode={editMode}
          >
            <ImagePreviewStyles
              className="dashboard-file-preview"
              id={component.id}
            >
              <ResizableContainer
                id={component.id}
                adjustableWidth={parentComponent.type === ROW_TYPE}
                adjustableHeight
                widthStep={columnWidth}
                widthMultiple={widthMultiple}
                heightStep={GRID_BASE_UNIT}
                heightMultiple={component.meta.height}
                minWidthMultiple={GRID_MIN_COLUMN_COUNT}
                minHeightMultiple={GRID_MIN_ROW_UNITS}
                maxWidthMultiple={availableColumnCount + widthMultiple}
                onResizeStart={onResizeStart}
                onResize={onResize}
                onResizeStop={onResizeStop}
                editMode={isFocused ? false : editMode}
              >
                <div
                  ref={dragSourceRef}
                  className="dashboard-component dashboard-component-chart-holder"
                  style={{ padding: '0px' }}
                >
                  {editMode && (
                    <HoverMenu position="top">
                      <DeleteComponentButton onDelete={handleDeleteComponent} />
                    </HoverMenu>
                  )}
                  {editMode && !component?.meta?.imagePath && (
                    <div
                      className="file-upload-container"
                      style={{ height: '97%', width: '100%', display: 'flex' }}
                    >
                      <div style={{ margin: 'auto' }}>
                        <Upload
                          name="image"
                          listType="picture"
                          className="avatar-uploader"
                          showUploadList={false}
                          action={`${process.env.REACT_APP_LLM_URL}agent/image_upload`}
                          onChange={handleChange}
                          accept="image/jpg, image/png , image/gif, image/jpeg"
                          style={{ height: '100%', background: 'transparent' }}
                        >
                          <button
                            style={{ border: 0, background: 'transparent' }}
                            type="button"
                          >
                            {loading ? <LoadingOutlined /> : <PlusOutlined />}
                            <div style={{ marginTop: 8 }}>Upload</div>
                          </button>
                        </Upload>
                      </div>
                    </div>
                  )}
                  {component?.meta?.imagePath ? (
                    <img
                      src={`${process.env.REACT_APP_LLM_URL}agent/preview-image?file_path=${component.meta.imagePath}`}
                      alt="image"
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'contain',
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'center',
                        backgroundSize: 'cover',
                      }}
                    />
                  ) : !editMode ? (
                    <Flex
                      justify="center"
                      align="center"
                      style={{ height: '100%' }}
                    >
                      <Empty
                        description={t('No Image')}
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                      />
                    </Flex>
                  ) : null}
                </div>
              </ResizableContainer>
            </ImagePreviewStyles>
          </WithPopoverMenu>
        )}
      </Draggable>
    </>
  );
};

ImagePreview.propTypes = {
  id: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  component: componentShape.isRequired,
  parentComponent: componentShape.isRequired,
  index: PropTypes.number.isRequired,
  depth: PropTypes.number.isRequired,
  editMode: PropTypes.bool.isRequired,
  deleteComponent: PropTypes.func.isRequired,
  handleComponentDrop: PropTypes.func.isRequired,
  availableColumnCount: PropTypes.number.isRequired,
  columnWidth: PropTypes.number.isRequired,
  onResize: PropTypes.func.isRequired,
  onResizeStart: PropTypes.func.isRequired,
  onResizeStop: PropTypes.func.isRequired,
};

export default React.memo(ImagePreview);
