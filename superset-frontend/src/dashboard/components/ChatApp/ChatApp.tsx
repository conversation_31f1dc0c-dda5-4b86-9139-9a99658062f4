import React, {
  CSSProperties,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import ChatMessage from './ChatMessage';
import Typing from './Typing';
import toast from 'react-hot-toast';
import axios from 'axios';
import {
  <PERSON>Complete,
  Button,
  Flex,
  Skeleton,
  TagProps,
  Tooltip,
} from 'antd-v5';
import {
  ArrowDownOutlined,
  BarChartOutlined,
  BorderlessTableOutlined,
  LineChartOutlined,
  LoadingOutlined,
  PaperClipOutlined,
  PieChartOutlined,
  RadarChartOutlined,
} from '@ant-design/icons';
import { BiDoughnutChart } from 'react-icons/bi';
import FileUploadModel from './FileUploadModel';
import FileTag from './FileTag';
import { IoIosSend } from 'react-icons/io';
import { debounce } from 'lodash';
// @ts-ignore
import EmptyChat from './EmptyChat/empty-chat-2.json';
import Lot<PERSON> from 'lottie-react';
import { getModelProvider, modelHelper } from 'src/utils/modelHelper';

type answerReasoningType = {
  [key: string]: {
    total_time: string;
    start_at: string;
    end_at: string;
    steps: string[];
  };
};

export interface Message {
  id: number;
  content: string;
  role: 'user' | 'bot';
  isAnimating?: boolean;
  datetime?: string;
  model: string;
  like: boolean;
  dislike: boolean;
  file: string[];
  comment: string;
  answerReasoning: answerReasoningType;
}

interface Props {
  id: string;
  IsModelOpen: boolean;
  IsDelete: boolean;
  setIsDelete: React.Dispatch<React.SetStateAction<boolean>>;
  IsDashboard: boolean;
  showModelInfo: boolean;
  fontSizeValue: number;
  setSaveChildState: React.Dispatch<
    React.SetStateAction<{
      isTyping: Record<string, boolean>;
      isLoading: Record<string, boolean>;
      filePath: string[];
    }>
  >;
  saveChildState: {
    isTyping: Record<string, boolean>;
    isLoading: Record<string, boolean>;
    filePath: string[];
  };
}

type ChartDropDownOptions = {
  value:
    | '#Table'
    | '#BarChart'
    | '#LineChart'
    | '#RadarChart'
    | '#PieChart'
    | '#DoughnutChart';
  icon: JSX.Element;
  color: TagProps['color'];
};

const ChatApp = ({
  id,
  IsModelOpen,
  IsDelete,
  setIsDelete,
  IsDashboard,
  showModelInfo,
  fontSizeValue,
  setSaveChildState,
  saveChildState,
}: Props) => {
  const model = modelHelper();
  const [messages, setMessages] = useState<Message[]>([
    {
      content: '',
      id: 0,
      model: model as string,
      role: 'user',
      datetime: '',
      isAnimating: false,
      answerReasoning: {},
      comment: '',
      dislike: false,
      like: false,
      file: [],
    },
  ]);
  const [inputValue, setInputValue] = useState<string>('');
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>(
    saveChildState.isLoading || {},
  );
  const [historyLoading, setHistoryLoading] = useState<boolean>(true);
  const chatWindowRef = useRef<HTMLDivElement>(null);
  const [isTyping, setIsTyping] = useState<Record<string, boolean>>(
    saveChildState.isTyping || {},
  );
  const [isUserScrolled, setIsUserScrolled] = useState<boolean>(false);
  const [isDropdownVisible, setIsDropdownVisible] = useState<boolean>(false);
  const [currentModel, setCurrentModel] = useState({
    model: model,
    modelProvider: 'openai',
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const [fileModelOpen, setFileModelOpen] = useState<boolean>(false);
  const [filePath, setFilePath] = useState<string[]>(
    saveChildState.filePath || [],
  );
  const [questionOptions, setQuestionOptions] = useState<
    { value: string; label: string }[]
  >([]);

  const startLoading = (model: string) => {
    setIsLoading(prev => ({ ...prev, [model]: true }));
    setIsTyping(prev => ({ ...prev, [model]: true }));
    setSaveChildState(prev => ({
      ...prev,
      isLoading: { ...prev.isLoading, [model]: true },
      isTyping: { ...prev.isLoading, [model]: true },
    }));
  };

  const stopLoading = (model: string) => {
    setIsLoading(prev => ({ ...prev, [model]: false }));
    setIsTyping(prev => ({ ...prev, [model]: false }));
    setSaveChildState(prev => ({
      ...prev,
      isLoading: { ...prev.isLoading, [model]: false },
      isTyping: { ...prev.isLoading, [model]: false },
    }));
  };

  useEffect(() => {
    setIsLoading(saveChildState.isLoading || {});
    setIsTyping(saveChildState.isTyping || {});
    if (saveChildState.filePath && saveChildState.filePath !== filePath) {
      setFilePath(saveChildState.filePath);
    }

    const hasLoadingChanged =
      (saveChildState.isLoading &&
        currentModel.model &&
        saveChildState.isLoading[currentModel.model] !==
          isLoading[currentModel.model]) ||
      saveChildState.isTyping[currentModel.model] !==
        isTyping[currentModel.model];

    if (
      messages.length > 0 &&
      messages[messages.length - 1]?.role === 'user' &&
      hasLoadingChanged
    ) {
      fetchHistory();
    }
  }, [saveChildState]);

  useEffect(() => {
    setSaveChildState(prev => ({ ...prev, filePath }));
  }, [filePath]);

  const chartDropDownOptions: ChartDropDownOptions[] = [
    {
      value: '#Table',
      color: 'purple',
      icon: <BorderlessTableOutlined />,
    },
    {
      value: '#BarChart',
      color: 'magenta',
      icon: <BarChartOutlined />,
    },
    {
      value: '#LineChart',
      color: 'blue',
      icon: <LineChartOutlined />,
    },
    {
      value: '#PieChart',
      color: 'volcano',
      icon: <PieChartOutlined />,
    },
    {
      value: '#RadarChart',
      color: 'lime',
      icon: <RadarChartOutlined />,
    },
    {
      value: '#DoughnutChart',
      color: 'green',
      icon: (
        // @ts-ignore
        <BiDoughnutChart
          style={{
            verticalAlign: '-0.125em',
            marginRight: '5px',
            fontSize: '.9rem',
          }}
        />
      ),
    },
  ];

  const styles = {
    container: {
      display: 'flex',
      flexDirection: 'column' as 'column',
      justifyContent: 'center',
      alignItems: 'center',
      // height: breakPoints.md ? '73vh' : '40vh',
      height: '100%',
      backgroundColor: '#ffffff',
      padding: '10px',
      width: '100%',
      scrollBehavior: 'smooth' as 'smooth',
      position: 'relative',
      paddingTop: 0,
    } as React.CSSProperties,
    chatWindow: {
      width: '100%',
      height: '100%',
      backgroundColor: '#fff',
      padding: '10px',
      overflowY: 'auto' as 'auto',
      marginBottom: '10px',
      display: 'flex',
      flexDirection: 'column' as 'column',
      gap: '8px',
      scrollbarWidth: 'none' as 'none',
      scrollBehavior: 'smooth' as 'smooth',
    },
    scrollButton: {
      position: 'absolute',
      right: '20px',
      bottom: '100px',
      zIndex: 1000,
      backgroundColor: '#fff',
      boxShadow: '0 2px 6px rgba(0,0,0,0.15)',
      border: '1px solid white',
    } as CSSProperties,
    inputdiv: {
      height: '58px',
      width: '100%',
      gap: '8px',
      backgroundColor: 'rgb(247, 247, 252)',
      borderRadius: '10px',
      display: 'flex',
      alignItems: 'center',
    },
    message: {
      padding: '10px',
      backgroundColor: '#e0e0e0',
      borderRadius: '5px',
      marginBottom: '10px',
    },
    input: {
      width: '100%',
      outline: 'none',
    },
    button: {
      padding: '5px 14px',
      border: 'none',
      cursor: 'pointer',
      backgroundColor: '#FFF',
      // color: '#FFF',
      borderRadius: '10px',
      marginRight: '10px',
      boxShadow: '#504f4f24 0px 0px 7px 0px',
    },
  };

  const handleSendMessage = async (reccomedationQuations?: string) => {
    try {
      setIsDropdownVisible(false);
      if (inputValue.trim() === '' && !reccomedationQuations) return;

      startLoading(currentModel.model);

      const userMessage: Message = {
        id: Date.now(),
        content: reccomedationQuations ? reccomedationQuations : inputValue,
        role: 'user',
        isAnimating: false,
        model: currentModel.model,
        answerReasoning: {},
        comment: '',
        dislike: false,
        like: false,
        file: [],
      };

      setMessages(prev => [...prev, userMessage]);
      setInputValue('');

      const controller = new AbortController();
      const signal = controller.signal;

      abortControllerRef.current = controller;

      const response = await fetch(
        `${process.env.REACT_APP_LLM_URL}${IsDashboard ? 'reinforced_agent' : 'dataset'}/${IsDashboard ? 'query' : 'infer'}`,
        {
          method: 'POST',
          body: JSON.stringify({
            query: userMessage.content,
            model_name: currentModel.model,
            ...(IsDashboard
              ? { dashboard_id: Number(id) }
              : { dataset_id: Number(id) }),
            model_provider: currentModel.modelProvider,
            file_paths: filePath || [],
          }),
          headers: {
            'Content-Type': 'application/json',
          },
          signal,
        },
      );

      if (!response.body) {
        throw new Error('No response body received from server.');
      }

      const result = await response.json();

      const agentResponse =
        result.data[result.data.length - 1]?.combiner_agent.agent_outputs
          .combiner_agent.response;

      const messageId = await handelSaveResponse(result, currentModel.model);

      setMessages(prevMessages => {
        const updatedMessages = [...prevMessages];
        const lastMessage = updatedMessages[updatedMessages.length - 1];

        if (lastMessage?.role === 'bot') {
          lastMessage.content += agentResponse;
          lastMessage.isAnimating = true;
        } else {
          updatedMessages.push({
            id: messageId,
            content: agentResponse,
            role: 'bot',
            isAnimating: true,
            model: model[1],
            answerReasoning:
              result.data[result.data.length - 1]?.combiner_agent?.verbose ||
              {},
            comment: '',
            dislike: false,
            like: false,
            file: [
              ...result.data[result.data.length - 1]?.combiner_agent
                ?.csv_file_paths,
              ...result.data[result.data.length - 1]?.combiner_agent
                ?.pdf_file_path,
            ],
          });
        }

        return updatedMessages;
      });
    } catch (e: any) {
      console.error(e);
      if (e && e.message && !e?.message?.includes('abort'))
        toast.error(e.message || 'Failed to send message.');
    } finally {
      stopLoading(currentModel.model);
      setIsDropdownVisible(false);
    }
  };

  const handelSaveResponse = async (message: string, model: string) => {
    try {
      const result = await axios.post(
        `${process.env.REACT_APP_LLM_URL}${IsDashboard ? 'dashboard' : 'dataset'}/response_add`,
        {
          ...(IsDashboard ? { dashboard_id: id } : { dataset_id: id }),
          model_name: model,
          response_data: message,
        },
      );

      if (result && result.status === 200) return result.data.message_id;
      setIsDropdownVisible(false);
    } catch (error) {
      console.log(error);
      toast.error('Failed to save response');
    }
  };

  // @ts-ignore
  const handleStopResponse = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      stopLoading(currentModel.model);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault();

      if (event.shiftKey) {
        setInputValue(prev => prev + '\n');
        return;
      }

      const dropdownMenu = document.querySelector('.antd5-select-dropdown');
      if (dropdownMenu) {
        const highlightedItem = dropdownMenu.querySelector(
          '.antd5-select-item-option-active',
        );

        if (highlightedItem && isDropdownVisible) {
          return;
        }
      }

      handleSendMessage();
    }
  };

  const fetchHistory = async (_controller?: AbortController) => {
    try {
      setHistoryLoading(true);

      const model = modelHelper();

      const response = await axios.get(
        `${process.env.REACT_APP_LLM_URL}chat/chat_history?${IsDashboard ? 'dashboard' : 'dataset'}_id=${id}&model_name=${model[1]}`,
      );

      response.data.chat_history.map((x: any) => {
        if (x.role === 'bot') {
          const tmp = JSON.parse(x.content);
          x.like = x.is_liked;
          x.dislike = x.is_disliked;

          x.answerReasoning =
            tmp.data[tmp.data.length - 1]?.combiner_agent?.verbose || {};

          // FILE FLAG
          if (
            tmp.data[tmp.data.length - 1]?.combiner_agent?.csv_file_paths ||
            tmp.data[tmp.data.length - 1]?.combiner_agent?.pdf_file_path
          ) {
            x.file = [
              ...tmp.data[tmp.data.length - 1]?.combiner_agent?.csv_file_paths,
              ...tmp.data[tmp.data.length - 1]?.combiner_agent?.pdf_file_path,
            ];
          }

          return (x.content =
            tmp.data[
              tmp.data.length - 1
            ]?.combiner_agent.agent_outputs.combiner_agent.response);
        } else {
          return x;
        }
      });
      setMessages(response.data.chat_history || []);
      setHistoryLoading(false);
    } catch (err) {
      setMessages([]);
      setHistoryLoading(false);
    }
  };

  const scrollToBottom = () => {
    if (chatWindowRef?.current) {
      requestAnimationFrame(() => {
        if (chatWindowRef.current) {
          chatWindowRef.current.scrollTo({
            top: chatWindowRef.current.scrollHeight,
            behavior: 'instant',
          });
        }
      });
    }
  };

  useEffect(() => {
    const currentModel = modelHelper();
    const Provider = getModelProvider(currentModel[1]);
    setCurrentModel({
      model: currentModel[1],
      modelProvider: Provider as string,
    });
  }, []);

  useEffect(() => {
    const controller = new AbortController();
    fetchHistory(controller);
    return () => controller.abort();
  }, []);

  useEffect(() => {
    scrollToBottom();
    setIsDropdownVisible(false);
    setQuestionOptions([]);
  }, [!IsModelOpen]);

  useEffect(() => {
    if (!isUserScrolled) {
      scrollToBottom();
    }
  }, [messages]);

  const handleUserScroll = () => {
    if (chatWindowRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = chatWindowRef.current;
      setIsUserScrolled(scrollTop + clientHeight < scrollHeight - 50);
    }
  };

  useEffect(() => {
    const clearChat = async () => {
      try {
        setHistoryLoading(true);
        const model = modelHelper();

        const response = await axios.delete(
          `${process.env.REACT_APP_LLM_URL}${IsDashboard ? 'dashboard' : 'dataset'}/clear_chat?${IsDashboard ? 'dashboard' : 'dataset'}_id=${id}&model_name=${model[1]}`,
        );
        if (response.status === 200) {
          toast.success('Chat cleared successfully!');
          setMessages([]);
        }
      } catch (err) {
        toast.error('Failed to clear chat!');
        setIsDelete(false);
      } finally {
        setHistoryLoading(false);
        setIsDelete(false);
      }
    };
    if (IsDelete) clearChat();
  }, [IsDelete]);

  // @ts-ignore
  const handleAutoCompleteChange = (value: string) => {
    setInputValue(value);
    const isVisible =
      value.startsWith('#') &&
      !chartDropDownOptions.some(option => value.includes(option.value));
    setIsDropdownVisible(isVisible);
  };

  const handleRemoveFile = (fileName: string) => {
    setSaveChildState(prev => ({
      ...prev,
      filePath: prev.filePath.filter(file => {
        const name = file?.split('/').pop() || '';
        return name !== fileName;
      }),
    }));
  };

  const fetchSuggestions = useCallback(
    debounce(async (value: any) => {
      try {
        if (value === '') {
          setQuestionOptions([]);
          return;
        }

        const controller = new AbortController();
        const signal = controller.signal;

        const result = await axios.get(
          `${process.env.REACT_APP_LLM_URL}/agent/search_chat?q=${value}&${IsDashboard ? 'dashboard_id' : 'dataset_id'}=${id}`,
          { signal },
        );
        if (result.status === 200 && result.data.results.length > 0) {
          setQuestionOptions(
            result.data.results.map((option: string) => ({
              value: option,
              label: option,
            })),
          );
          setIsDropdownVisible(true);
        } else {
          setIsDropdownVisible(false);
        }
      } catch (error) {
        setIsDropdownVisible(false);
        console.log(error);
      }
    }, 500),
    [id, IsDashboard],
  );

  const handleChange = (value: string) => {
    setInputValue(value);
    fetchSuggestions(value);
  };

  const handleSelect = (value: string) => {
    setInputValue(value);
    setQuestionOptions([]);
    setIsDropdownVisible(false);
  };

  return (
    <>
      <style>{`
      .child-modal-wrapper {
        position: absolute !important; 
        width: 100%;
        height: 100%;
        backdrop-filter: blur(3px);
        overflow:hidden !important;
      }
      .child-modal {
        position: absolute !important;
        top: 35% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        width: 80% !important; 
        max-width: none !important;
      }
      `}</style>
      <div style={styles.container}>
        <FileUploadModel
          fileModelOpen={fileModelOpen}
          setFileModelOpen={setFileModelOpen}
          setFilePath={setFilePath}
        />
        <div
          ref={chatWindowRef}
          style={styles.chatWindow}
          onScroll={handleUserScroll}
        >
          {!historyLoading ? (
            messages.map((message, i) => (
              <ChatMessage
                key={`${i}-message`}
                content={message.content}
                role={message.role}
                id={message.id}
                isAnimating={message.isAnimating}
                date={message.datetime}
                model={currentModel.model as string}
                isTyping={isTyping[currentModel.model]}
                showModelInfo={showModelInfo}
                answerReasoning={message.answerReasoning}
                comment={message.comment}
                dislike={message.dislike}
                like={message.like}
                file={message.file}
                fontSizeValue={fontSizeValue}
              />
            ))
          ) : (
            <Flex vertical={true} gap={'large'} key={'Skeleton'}>
              {[1, 2, 3, 4, 5].map((element, i) => (
                <Flex
                  align="end"
                  key={`${i}-skeleton`}
                  justify={element % 2 === 0 ? 'start' : 'end'}
                >
                  {element % 2 === 0 ? (
                    <Skeleton loading={true} active />
                  ) : (
                    <Skeleton.Button active />
                  )}
                </Flex>
              ))}
            </Flex>
          )}
          {isLoading[currentModel.model] && <Typing />}
          {!historyLoading && messages.length === 0 && (
            <div
              style={{
                height: '100%',
                width: '100%',
                fontSize: '4em',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                color: 'rgb(0 0 0 / 9%)',
              }}
            >
              <Lottie
                style={{ height: '20%', width: '20%' }}
                animationData={EmptyChat}
              />
            </div>
          )}
        </div>
        {isUserScrolled && (
          <Button
            style={styles.scrollButton}
            shape="circle"
            onClick={() => scrollToBottom()}
            icon={<ArrowDownOutlined />}
          />
        )}
        {filePath && filePath?.length > 0 && (
          <div
            style={{
              display: 'flex',
              gap: 3,
              padding: '8px 0px',
              width: '100%',
            }}
          >
            {filePath.map((file, i) => (
              <FileTag
                key={i}
                fileName={file?.split('/').pop() || ''}
                onRemove={handleRemoveFile}
              />
            ))}
          </div>
        )}
        <div style={styles.inputdiv}>
          <AutoComplete
            value={inputValue}
            placement="topLeft"
            variant="borderless"
            style={styles.input}
            onSearch={handleChange}
            open={isDropdownVisible}
            options={questionOptions}
            onKeyDown={handleKeyDown}
            onSelect={handleSelect}
            placeholder={'Ask anything'}
            styles={{
              popup: {
                root: {
                  borderRadius: '8px',
                  backgroundColor: 'white',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                  zIndex: fileModelOpen ? 2999 : undefined,
                },
              },
            }}
            disabled={
              isLoading[currentModel.model] ||
              isTyping[currentModel.model] ||
              historyLoading
            }
          />
          <Flex justify="space-between" align="center" gap={8}>
            <Button
              disabled={
                isLoading[currentModel.model] ||
                isTyping[currentModel.model] ||
                historyLoading
              }
              onClick={() => setFileModelOpen(true)}
              type="text"
              style={{ textAlign: 'center' }}
              icon={
                <PaperClipOutlined
                  style={{
                    color: 'rgb(0 0 0 / 47%)',
                    fontSize: 16,
                    borderRadius: '9px',
                  }}
                />
              }
            />

            <Tooltip title="Send" placement="top">
              <Button
                disabled={
                  isLoading[currentModel.model] ||
                  isTyping[currentModel.model] ||
                  historyLoading
                }
                onClick={() => handleSendMessage()}
                style={styles.button}
              >
                {isLoading[currentModel.model] ||
                isTyping[currentModel.model] ? (
                  <LoadingOutlined color="#000" size={20} spin />
                ) : (
                  // @ts-ignore
                  <IoIosSend color="#000" size={20} />
                )}
              </Button>
            </Tooltip>
          </Flex>
        </div>
      </div>
    </>
  );
};
export default ChatApp;
