import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Card, Typography, Button, Space, Flex } from 'antd-v5';
import {
  ArrowRightOutlined,
  BulbOutlined,
  CloseOutlined,
  DownOutlined,
  UpOutlined,
} from '@ant-design/icons';
import axios from 'axios';
const { Title, Text } = Typography;

type Props = {
  onQuestionSelect: any;
  id: string;
  setShowRecommendationQuations: Dispatch<SetStateAction<boolean>>;
};

const QuestionRecommendation = ({
  onQuestionSelect,
  id,
  setShowRecommendationQuations,
}: Props) => {
  const [recommendationQuestions, setRecommendationQuestions] = useState<
    string[]
  >([]);
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);

  const fetchRecommendationQuestions = async () => {
    try {
      const result = await axios.get(
        `${process.env.REACT_APP_LLM_URL}/chat/initial_question_recommendation?dashboard_id=${id}`,
      );
      if (result && result.status === 200) {
        setRecommendationQuestions(result.data.recommendations);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    fetchRecommendationQuestions();
  }, []);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };
  const header = (
    <Flex align="center" justify="space-between" style={{ width: '100%' }}>
      <Flex align="center">
        <BulbOutlined
          style={{ marginRight: 10, color: '#1890ff', fontSize: '18px' }}
        />
        <Title level={5} style={{ margin: 0 }}>
          Recommended Questions
        </Title>
      </Flex>
      <Space>
        <Button
          type="text"
          icon={isCollapsed ? <DownOutlined /> : <UpOutlined />}
          onClick={toggleCollapse}
          style={{ color: '#1890ff' }}
        />
        <Button
          type="text"
          icon={<CloseOutlined />}
          onClick={() => setShowRecommendationQuations(false)}
          style={{ color: '#ff4d4f' }}
        />
      </Space>
    </Flex>
  );

  return (
    <Card
      style={{
        width: '100%',
        borderRadius: '12px',
        background: 'linear-gradient(145deg, #ffffff, #f0f2f5)',
        boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
        transition: 'all 0.3s ease',
        border: 'none',
      }}
      styles={{
        body: {
          padding: isCollapsed ? 0 : '16px',
          transition: 'all 0.3s ease',
        },
      }}
    >
      <div
        style={{
          padding: '12px 16px',
          cursor: 'pointer',
          borderBottom: isCollapsed ? 'none' : '1px solid rgba(0,0,0,0.06)',
        }}
        onClick={toggleCollapse}
      >
        {header}
      </div>

      {!isCollapsed && (
        <div
          style={{
            overflowY: 'auto',
            padding: '8px 0',
            transition: 'max-height 0.3s ease',
          }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            {recommendationQuestions.length > 0 &&
              recommendationQuestions.map((question, index) => (
                <Button
                  key={index}
                  block
                  type="text"
                  style={{
                    textAlign: 'left',
                    borderRadius: '10px',
                    transition: 'all 0.3s',
                    border: '1px solid rgba(0,0,0,0.06)',
                    overflowX: 'hidden',
                    width: '97%',
                    justifyContent: 'space-between',
                    height: '50px',
                    backgroundColor: '#FFF',
                    padding: '0 16px',
                    margin: '4px 0',
                    boxShadow: '0 2px 6px rgba(0,0,0,0.03)',
                  }}
                  onMouseEnter={e => {
                    e.currentTarget.style.backgroundColor =
                      'rgba(24,144,255,0.08)';
                    e.currentTarget.style.transform = 'translateX(5px)';
                    e.currentTarget.style.boxShadow =
                      '0 4px 8px rgba(24,144,255,0.15)';
                    e.currentTarget.style.borderColor = 'rgba(24,144,255,0.3)';
                  }}
                  onMouseLeave={e => {
                    e.currentTarget.style.backgroundColor = 'white';
                    e.currentTarget.style.transform = 'translateX(0)';
                    e.currentTarget.style.boxShadow =
                      '0 2px 6px rgba(0,0,0,0.03)';
                    e.currentTarget.style.borderColor = 'rgba(0,0,0,0.06)';
                  }}
                  onClick={() => onQuestionSelect(question)}
                  icon={<ArrowRightOutlined style={{ color: '#1890ff' }} />}
                  iconPosition="end"
                >
                  <Text
                    ellipsis={{ tooltip: question }}
                    style={{
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      color: 'rgba(0,0,0,0.65)',
                      maxWidth: 'calc(100% - 24px)', // Leave space for icon
                    }}
                  >
                    {question}
                  </Text>
                </Button>
              ))}
          </Space>
        </div>
      )}
    </Card>
  );
};

export default QuestionRecommendation;
