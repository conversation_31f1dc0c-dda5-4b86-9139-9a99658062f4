import {
  Modal,
  Upload,
  Input,
  Button,
  Space,
  Typography,
  UploadProps,
  UploadFile,
} from 'antd-v5';
import { InboxOutlined } from '@ant-design/icons';
import React, {
  Dispatch,
  SetStateAction,
  useState,
  useEffect,
  useRef,
} from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';
import { useParams } from 'react-router-dom';

type Props = {
  fileModelOpen: boolean;
  setFileModelOpen: Dispatch<SetStateAction<boolean>>;
  setFilePath: Dispatch<SetStateAction<string[]>>;
  setDescriptionMetaData?: Dispatch<SetStateAction<{}>>;
  isDashboard?: boolean;
};

const FileUploadModel: React.FC<Props> = ({
  fileModelOpen,
  setFileModelOpen,
  setFilePath,
  isDashboard = false,
  setDescriptionMetaData,
}) => {
  const [fileList, setFileList] = useState<UploadFile<any>[]>([]);
  const [description, setDescription] = useState<Record<string, string>>({});
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { idOrSlug }: { idOrSlug?: string } = useParams();
  const isMountedRef = useRef(true);

  const { Dragger } = Upload;
  const { Title, Text } = Typography;

  // Set up cleanup function to prevent state updates after unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const mimeToExtension = {
    'application/pdf': 'PDF',
    'text/csv': 'CSV',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      'DOCX',
    'application/vnd.oasis.opendocument.text': 'ODT',
    'application/rtf': 'RTF',
    'text/plain': 'TXT',
    'application/epub+zip': 'EPUB',
    'text/markdown': 'MD',
  };

  const handleCancel = () => {
    if (isMountedRef.current) {
      setFileModelOpen(false);
      setFileList([]);
      setDescription({});
    }
  };

  const handleSubmit = async () => {
    try {
      if (!isMountedRef.current) return;

      setIsSubmitted(true);

      const hasEmptyDescription = fileList.some(file => {
        const desc = description[file.name];
        return !desc || desc.trim() === '';
      });

      if (hasEmptyDescription) {
        return;
      }

      setIsLoading(true);

      const formData = new FormData();

      fileList.forEach(
        file =>
          file.originFileObj && formData.append('files', file.originFileObj),
      );

      if (isDashboard && idOrSlug) {
        formData.append('upload_type', 'dashboard');
        formData.append('dashboard_id', idOrSlug);
      } else {
        formData.append('upload_type', 'chat');
      }

      formData.append('description', JSON.stringify(description));

      const result = await axios.post(
        `${process.env.REACT_APP_LLM_URL}agent/file_upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );

      if (result && result.status === 200) {
        const paths = result.data.uploaded_files.map((x: any) => x.file_path);

        // Update state only if component is still mounted
        if (isMountedRef.current) {
          setFilePath(paths);
          if (setDescriptionMetaData) {
            setDescriptionMetaData(description);
          }
          toast.success('File Uploaded Successfully!');
          handleCancel();
        }
      }

      if (isMountedRef.current) {
        setIsLoading(false);
      }
    } catch (error: any) {
      if (isMountedRef.current) {
        setIsLoading(false);
        toast.error(`${error?.message || 'Error While Upload Message!'}`);
      }
    }
  };

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: !isDashboard,
    fileList,
    accept: isDashboard ? '.pdf,.csv,.docx,.txt,.epub,.md' : '.pdf,.csv',
    maxCount: isDashboard ? 1 : 2,
    customRequest: handleSubmit,
    beforeUpload: (file, fileList) => {
      const isLt15M = file.size / 1024 / 1024 < 15;
      if (!isLt15M) {
        toast.error('File must be smaller than 15MB!');
        return Upload.LIST_IGNORE;
      }

      if (!isDashboard) {
        const pdfCount = fileList.filter(
          f => f.type === 'application/pdf',
        ).length;

        const csvCount = fileList.filter(f => f.type === 'text/csv').length;

        if (pdfCount > 1 || csvCount > 1) {
          toast.error('You can only upload one PDF and one CSV file!');
          return Upload.LIST_IGNORE;
        }
      }

      return false;
    },
    onChange: ({ fileList }) => {
      if (isMountedRef.current) {
        fileList.forEach(f =>
          setDescription(prev => ({ ...prev, [f.name]: '' })),
        );
        setFileList(fileList);
      }
    },
    onRemove: file => {
      if (isMountedRef.current) {
        setFileList(prev => prev.filter(f => f !== file));
        setDescription(prevDescriptions => {
          delete prevDescriptions[file.name];
          return prevDescriptions;
        });
      }
    },
  };

  const getFileTypeDisplay = (file: UploadFile) => {
    return (
      mimeToExtension[file.type as keyof typeof mimeToExtension] || file.type
    );
  };

  return (
    <>
      <style>{`
        .child-modal-Dashboard {
          position: fixed !important;
          top: 50% !important;
          left: 50% !important;
          transform: translate(-50%, -50%) !important;
          width: 80% !important; 
          max-width: none !important;
        }
        .child-modal-wrapper-Dashboard {
          position: absolute !important; 
          width: 100%;
          height: 100%;
          overflow:hidden !important;
      }
    `}</style>
      <Modal
        className={isDashboard ? 'child-modal-Dashboard' : 'child-modal'}
        title="Upload File"
        open={fileModelOpen}
        onOk={handleSubmit}
        onCancel={handleCancel}
        getContainer={isDashboard ? document.body : false}
        wrapClassName={
          isDashboard ? 'child-modal-wrapper-Dashboard' : 'child-modal-wrapper'
        }
        styles={{
          header: { backgroundColor: 'white' },
          content: { backgroundColor: 'white' },
          mask: {
            backgroundColor: '#0000006e',
          },
        }}
        closable={false}
        mask={isDashboard}
        centered={isDashboard}
        footer={[
          <Button disabled={isLoading} key="cancel" onClick={handleCancel}>
            Cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleSubmit}
            disabled={fileList.length === 0}
            loading={isLoading}
          >
            Upload
          </Button>,
        ]}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={5}>Select File</Title>
            <Dragger {...uploadProps}>
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">
                Click or drag file to this area to upload
              </p>
              <p className="ant-upload-hint">
                {isDashboard ? (
                  <span>
                    You can upload{' '}
                    <span style={{ fontWeight: 'bold' }}>one file</span> of the
                    following types:
                    <span style={{ fontWeight: 'bold' }}>
                      {' '}
                      PDF, CSV, DOCX, TXT, EPUB, MD
                    </span>
                  </span>
                ) : (
                  <span>
                    You can upload{' '}
                    <span style={{ fontWeight: 'bold' }}>
                      one PDF and one CSV file only
                    </span>
                  </span>
                )}
              </p>
            </Dragger>
          </div>

          <div>
            {fileList.length ? <Title level={5}>File Description</Title> : null}
            {fileList.map((file, index) => (
              <div key={index}>
                <Input
                  placeholder={`Enter a description for your ${getFileTypeDisplay(file)} file`}
                  style={{
                    backgroundColor: '#FFF',
                    margin: 5,
                    padding: 7,
                    marginLeft: 0,
                  }}
                  value={description[file.name]}
                  onChange={e =>
                    setDescription(prev => ({
                      ...prev,
                      [file.name]: e.target.value,
                    }))
                  }
                />
                {isSubmitted && description[file.name]?.trim() === '' && (
                  <Text style={{ color: 'red', fontSize: 12 }}>
                    Please Enter Description
                  </Text>
                )}
              </div>
            ))}
          </div>

          {fileList.length > 0 && (
            <div>
              {fileList.map((file, index) => (
                <Text
                  key={index}
                  type="success"
                  ellipsis
                  style={{ display: 'block' }}
                >
                  Selected file: {file.name}
                </Text>
              ))}
            </div>
          )}
        </Space>
      </Modal>
    </>
  );
};

export default React.memo(FileUploadModel);
