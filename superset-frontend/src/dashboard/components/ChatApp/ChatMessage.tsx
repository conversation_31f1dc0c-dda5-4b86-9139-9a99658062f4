import React, { useState, useMemo, useCallback } from 'react';
import ReactMarkdown from 'react-markdown';
import { Table } from 'antd';
import { Bar, Doughnut, Line, Pie, PolarArea, Radar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Legend,
  ChartOptions,
  ChartData,
  ArcElement,
  RadialLinearScale,
  PointElement,
  LineElement,
} from 'chart.js';
import { Spin, Tag, TagProps } from 'antd-v5';
import {
  BarChartOutlined,
  BorderlessTableOutlined,
  LineChartOutlined,
  PieChartOutlined,
  RadarChartOutlined,
} from '@ant-design/icons';
import { isJsonString } from '@superset-ui/core';
import { BiDoughnutChart } from 'react-icons/bi';
import { ColumnType } from 'antd/lib/table';
import axios from 'axios';
import FileTag from './FileTag';
import MessageOptions from './MessageOptions';
import AnswerReasoningCard from './AnswerReasoningCard';
import remarkGfm from 'remark-gfm';

ChartJS.defaults.responsive = true;

ChartJS.register(
  RadialLinearScale,
  CategoryScale,
  PointElement,
  LinearScale,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Legend,
);

interface ChatMessageProps {
  id: number;
  content: string;
  role: 'user' | 'bot';
  isTyping?: boolean;
  isAnimating?: boolean;
  date?: string;
  model: string;
  showModelInfo: boolean;
  like: boolean;
  dislike: boolean;
  comment: string;
  answerReasoning: {
    [key: string]: {
      total_time: string;
      start_at: string;
      end_at: string;
      steps: string[];
    };
  };
  file: string[];
  fontSizeValue: number;
}

type ChartTags = {
  value: string;
  color: TagProps['color'];
  icon: JSX.Element;
};

interface TableDataItem {
  key: number;
  [key: string]: string | number;
}

const defaultChartOptions: ChartOptions<any> = {
  responsive: true,
  maintainAspectRatio: false,
  animation: false,
  scales: {
    // y: {
    //   beginAtZero: true,
    //   ticks: {
    //     callback: (value: any) =>
    //       typeof value === 'number' ? value.toLocaleString() : value,
    //   },
    // },
  },
  plugins: {
    legend: {
      display: true,
    },
    zoom: true,
    tooltip: {
      callbacks: {
        label: (context: any) =>
          `${context.dataset.label}: ${context.raw.toLocaleString()}`,
      },
    },
  },
};

const ChatMessage: React.FC<ChatMessageProps> = ({
  content,
  role,
  isTyping = false,
  isAnimating,
  showModelInfo,
  id,
  answerReasoning,
  comment,
  dislike,
  like,
  file,
  fontSizeValue,
}) => {
  const [, setRenderedContent] = useState<React.ReactNode>(null);
  const [contentType, setContentType] = useState<'text' | 'table' | 'chart'>(
    'text',
  );
  const [userReaction, setUserReaction] = useState<{
    like: boolean;
    dislike: boolean;
  }>({
    like: like || false,
    dislike: dislike || false,
  });
  const [messagePopOpen, setMessagePopOpen] = useState<boolean>(false);
  const [commentMessage, setCommentMessage] = useState<string>(comment || '');
  const [commentLoading, setCommentLoading] = useState<boolean>(false);
  const fileNames = file?.map(f => f?.split('/').pop() || '');

  const handleUserReaction = async (likes: {
    like: boolean;
    dislike: boolean;
  }) => {
    try {
      await axios.post(
        `${process.env.REACT_APP_LLM_URL}/chat/update_reaction`,
        { id, ...likes },
      );
    } catch (error) {
      console.log(error);
    }
  };

  const handleReaction = async (reaction: 'like' | 'dislike') => {
    const newReaction = {
      like: reaction === 'like' ? !userReaction.like : false,
      dislike: reaction === 'dislike' ? !userReaction.dislike : false,
    };
    if (!commentMessage.trim()) setMessagePopOpen(true);
    setUserReaction(newReaction);
  };

  const handleUserComment = async (reactions?: {
    like: boolean;
    dislike: boolean;
  }) => {
    try {
      if (commentMessage.trim() == '') return;
      setCommentLoading(true);
      const result = await axios.post(
        `${process.env.REACT_APP_LLM_URL}/chat/update_comment`,
        {
          id,
          comment: commentMessage,
        },
      );
      await axios.post(`${process.env.REACT_APP_LLM_URL}/reinforced_agent/feedback`,
        { message_id: id, ...reactions, comment: commentMessage },
      );
      if (result.status === 200 && reactions) {
        setUserReaction(reactions);
        handleUserReaction(reactions);
      }
      result.status !== 200 && setUserReaction({ like: false, dislike: false });
      setCommentLoading(false);
      setMessagePopOpen(false);
    } catch (error) {
      setCommentLoading(false);
    }
  };

  const chartTags: ChartTags[] = [
    {
      value: '#Table',
      color: 'purple',
      icon: <BorderlessTableOutlined />,
    },
    {
      value: '#BarChart',
      color: 'magenta',
      icon: <BarChartOutlined />,
    },
    {
      value: '#LineChart',
      color: 'blue',
      icon: <LineChartOutlined />,
    },
    {
      value: '#PieChart',
      color: 'volcano',
      icon: <PieChartOutlined />,
    },
    {
      value: '#RadarChart',
      color: 'lime',
      icon: <RadarChartOutlined />,
    },
    {
      value: '#DoughnutChart',
      color: 'green',
      icon: (
        <BiDoughnutChart
          style={{
            verticalAlign: '-0.125em',
            marginRight: '5px',
            fontSize: '.9rem',
          }}
        />
      ),
    },
  ];

  const containerStyle: React.CSSProperties = useMemo(
    () => ({
      display: 'flex',
      justifyContent: role === 'user' ? 'flex-end' : 'flex-start',
      margin: '5px 0',
    }),
    [role],
  );

  const components = useMemo(
    () => ({
      table: ({ children }: { children: React.ReactNode }) => (
        <table
          style={{
            width: '100%',
            borderCollapse: 'collapse',
            marginBottom: '16px',
            border: '1px solid #ddd',
          }}
        >
          {children}
        </table>
      ),
      th: ({ children }: { children: React.ReactNode }) => (
        <th
          style={{
            padding: '8px',
            border: '1px solid #ddd',
            backgroundColor: 'rgb(187 240 253 / 76%)',
            textAlign: 'left',
          }}
        >
          {children}
        </th>
      ),
      td: ({ children }: { children: React.ReactNode }) => (
        <td
          style={{
            padding: '8px',
            border: '1px solid #ddd',
            textAlign: 'left',
          }}
        >
          {children}
        </td>
      ),
    }),
    [],
  );

  const messageStyle = useMemo(
    () => ({
      maxWidth: '97%',
      backgroundColor:
        role === 'user' ? 'rgb(231, 241, 253)' : 'rgb(247, 247, 252)',
      padding: '15px',
      // paddingBottom: 0,
      // borderRadius: role === 'user' ? '15px 0 15px 15px' : '0 15px 15px 15px',
      borderRadius: role === 'user' ? '15px 0 15px 15px' : '0 15px 15px 15px',
      wordBreak: 'break-word' as const,
      wordSpacing: '2px',
      position: 'relative' as const,
      textAlign: 'justify' as const,
      fontFamaliy: 'Popins',
      fontWeight: 400,
    }),
    [role],
  );

  const tableStyle = useMemo(
    () => ({
      borderRadius: '10px',
      overflow: 'hidden',
      marginTop: '10px',
      backgroundColor: 'transparent !important',
      boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.1)',
      border: '1px solid rgba(240, 240, 240, 0) !important',
    }),
    [],
  );

  const parseTableData = useCallback(
    (
      content: string,
    ): { columns: ColumnType<any>[]; data: TableDataItem[] } | undefined => {
      try {
        if (!isJsonString(content)) return undefined;

        const tableData = JSON.parse(content);

        if (
          !Array.isArray(tableData.columns) ||
          !Array.isArray(tableData.data)
        ) {
          return undefined;
        }

        return {
          columns: tableData.columns,
          data: tableData.data,
        };
      } catch (error) {
        console.error('Error parsing table data:', error);
        return { columns: [], data: [] };
      }
    },
    [],
  );

  const parseChartData = useCallback(
    (
      content: string,
    ): ChartData<'bar' | 'line' | 'doughnut' | 'pie' | 'radar'> | null => {
      try {
        if (!isJsonString(content)) return null;

        const { labels, datasets, ...rest } = JSON.parse(content);

        if (!Array.isArray(labels) || !Array.isArray(datasets)) return null;

        return {
          labels,
          ...rest,
          datasets: datasets.map((x: any) => ({
            ...x,
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 1,
          })),
        };
      } catch (error) {
        console.error('Error parsing chart data:', error);
        return null;
      }
    },
    [],
  );

  // @ts-ignore
  const renderContent = useCallback(async () => {
    if (!content) return;

    const isChart =
      role === 'bot' &&
      content.includes('labels') &&
      content.includes('datasets');

    const isTable =
      role === 'bot' && content.includes('columns') && content.includes('data');

    setContentType(isChart ? 'chart' : isTable ? 'table' : 'text');

    try {
      if (isChart) {
        const chartData = parseChartData(content);
        if (chartData) {
          const chartTypes = {
            bar: React.memo(Bar),
            line: React.memo(Line),
            pie: React.memo(Pie),
            doughnut: React.memo(Doughnut),
            radar: React.memo(Radar),
            polarArea: React.memo(PolarArea),
          };

          // @ts-ignore
          const ChartComponent = chartTypes[chartData.chartType || 'bar'];

          setRenderedContent(
            <div
              style={{ minHeight: '300px', minWidth: '300px', width: '100%' }}
            >
              <ChartComponent data={chartData} options={defaultChartOptions} />
            </div>,
          );
        } else if (isTyping) {
          setRenderedContent(
            <span style={{ display: 'flex', gap: '10px' }}>
              <p> Crafting your visualization</p>
              <Spin />
            </span>,
          );
        } else {
          setRenderedContent(<p>Error: Invalid chart data</p>);
        }
      } else if (isTable) {
        const tableData = parseTableData(content);
        if (tableData) {
          const { columns, data } = tableData;
          setRenderedContent(
            <Table
              columns={columns}
              dataSource={data}
              rowKey={() => Math.random() + new Date().getTime()}
              size="middle"
              bordered
              style={tableStyle}
              pagination={
                data.length < 6
                  ? false
                  : {
                    pageSize: 5,
                    style: { margin: '0.4px 0px', background: '#45bed642' },
                  }
              }
              components={{
                header: {
                  cell: (props: any) => (
                    <th
                      {...props}
                      style={{ backgroundColor: 'rgb(187 240 253 / 76%)' }}
                    />
                  ),
                },
                body: {
                  cell: (props: any) => (
                    <td
                      {...props}
                      style={{ backgroundColor: 'rgb(187 240 253 / 76%)' }}
                    />
                  ),
                },
                table: (props: any) => (
                  <table
                    {...props}
                    style={{
                      width: '100%',
                      backgroundColor: 'rgb(187 240 253 / 76%)',
                    }}
                  />
                ),
              }}
            />,
          );
        } else if (isTyping) {
          setRenderedContent(
            <span style={{ display: 'flex', gap: '10px' }}>
              <p> Crafting your visualization</p>
              <Spin />
            </span>,
          );
        } else {
          setRenderedContent(<p>Error: Invalid table data</p>);
        }
      } else {
        const matchedTag = chartTags.find(tag => content.includes(tag.value));

        setRenderedContent(
          <>
            {matchedTag ? (
              <>
                <Tag
                  icon={matchedTag.icon}
                  style={{ marginBottom: '10px' }}
                  color={matchedTag.color}
                >
                  {matchedTag.value.replace('#', '')}
                </Tag>
                <ReactMarkdown
                  components={{
                    p: ({ children }) => <span>{children}</span>,
                  }}
                >
                  {content.replace(matchedTag.value, '')}
                </ReactMarkdown>
              </>
            ) : !isAnimating ? (
              <ReactMarkdown>{content}</ReactMarkdown>
            ) : (
              // <TypeAnimation cursor={false} sequence={[content]} speed={20} />
              <ReactMarkdown>{content}</ReactMarkdown>
            )}
          </>,
        );
      }
    } catch (error) {
      console.error('Error rendering content:', error);
      setRenderedContent(<p>Error rendering content.</p>);
    }
  }, [content, role, parseChartData, parseTableData, tableStyle]);

  // useEffect(() => {
  //   renderContent();
  // }, [renderContent]);

  const handleMessagePopClose = () => {
    setMessagePopOpen(false);
    if (comment.trim() !== '') setCommentMessage(comment);
    if (comment == '' && commentMessage.trim() == '') {
      setUserReaction({ like: false, dislike: false });
    }
  };

  return (
    <>
      <style>{`
      .message-options {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
      }
      .chat-message-container:hover .message-options {
        opacity: 1;
      }
      .file-tag-hover:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }
   `}</style>
      <div
        onMouseLeave={handleMessagePopClose}
        className="chat-message-container"
      >
        <div style={{ ...containerStyle, fontSize: fontSizeValue }}>
          <div style={isTyping || contentType !== 'table' ? messageStyle : {}}>
            {role === 'bot' &&
              fileNames &&
              fileNames.length > 0 &&
              fileNames?.map(fn => (
                <FileTag key={fn} fileName={fn} onRemove={undefined} />
              ))}
            {role === 'bot' ? (
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={components}
              >
                {content}
              </ReactMarkdown>
            ) : (
              <span>{content}</span>
            )}
            {showModelInfo && (
              <AnswerReasoningCard
                answerReasoning={answerReasoning}
                fontSizeValue={fontSizeValue}
                id={id}
              />
            )}
          </div>
        </div>
        {role === 'bot' && (
          <MessageOptions
            userReaction={userReaction}
            handleReaction={handleReaction}
            commentMessage={commentMessage}
            setCommentMessage={setCommentMessage}
            messagePopOpen={messagePopOpen}
            setMessagePopOpen={setMessagePopOpen}
            handleMessagePopClose={handleMessagePopClose}
            handleUserComment={handleUserComment}
            commentLoading={commentLoading}
          />
        )}
      </div>
    </>
  );
};

export default React.memo(ChatMessage);
