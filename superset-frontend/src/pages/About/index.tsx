import React from 'react';

interface Styles {
  container: React.CSSProperties;
  card: React.CSSProperties;
  logoContainer: React.CSSProperties;
  logo: React.CSSProperties;
  title: React.CSSProperties;
  versionContainer: React.CSSProperties;
  versionText: React.CSSProperties;
  description: React.CSSProperties;
  licenseText: React.CSSProperties;
  copyright: React.CSSProperties;
}

const styles: Styles = {
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '80vh',
    // backgroundColor: 'white',
    padding: '2rem',
  },
  card: {
    maxWidth: '600px',
    width: '100%',
    border: '1px solid #e5e5e5',
    padding: '2rem',
    borderRadius: '8px',
  },
  logoContainer: {
    display: 'flex',
    justifyContent: 'center',
    marginBottom: '2rem',
  },
  logo: {
    width: '128px',
    height: '128px',
  },
  title: {
    textAlign: 'center',
    fontSize: '24px',
    fontWeight: 'bold',
    marginBottom: '1rem',
    color: '#000',
  },
  versionContainer: {
    textAlign: 'center',
    marginBottom: '2rem',
  },
  versionText: {
    fontWeight: '600',
    margin: '0.25rem 0',
  },
  description: {
    textAlign: 'center',
    marginBottom: '2rem',
    color: '#333',
    lineHeight: '1.6',
  },
  licenseText: {
    textAlign: 'center',
    marginBottom: '2rem',
    color: '#333',
    lineHeight: '1.6',
  },
  copyright: {
    textAlign: 'center',
    color: '#666',
  },
};

// interface LogoPathProps {
//   d: string;
//   fill: string;
// }

// const LogoPath: React.FC<LogoPathProps> = ({ d, fill }) => (
//   <path d={d} fill={fill} />
// );

interface DDXInfoPageProps {
  version?: string;
  date?: string;
}

const DDXInfoPage: React.FC<DDXInfoPageProps> = ({
  version = '1.3.0 (Universal)',
  date = '30 June 2025',
}) => {
  // const logoData: LogoPathProps[] = [
  //   { d: 'M30 30 L50 30 L50 50 L30 50 Z', fill: '#00A651' }, // Green
  //   { d: 'M50 30 L70 30 L70 50 L50 50 Z', fill: '#ED1C24' }, // Red
  //   { d: 'M30 50 L50 50 L50 70 L30 70 Z', fill: '#662D91' }, // Purple
  //   { d: 'M50 50 L70 50 L70 70 L50 70 Z', fill: '#00A9E0' }, // Blue
  // ];

  return (
    <div style={styles.container}>
      <div style={styles.card}>
        {/* Logo SVG */}
        <div style={styles.logoContainer}>
          <img
            src="/static/assets/images/superset-logo-horiz copy.png"
            alt="DDX"
            style={{ height: '100px', width: '100px' }}
          />
        </div>

        {/* Title */}
        <h1 style={styles.title}>DDX Unified Intelligence (DDX UI™)</h1>

        {/* Version and Date */}
        <div style={styles.versionContainer}>
          <p style={styles.versionText}>Version: {version}</p>
          <p style={styles.versionText}>Date: {date}</p>
        </div>

        {/* Description */}
        <p style={styles.description}>
          DDX UI™ is built on an enhanced and secure foundation of Apache
          Superset, integrating cutting-edge Generative AI and Open Source Large
          Language Model (LLM) technologies from a diverse ecosystem, including
          BigScience Initiative MistralAI, Meta, OpenAI, Anthropic, Qdrant and
          other leading contributors to AI research.
        </p>
        <p>
          This version introduces an advanced implementation of
          Retrieval-Augmented Generation (RAG) using LangChain and Managed
          Inference, extending the system’s capabilities beyond standard LLMs to
          provide more contextual, efficient, and adaptive intelligence.
        </p>

        {/* License Info */}
        <p style={styles.licenseText}>
          We acknowledge and respect the intellectual contributions of all
          products and brand names referenced, as well as the rights of their
          respective license holders. The majority of these technologies are
          governed by open-source licenses, including but not limited to the
          PostgreSQL License, The Apache License (Version 2.0), and the MIT
          License, ensuring compliance with their respective terms.
        </p>

        {/* Copyright */}
        <p style={styles.copyright}>© 2023-2025</p>
      </div>
    </div>
  );
};

export default DDXInfoPage;
