import { message, Row, Col } from 'antd-v5';
import axios from 'axios';
import { useState, useMemo, useCallback, useEffect } from 'react';
import { ApiConfigurationPanel } from './components/ApiConfigForm/ApiConfigForm';
import ResponseAnalysisPanel from './components/ResponseAnalysisPanel/ResponseAnalysisPanel';
import useSchemaExtraction from './hooks/useSchemaExtraction';
import { ApiConfig, TreeNode } from './types';
import { getAllLeafKeys } from './utils';
import SubMenu from 'src/features/home/<USER>';
import { t } from '@superset-ui/core';
import DatabaseConfigModal from './components/DatabaseConfigModal/DatabaseConfigModal';

const EtlTools: React.FC = () => {
  const [apiConfig, setApiConfig] = useState<ApiConfig>({
    pipelineName: '',
    curlInput: '',
    apiKey: '',
    headers: '{"Content-Type": "application/json"}',
  });

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [apiResponse, setApiResponse] = useState<any>(null);
  const [schemaTree, setSchemaTree] = useState<TreeNode[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<string>('schema');
  const [showDatabaseModal, setShowDatabaseModal] = useState<boolean>(false);
  const [pipelineId, setPipelineId] = useState<number>(0);
  const [isReset, setIsReset] = useState<boolean>(false);
  const [dbConfig, setDbConfig] = useState<{ database: string; table: string }>(
    {
      database: '',
      table: '',
    },
  );
  const params = new URLSearchParams(window.location.search);
  const EditpipelineId = params.get('pipelineId');
  const [dbs, setDbs] = useState<string[]>([]);

  const fetchDbs = useCallback(async () => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_LLM_URL}/pipeline/get_dbs`,
      );
      if (response && response.status === 200) {
        setDbs(response.data.dbs || []);
        setDbConfig({
          database: response.data.dbs[0],
          table: '',
        });
      }
    } catch (error) {
      console.log(error);
    }
  }, []);

  useEffect(() => {
    fetchDbs();
  }, [fetchDbs]);

  const { extractSchemaFromData, buildTreeData } = useSchemaExtraction();

  const isDisabled = useMemo(
    () => !apiConfig.pipelineName || !apiConfig.curlInput || !!EditpipelineId,
    [apiConfig, EditpipelineId],
  );

  useEffect(() => {
    if (isReset) {
      setApiConfig({
        pipelineName: '',
        curlInput: '',
        apiKey: '',
        headers: '{"Content-Type": "application/json"}',
      });
      setApiResponse(null);
      setSchemaTree([]);
      setSelectedKeys([]);
      setExpandedKeys([]);
      setActiveTab('schema');
      setPipelineId(0);
      setIsReset(false);
    }
  }, [isReset]);

  const processSchemaData = useCallback(
    (data: any) => {
      try {
        const schema = extractSchemaFromData(data);
        const treeData = buildTreeData(schema);
        setSchemaTree(treeData);

        const firstLevelKeys = treeData.map(node => node.key);
        setExpandedKeys(firstLevelKeys);

        const allLeafKeys = getAllLeafKeys(treeData);
        setSelectedKeys(allLeafKeys);
      } catch (error) {
        console.error('Error processing schema:', error);
        message.error('Failed to process schema data');
        setSchemaTree([]);
        setSelectedKeys([]);
      }
    },
    [extractSchemaFromData, buildTreeData],
  );

  useEffect(() => {
    if (apiResponse) {
      processSchemaData(apiResponse);
    }
  }, [apiResponse, processSchemaData]);

  const handleFetch = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    try {
      const payload = {
        pipeline_name: apiConfig.pipelineName,
        api_curl: apiConfig.curlInput,
        api_key: apiConfig.apiKey,
        headers: apiConfig.headers
          ? JSON.parse(apiConfig.headers)
          : {
              'Content-Type': 'application/json',
            },
      };

      const response = await axios.post(
        `${process.env.REACT_APP_LLM_URL}/pipeline/create_and_fetching`,
        payload,
      );

      if (response.status === 200) {
        setApiResponse(response.data.sample_data);
        setPipelineId(response.data.pipeline_id);
        message.success('Data fetched successfully');
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      message.error(
        error?.response?.data?.error?.includes('duplicate')
          ? 'Pipeline name already exists'
          : 'Failed to fetch data',
      );
    } finally {
      setIsLoading(false);
    }
  }, [apiConfig]);

  const fetchEditPipeline = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_LLM_URL}pipeline/get_pipeline/${EditpipelineId}`,
      );

      if (response.status === 200) {
        setApiConfig({
          pipelineName: response.data.pipeline.pipeline_name,
          curlInput: response.data.pipeline.api_curl,
          apiKey: response.data.pipeline.api_key,
          headers: JSON.stringify(response.data.pipeline.headers),
        });
        setApiResponse(response.data.pipeline.fetch_response);
        setPipelineId(response.data.pipeline.pipeline_id);
        setDbConfig({
          database: response.data.pipeline.db,
          table: response.data.pipeline.table_name,
        });
        setSelectedKeys(
          response.data.pipeline.updated_schema?.selected_fields || [],
        );
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      message.error('Failed to fetch data');
    } finally {
      setIsLoading(false);
    }
  }, [EditpipelineId]);

  useEffect(() => {
    if (EditpipelineId) {
      fetchEditPipeline();
    }
  }, [EditpipelineId, fetchEditPipeline]);

  const handleConfigChange = useCallback(
    (field: keyof ApiConfig, value: string): void => {
      setApiConfig(prev => ({ ...prev, [field]: value }));
    },
    [],
  );

  const handleTreeCheck = useCallback(
    (
      checkedKeys:
        | React.Key[]
        | { checked: React.Key[]; halfChecked: React.Key[] },
    ): void => {
      const keys = Array.isArray(checkedKeys)
        ? checkedKeys
        : checkedKeys.checked;
      setSelectedKeys(keys.map(key => String(key).trim()));
    },
    [],
  );

  const handleExpand = useCallback((keys: React.Key[]): void => {
    setExpandedKeys(keys.map(key => String(key)));
  }, []);

  const handleContinue = () => {
    setShowDatabaseModal(true);
  };

  const handleDatabaseCancel = useCallback((): void => {
    setShowDatabaseModal(false);
  }, []);

  return (
    <>
      <style>{`
        .ant-message { z-index: 3001 !important; }
    `}</style>
      <SubMenu name={t('ETL Tools (data streaming pipeline)')} />
      <DatabaseConfigModal
        visible={showDatabaseModal}
        selectedFieldsCount={selectedKeys.length}
        onCancel={handleDatabaseCancel}
        pipelineId={pipelineId}
        selectedKeys={selectedKeys}
        setShowDatabaseModal={setShowDatabaseModal}
        setIsReset={setIsReset}
        dbConfig={dbConfig}
        dbs={dbs}
      />
      <div style={{ padding: '24px' }}>
        <Row gutter={[24, 24]} justify="center">
          <Col xs={24} lg={10}>
            <ApiConfigurationPanel
              config={apiConfig}
              onConfigChange={handleConfigChange}
              onFetch={handleFetch}
              isLoading={isLoading}
              isDisabled={isDisabled}
            />
          </Col>
          <Col xs={24} lg={14}>
            <ResponseAnalysisPanel
              apiResponse={apiResponse}
              treeData={schemaTree}
              selectedKeys={selectedKeys}
              expandedKeys={expandedKeys}
              activeTab={activeTab}
              onTabChange={setActiveTab}
              onTreeCheck={handleTreeCheck}
              onExpand={handleExpand}
              onContinue={handleContinue}
            />
          </Col>
        </Row>
      </div>
    </>
  );
};

export default EtlTools;
