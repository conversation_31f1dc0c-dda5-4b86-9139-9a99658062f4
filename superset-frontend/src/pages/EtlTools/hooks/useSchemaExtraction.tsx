import { Space, Tag, Typography } from 'antd-v5';
import { useCallback } from 'react';
import { MAX_DEPTH, SAMPLE_SIZE } from '../constants';
import { SchemaField, DataType, TreeNode } from '../types';
import { getTypeColor } from '../utils';

const useSchemaExtraction = () => {
  const extractSchemaFromData = useCallback((data: any): SchemaField[] => {
    const extractWithDepth = (
      obj: any,
      prefix: string = '',
      depth: number = 0,
      visited: WeakSet<object> = new WeakSet(),
    ): SchemaField[] => {
      if (depth > MAX_DEPTH) {
        return [
          {
            key: prefix || 'root',
            title: 'Max Depth Reached',
            type: 'truncated',
            value: '[Truncated - Too Deep]',
            path: prefix,
            isLeaf: true,
          },
        ];
      }

      if (obj === null || obj === undefined) {
        return [
          {
            key: prefix || 'root',
            title: prefix.split('.').pop() || 'root',
            type: obj === null ? 'null' : 'undefined',
            value: obj,
            path: prefix,
            isLeaf: true,
          },
        ];
      }

      if (typeof obj === 'object' && visited.has(obj)) {
        return [
          {
            key: prefix,
            title: prefix.split('.').pop() || 'circular',
            type: 'circular',
            value: '[Circular Reference]',
            path: prefix,
            isLeaf: true,
          },
        ];
      }

      if (Array.isArray(obj)) {
        if (obj.length === 0) {
          return [
            {
              key: prefix,
              title: (prefix.split('.').pop() || 'array') + '[]',
              type: 'array',
              value: 'Empty Array',
              path: prefix,
              isLeaf: true,
            },
          ];
        }

        const newVisited = new WeakSet();
        if (typeof obj === 'object') {
          newVisited.add(obj);
        }

        const fieldMap = new Map<
          string,
          { field: SchemaField; occurrences: number; types: Set<string> }
        >();
        const sampleSize = Math.min(obj.length, SAMPLE_SIZE);
        const sampledItems = obj.slice(0, sampleSize);

        sampledItems.forEach(item => {
          const itemFields = extractWithDepth(item, '', depth + 1, newVisited);
          itemFields.forEach(field => {
            const fieldKey = field.path;
            if (!fieldMap.has(fieldKey)) {
              fieldMap.set(fieldKey, {
                field: { ...field },
                occurrences: 0,
                types: new Set(),
              });
            }
            const existing = fieldMap.get(fieldKey)!;
            existing.occurrences++;
            existing.types.add(field.type);
          });
        });

        const children: SchemaField[] = Array.from(fieldMap.values()).map(
          ({ field, occurrences, types }) => ({
            ...field,
            key: prefix ? `${prefix}.${field.path}` : field.path,
            path: prefix ? `${prefix}.${field.path}` : field.path,
            type: types.size > 1 ? Array.from(types).join('|') : field.type,
            arrayContext: true,
            occurrences,
            totalItems: obj.length,
          }),
        );

        return [
          {
            key: prefix,
            title: (prefix.split('.').pop() || 'array') + `[${obj.length}]`,
            type: 'array',
            value: `Array with ${obj.length} items`,
            path: prefix,
            isLeaf: false,
            children,
          },
        ];
      }

      if (typeof obj === 'object') {
        const newVisited = new WeakSet();
        if (typeof obj === 'object') {
          newVisited.add(obj);
        }

        const children: SchemaField[] = Object.keys(obj).flatMap(key => {
          const fullPath = prefix ? `${prefix}.${key}` : key;
          return extractWithDepth(obj[key], fullPath, depth + 1, newVisited);
        });

        if (prefix === '') {
          return children;
        }

        return [
          {
            key: prefix,
            title: prefix.split('.').pop() || 'object',
            type: 'object',
            value: 'Object',
            path: prefix,
            isLeaf: false,
            children,
          },
        ];
      }

      return [
        {
          key: prefix || 'primitive',
          title: prefix.split('.').pop() || 'value',
          type: typeof obj as DataType,
          value: obj,
          path: prefix,
          isLeaf: true,
        },
      ];
    };

    return extractWithDepth(data);
  }, []);

  const buildTreeData = useCallback((schema: SchemaField[]): TreeNode[] => {
    const processedKeys = new Set<string>();

    const buildTree = (fields: SchemaField[]): TreeNode[] => {
      return fields.map(item => {
        if (processedKeys.has(item.key)) {
          return {
            key: `${item.key}_duplicate_${Math.random()}`,
            title: (
              <Typography.Text type="secondary">[{item.title}]</Typography.Text>
            ),
            value: item.value,
            type: item.type,
            path: item.path,
            isLeaf: true,
          };
        }

        processedKeys.add(item.key);

        const node: TreeNode = {
          key: item.key,
          title: (
            <Space size="small">
              <Typography.Text strong style={{ color: '#1890ff' }}>
                {typeof item.title === 'string'
                  ? item.title
                  : String(item.title)}
              </Typography.Text>
              <Tag color={getTypeColor(item.type)}>{item.type}</Tag>
              {item.arrayContext && item.occurrences && item.totalItems && (
                <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                  ({item.occurrences}/{item.totalItems})
                </Typography.Text>
              )}
            </Space>
          ),
          value: item.value,
          type: item.type,
          path: item.path,
          isLeaf: item.isLeaf,
        };

        if (item.children && item.children.length > 0 && !item.isLeaf) {
          node.children = buildTree(item.children);
        }

        return node;
      });
    };

    return buildTree(schema);
  }, []);

  return { extractSchemaFromData, buildTreeData };
};

export default useSchemaExtraction;
