import { ApiOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Card, Space, Input, Tooltip, Button, Typography } from 'antd-v5';
import { TextArea } from 'src/components/Input';
import { ApiConfig } from '../../types';
import React from 'react';

interface Props {
  config: ApiConfig;
  onConfigChange: (field: keyof ApiConfig, value: string) => void;
  onFetch: () => void;
  isLoading: boolean;
  isDisabled: boolean;
}

export const ApiConfigurationPanel: React.FC<Props> = React.memo(
  ({ config, onConfigChange, onFetch, isLoading, isDisabled }) => {
    return (
      <Card
        title={
          <Space>
            <ApiOutlined style={{ color: '#1890ff' }} />
            <Typography.Text strong>API Configuration</Typography.Text>
          </Space>
        }
        style={{
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          backgroundColor: '#fff',
        }}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Typography.Text strong>Pipeline Name</Typography.Text>
            <Input
              placeholder="Enter Pipeline Name"
              value={config.pipelineName}
              onChange={e => onConfigChange('pipelineName', e.target.value)}
              style={{ marginTop: '8px' }}
            />
          </div>

          <div>
            <Typography.Text strong>
              cURL Command
              <Tooltip title="Paste your cURL command here">
                <InfoCircleOutlined
                  style={{ marginLeft: 8, color: '#8c8c8c' }}
                />
              </Tooltip>
            </Typography.Text>
            <TextArea
              rows={3}
              placeholder="curl -X GET 'https://api.example.com/data'"
              value={config.curlInput}
              onChange={e => onConfigChange('curlInput', e.target.value)}
              style={{ marginTop: '8px', fontFamily: 'Monaco, monospace' }}
            />
          </div>

          <div>
            <Typography.Text strong>API Key</Typography.Text>
            <Input.Password
              placeholder="Enter your API key"
              value={config.apiKey}
              onChange={e => onConfigChange('apiKey', e.target.value)}
              style={{ marginTop: '8px' }}
            />
          </div>

          <div>
            <Typography.Text strong>
              Custom Headers
              <Tooltip title="Enter headers as a JSON object. Example: {'Content-Type': 'application/json'}">
                <InfoCircleOutlined
                  style={{ marginLeft: 8, color: '#8c8c8c' }}
                />
              </Tooltip>
            </Typography.Text>
            <TextArea
              rows={2}
              placeholder='{"Content-Type": "application/json"}'
              value={config.headers}
              onChange={e => onConfigChange('headers', e.target.value)}
              style={{ marginTop: '8px', fontFamily: 'Monaco, monospace' }}
            />
          </div>

          <Button
            type="primary"
            onClick={onFetch}
            loading={isLoading}
            disabled={isDisabled}
            size="large"
          >
            {isLoading ? 'Fetching...' : 'Fetch Data'}
          </Button>
        </Space>
      </Card>
    );
  },
);
