import { DatabaseOutlined } from '@ant-design/icons';
import { Card, Space, Tabs, Button, Typography } from 'antd-v5';
import TabPane from 'antd-v5/es/tabs/TabPane';
import React, { useMemo } from 'react';
import { TreeNode } from '../../types';
import SchemaTreeView from '../SchemaTreeView/SchemaTreeView';

interface Props {
  apiResponse: any;
  treeData: TreeNode[];
  selectedKeys: string[];
  expandedKeys: string[];
  activeTab: string;
  onTabChange: (key: string) => void;
  onTreeCheck: (
    checkedKeys:
      | React.Key[]
      | { checked: React.Key[]; halfChecked: React.Key[] },
  ) => void;
  onExpand: (keys: React.Key[]) => void;
  onContinue: () => void;
}

const ResponseAnalysisPanel: React.FC<Props> = React.memo(
  ({
    apiResponse,
    treeData,
    selectedKeys,
    expandedKeys,
    activeTab,
    onTabChange,
    onTreeCheck,
    onExpand,
    onContinue,
  }) => {
    const jsonString = useMemo(
      () => (apiResponse ? JSON.stringify(apiResponse, null, 2) : ''),
      [apiResponse],
    );

    if (!apiResponse) {
      return (
        <Card
          title={
            <Space>
              <DatabaseOutlined style={{ color: '#52c41a' }} />
              <Typography.Text strong>API Response Analysis</Typography.Text>
            </Space>
          }
          style={{
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
            borderRadius: '12px',
            backgroundColor: '#FFF',
          }}
        >
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <Typography.Title level={4} type="secondary">
              No Response Data
            </Typography.Title>
            <Typography.Text type="secondary">
              Configure and fetch an API to analyze the response schema
            </Typography.Text>
          </div>
        </Card>
      );
    }

    return (
      <Card
        title={
          <Space>
            <DatabaseOutlined style={{ color: '#52c41a' }} />
            <Typography.Text strong>API Response Analysis</Typography.Text>
          </Space>
        }
        style={{
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          backgroundColor: '#FFF',
        }}
      >
        <Tabs activeKey={activeTab} onChange={onTabChange}>
          <TabPane tab="Schema Selection" key="schema">
            <SchemaTreeView
              treeData={treeData}
              selectedKeys={selectedKeys}
              expandedKeys={expandedKeys}
              onTreeCheck={onTreeCheck}
              onExpand={onExpand}
            />
            <div
              style={{
                marginTop: 16,
                paddingTop: 16,
                borderTop: '1px solid #f0f0f0',
              }}
            >
              <Button
                type="primary"
                size="large"
                disabled={selectedKeys.length === 0}
                style={{ width: '100%' }}
                onClick={onContinue}
              >
                Continue with {selectedKeys.length} Selected Fields
              </Button>
            </div>
          </TabPane>
          <TabPane tab="Raw Response" key="raw">
            <pre
              style={{
                fontSize: 12,
                maxHeight: '500px',
                overflow: 'auto',
                whiteSpace: 'pre-wrap',
              }}
            >
              {jsonString}
            </pre>
          </TabPane>
        </Tabs>
      </Card>
    );
  },
);

export default ResponseAnalysisPanel;
