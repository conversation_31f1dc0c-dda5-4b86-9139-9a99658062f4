import { Space, Tag, Tree, Typography } from 'antd-v5';
import React, { useMemo, useCallback } from 'react';
import { TreeNode } from '../../types';
import { CaretDownOutlined } from '@ant-design/icons';

interface OptimizedSchemaTreeViewProps {
  treeData: TreeNode[];
  selectedKeys: string[];
  expandedKeys: string[];
  onTreeCheck: (
    checkedKeys:
      | React.Key[]
      | { checked: React.Key[]; halfChecked: React.Key[] },
  ) => void;
  onExpand: (keys: React.Key[]) => void;
}

const SchemaTreeView: React.FC<OptimizedSchemaTreeViewProps> = React.memo(
  ({ treeData, selectedKeys, expandedKeys, onTreeCheck, onExpand }) => {
    const selectedCount = useMemo(
      () => selectedKeys.length,
      [selectedKeys.length],
    );

    const memoizedTreeData = useMemo(() => treeData, [treeData]);

    const expandedKeysSet = useMemo(
      () => new Set(expandedKeys),
      [expandedKeys],
    );

    const selectedKeysSet = useMemo(
      () => new Set(selectedKeys),
      [selectedKeys],
    );

    const handleTreeCheck = useCallback(
      (
        checkedKeys:
          | React.Key[]
          | { checked: React.Key[]; halfChecked: React.Key[] },
      ) => {
        onTreeCheck(checkedKeys);
      },
      [onTreeCheck],
    );

    const handleExpand = useCallback(
      (keys: React.Key[]) => {
        onExpand(keys);
      },
      [onExpand],
    );

    const titleRender = useCallback(
      (nodeData: any) => {
        const isSelected = selectedKeysSet.has(nodeData.key);

        return (
          <span
            style={{
              fontWeight: isSelected ? 600 : 400,
              color: isSelected ? '#1890ff' : undefined,
            }}
            title={nodeData.title}
          >
            {nodeData.title}
          </span>
        );
      },
      [selectedKeysSet, expandedKeysSet],
    );

    return (
      <div>
        <div
          style={{
            marginBottom: 16,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Space>
            <Typography.Text strong>Schema Fields</Typography.Text>
            <Tag color="blue">{selectedCount} selected</Tag>
          </Space>
        </div>

        <Tree
          checkable
          checkedKeys={selectedKeys}
          expandedKeys={expandedKeys}
          onCheck={handleTreeCheck}
          onExpand={handleExpand}
          treeData={memoizedTreeData}
          titleRender={titleRender}
          height={400}
          virtual={true}
          blockNode={true}
          showLine={false}
          style={{
            border: '1px solid #f0f0f0',
            borderRadius: '6px',
            padding: '12px',
          }}
          switcherIcon={<CaretDownOutlined style={{ position: 'relative' }} />}
        />
      </div>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.treeData === nextProps.treeData &&
      prevProps.selectedKeys.length === nextProps.selectedKeys.length &&
      prevProps.expandedKeys.length === nextProps.expandedKeys.length &&
      prevProps.selectedKeys.every(
        (key, index) => key === nextProps.selectedKeys[index],
      ) &&
      prevProps.expandedKeys.every(
        (key, index) => key === nextProps.expandedKeys[index],
      )
    );
  },
);

SchemaTreeView.displayName = 'SchemaTreeView';

export default SchemaTreeView;
