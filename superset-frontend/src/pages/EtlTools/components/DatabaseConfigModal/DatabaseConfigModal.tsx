import { DatabaseOutlined } from '@ant-design/icons';
import {
  Form,
  Modal,
  Space,
  Input,
  Button,
  Typography,
  message,
  Select,
} from 'antd-v5';
import React, { Dispatch, SetStateAction, useState } from 'react';
import axios from 'axios';

interface Props {
  visible: boolean;
  selectedFieldsCount: number;
  onCancel: () => void;
  pipelineId: number;
  selectedKeys: string[];
  setShowDatabaseModal: Dispatch<SetStateAction<boolean>>;
  setIsReset: Dispatch<SetStateAction<boolean>>;
  dbConfig: { database: string; table: string };
  dbs: string[];
}

const DatabaseConfigModal: React.FC<Props> = React.memo(
  ({
    visible,
    onCancel,
    pipelineId,
    selectedKeys,
    setShowDatabaseModal,
    setIsReset,
    dbConfig,
    dbs,
  }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState<boolean>(false);

    const handleSave = async () => {
      try {
        setLoading(true);
        const values = await form.validateFields();

        const selectedFields = Array.from(
          new Set(
            selectedKeys
              .map(key => {
                const trimmedKey = key.trim();
                return trimmedKey.endsWith('.')
                  ? trimmedKey.slice(0, -1)
                  : trimmedKey;
              })
              .filter(key => key !== ''),
          ),
        );

        const payload = {
          db: values.database,
          table_name: values.table,
          pipeline_id: pipelineId,
          selected_fields: selectedFields,
          is_edit: !!dbConfig.table,
        };

        const response = await axios.post(
          `${process.env.REACT_APP_LLM_URL}pipeline/schema_selection_and_table_creation`,
          payload,
        );

        if (response.status === 200) {
          message.success('Schema selection saved successfully');
          setShowDatabaseModal(false);
          form.resetFields();
          !dbConfig.table && setIsReset(true);
        }

        const data = {
          ...values,
          frequency: `${values.frequencyValue}${values.frequencyUnit}`,
        };

        delete data.frequencyValue;
        delete data.frequencyUnit;

        form.resetFields();
      } catch (error) {
        message.error(
          error?.response?.data?.error || 'Failed to save schema selection',
        );
        console.error('Validation failed:', error);
      } finally {
        setLoading(false);
      }
    };

    const handleCancel = () => {
      form.resetFields();
      onCancel();
    };

    return (
      <>
        <style>{`
        .antd5-select-selector {
          background-color: white !important;
        }
      `}</style>
        <Modal
          centered
          closable={false}
          title={
            <Space>
              <DatabaseOutlined style={{ color: '#1890ff' }} />
              <Typography.Text strong>Database Configuration</Typography.Text>
            </Space>
          }
          open={visible}
          onCancel={handleCancel}
          footer={null}
          width={500}
          destroyOnHidden
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSave}
            initialValues={{
              database: dbConfig.database,
              table: dbConfig.table,
              frequencyValue: 1,
              frequencyUnit: 'd',
            }}
          >
            <Form.Item
              name="database"
              label="Database"
              rules={[
                { required: true, message: 'Please enter database name' },
              ]}
            >
              <Select
                placeholder="Select Database"
                disabled={dbConfig.table ? true : false}
                options={dbs?.map(db => ({ value: db, label: db }))}
                styles={{
                  popup: { root: { backgroundColor: 'white' } },
                  root: { background: 'white' },
                }}
              />
            </Form.Item>

            <Form.Item
              name="table"
              label="Table"
              rules={[
                { required: true, message: 'Please enter table name' },
                {
                  pattern: /^[a-zA-Z_]+$/,
                  message:
                    'Only alphabetic letters allowed (no numbers, spaces, or special characters)',
                },
              ]}
            >
              <Input
                placeholder="Enter Table"
                disabled={dbConfig.table ? true : false}
              />
            </Form.Item>

            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
                gap: 8,
                marginTop: 24,
              }}
            >
              <Button onClick={handleCancel} disabled={loading}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                Save
              </Button>
            </div>
          </Form>
        </Modal>
      </>
    );
  },
);

export default DatabaseConfigModal;
