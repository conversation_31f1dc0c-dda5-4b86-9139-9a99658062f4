import { TYPE_COLORS } from './constants';
import { TreeNode } from './types';

const getTypeColor = (type: string): string => {
  if (type.includes('|')) return 'geekblue';
  return TYPE_COLORS[type] || 'default';
};

const getAllLeafKeys = (treeData: TreeNode[]): string[] => {
  const leafKeys: string[] = [];
  const traverse = (nodes: TreeNode[]): void => {
    nodes.forEach(node => {
      if (node.isLeaf) {
        leafKeys.push(node.key);
      }
      if (node.children) {
        traverse(node.children);
      }
    });
  };
  traverse(treeData);
  return leafKeys;
};

export { getTypeColor, getAllLeafKeys };
