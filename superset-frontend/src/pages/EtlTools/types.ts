export interface ApiConfig {
  pipelineName: string;
  curlInput: string;
  apiKey: string;
  headers: string;
}

export interface SchemaField {
  key: string;
  title: string;
  type: string;
  value: any;
  path: string;
  isLeaf: boolean;
  children?: SchemaField[];
  arrayContext?: boolean;
  occurrences?: number;
  totalItems?: number;
}

export interface TreeNode {
  key: string;
  title: React.ReactNode;
  value: any;
  type: string;
  path: string;
  isLeaf: boolean;
  children?: TreeNode[];
}

export type DataType =
  | 'string'
  | 'number'
  | 'boolean'
  | 'object'
  | 'array'
  | 'null'
  | 'undefined'
  | 'circular';

export interface DatabaseConfig {
  database: string;
  table: string;
  frequency: string;
}
