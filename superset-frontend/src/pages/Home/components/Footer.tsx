import React from 'react';
import { Layout, Image, Typography } from 'antd';
import { Flex } from 'antd-v5';

const { Footer: AntFooter } = Layout;
const { Paragraph, Text } = Typography;

const Footer = () => {
  return (
    <AntFooter
      style={{
        maxWidth: '1280px',
        margin: '0 auto',
        padding: '40px 16px',
        textAlign: 'center',
        borderTop: '1px solid #f0f0f0',
      }}
    >
      <Flex gap={15}>
        <a href="#">
          <Image
            src="/static/assets/images/superset-logo-horiz copy.png"
            alt="DDX UI"
            preview={false}
            style={{ height: '32px', width: '32px', mixBlendMode: 'multiply' }}
          />
        </a>
        <Paragraph style={{ color: '#6b7280', fontSize: '16px' }}>
          <Text
            style={{
              background: 'linear-gradient(to right, #ffd523, #ff6b6b)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 'bold',
              fontSize: '20px',
            }}
          >
            DDX UI™
          </Text>{' '}
          is built on a fortified Apache Superset with integration to Generative
          AI and LLM Technologies Big Science Initiative: MistralAI and Meta:
          Llama3.1
        </Paragraph>
      </Flex>
      <Paragraph
        style={{ color: '#6b7280', fontSize: '14px', marginTop: '12px' }}
      >
        © 2020-2025, DRE Digital
      </Paragraph>
      <Image
        src="/static/assets/images/images/underline.png"
        alt="underline"
        preview={false}
        style={{ display: 'block', margin: '4px auto 0' }}
      />
    </AntFooter>
  );
};

export default Footer;
