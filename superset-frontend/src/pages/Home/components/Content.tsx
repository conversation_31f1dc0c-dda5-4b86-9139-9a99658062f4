import React from 'react';
import { Row, Col, Typography, Image } from 'antd';
import Accordion from './Accordion';

const { Title, Paragraph } = Typography;

function Content() {
  return (
    <div style={{ backgroundColor: '#f9fafe' }}>
      <div style={{ padding: '10px 0' }}>
        <Title level={1} style={{ textAlign: 'center' }}>
          <span
            style={{
              fontWeight: 'bold',
              background: 'linear-gradient(to right, #ffd523, #ff6b6b)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            DDX UI™:
          </span>{' '}
          Unleashing the Power of Your Data
        </Title>
        <div className="text-center">
          <Image
            src="/static/assets/images/images/underline.png"
            alt="underline"
            preview={false}
            style={{ display: 'block', margin: '5px auto 10px' }}
          />
        </div>

        <Row gutter={[0, 40]}>
          <Col xs={24} md={12} style={{ paddingTop: '20px' }}>
            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <Image
                src="/static/assets/images/images/unnamed.gif"
                alt="u"
                width={150}
                preview={false}
              />
            </div>
          </Col>
          <Col xs={24} md={12} style={{ paddingTop: '20px' }}>
            <Paragraph>
              <span style={{ fontWeight: 'bold', fontSize: '18px' }}>
                DDX UI™
              </span>{' '}
              (DDX Unified Intelligence™) platform is a leading-edge data
              democratization, data exploration, visualization, data analytics,
              and artificial intelligence solution that adds unparalleled
              business value. It is built upon a foundation of cutting-edge
              Open-Source components from the Apache® Software Foundation,
              OpenAI, MistralAI, Meta, Anthropic, Big Science Initiative and
              Google®, seamlessly integrated with our proprietary, leading-edge
              frameworks and algorithms. Harness the synergy of Open-Source
              innovation and proprietary excellence to unlock the full potential
              of your data.
            </Paragraph>
          </Col>
        </Row>
      </div>

      <div>
        <div className="bg-text-1" style={{ padding: '15px' }}>
          <div style={{ padding: '10px 0' }}>
            <div style={{ paddingTop: '20px' }}>
              <div
                className="text-card"
                style={{ width: '100%', maxWidth: '66.6667%' }}
              >
                <Paragraph>
                  With a focus on Axiology, Epistemology, Ontology, Taxonomy,
                  Topology, and Homology, our platform extracts value in ways no
                  other solution can. Whether you're a data novice or an expert,{' '}
                  <span style={{ fontWeight: 'bold', fontSize: '18px' }}>
                    DDX UI™
                  </span>{' '}
                  offers an intuitive, fast, and lightweight self-serve
                  experience. From simple line charts to intricate geospatial
                  visualizations, our platform makes it easy to explore and
                  understand your data.
                </Paragraph>
              </div>
            </div>
            <div
              style={{
                paddingTop: '20px',
                display: 'flex',
                justifyContent: 'flex-end',
              }}
            >
              <div
                className="text-card"
                style={{ width: '100%', maxWidth: '66.6667%' }}
              >
                <Paragraph>
                  <span style={{ fontWeight: 'bold', fontSize: '18px' }}>
                    DDX UI™
                  </span>{' '}
                  incorporates the latest advancements in Large Language Models
                  (LLMs), Generative AI, and our proprietary innovations to
                  ensure your data analysis is both powerful and efficient. Our
                  platform's user-friendly design and advanced capabilities
                  enable users to perform data wizardry, bringing out the best
                  possible results for enterprise value. Discover the synergy of
                  Open Source and proprietary excellence with{' '}
                  <span style={{ fontWeight: 'bold', fontSize: '18px' }}>
                    DDX UI™
                  </span>
                  , and revolutionize the way you handle your data.
                </Paragraph>
              </div>
            </div>
            <div style={{ paddingTop: '20px' }}>
              <div
                className="text-card"
                style={{ width: '100%', maxWidth: '66.6667%' }}
              >
                <Paragraph>
                  By incorporating Agent Architectures{' '}
                  <span style={{ fontWeight: 'bold', fontSize: '18px' }}>
                    DDX UI™
                  </span>{' '}
                  enables data transforms intelligence, enhances
                  decision-making, predicts trends, personalizes healthcare,
                  revolutionizes diagnostics, optimizes financial strategies,
                  ensures security, and creates unprecedented insights across
                  all sectors.
                </Paragraph>
              </div>
            </div>
          </div>
        </div>

        <div
          style={{
            padding: '0 16px 10px',
            maxWidth: '1200px',
            margin: '0 auto',
          }}
        >
          <div
            style={{
              padding: '20px 16px 0',
              maxWidth: '1200px',
              margin: '0 auto',
            }}
          >
            <div className="text-center">
              <Image
                src="/static/assets/images/images/overline.png"
                alt="overline"
                preview={false}
                style={{ display: 'block', margin: '0 auto' }}
              />
            </div>
            <Title
              level={2}
              style={{
                textAlign: 'center',
                marginTop: '12px',
                marginBottom: '0',
              }}
            >
              <span
                style={{
                  background: 'linear-gradient(to right, #ff6b6b, #ffa500)',
                  WebkitBackgroundClip: 'text',
                  color: 'transparent',
                }}
              >
                Overview
              </span>
            </Title>
            <div className="text-center">
              <Image
                src="/static/assets/images/images/underline.png"
                alt="underline"
                preview={false}
                style={{ display: 'block', margin: '5px auto 10px' }}
              />
            </div>

            <Paragraph style={{ textAlign: 'center', fontSize: '20px' }}>
              <span style={{ fontWeight: 'bold', fontSize: '24px' }}>
                DDX UI™
              </span>{' '}
              is fast, lightweight, intuitive, and loaded with options that make
              it easy for users of all skill sets to explore and visualize their
              data, from simple line charts to highly detailed geospatial
              charts.
            </Paragraph>
          </div>

          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
            <Row gutter={[12, 12]}>
              <Col xs={24} md={12} lg={6} style={{ marginBottom: '12px' }}>
                <div
                  style={{
                    border: '1px solid #ddd',
                    backgroundColor: '#fff',
                    borderRadius: '4px',
                    margin: '12px',
                    padding: '12px',
                    textAlign: 'center',
                    height: '100%',
                  }}
                >
                  <Image
                    src="/static/assets/images/images/Picture1.png"
                    alt="a"
                    preview={false}
                  />
                  <Title
                    level={4}
                    style={{
                      fontStyle: 'italic',
                      marginTop: '8px',
                      marginBottom: '8px',
                    }}
                  >
                    Powerful yet easy to use
                  </Title>
                  <Paragraph>
                    Superse`t makes it easy to explore your data, using either
                    our simple no-code viz builder or state-of-the-art SQL IDE.
                  </Paragraph>
                </div>
              </Col>
              <Col xs={24} md={12} lg={6} style={{ marginBottom: '12px' }}>
                <div
                  style={{
                    border: '1px solid #ddd',
                    backgroundColor: '#fff',
                    borderRadius: '4px',
                    margin: '12px',
                    padding: '12px',
                    textAlign: 'center',
                    height: '100%',
                  }}
                >
                  <Image
                    src="/static/assets/images/images/Picture2.png"
                    alt="a"
                    preview={false}
                  />
                  <Title
                    level={4}
                    style={{
                      fontStyle: 'italic',
                      marginTop: '8px',
                      marginBottom: '8px',
                    }}
                  >
                    Integrates with modern databases
                  </Title>
                  <Paragraph>
                    Superset can connect to any SQL-based databases including
                    modern cloud-native databases and engines at petabyte scale.
                  </Paragraph>
                </div>
              </Col>
              <Col xs={24} md={12} lg={6} style={{ marginBottom: '12px' }}>
                <div
                  style={{
                    border: '1px solid #ddd',
                    backgroundColor: '#fff',
                    borderRadius: '4px',
                    margin: '12px',
                    padding: '12px',
                    textAlign: 'center',
                    height: '100%',
                  }}
                >
                  <Image
                    src="/static/assets/images/images/Picture3.png"
                    alt="a"
                    preview={false}
                  />
                  <Title
                    level={4}
                    style={{
                      fontStyle: 'italic',
                      marginTop: '8px',
                      marginBottom: '8px',
                    }}
                  >
                    Modern architecture
                  </Title>
                  <Paragraph>
                    Superset is lightweight and highly scalable, leveraging the
                    power of your existing data infrastructure without requiring
                    yet another ingestion layer.
                  </Paragraph>
                </div>
              </Col>
              <Col xs={24} md={12} lg={6} style={{ marginBottom: '12px' }}>
                <div
                  style={{
                    border: '1px solid #ddd',
                    backgroundColor: '#fff',
                    borderRadius: '4px',
                    margin: '12px',
                    padding: '12px',
                    textAlign: 'center',
                    height: '100%',
                  }}
                >
                  <Image
                    src="/static/assets/images/images/Picture4.png"
                    alt="a"
                    preview={false}
                  />
                  <Title
                    level={4}
                    style={{
                      fontStyle: 'italic',
                      marginTop: '8px',
                      marginBottom: '8px',
                    }}
                  >
                    Rich visualizations and dashboards
                  </Title>
                  <Paragraph>
                    Superset ships with 40+ pre-installed visualization types.
                    Our plug-in architecture makes it easy to build custom
                    visualizations.
                  </Paragraph>
                </div>
              </Col>
            </Row>
          </div>
        </div>
      </div>

      <div className="bg-text-2" style={{ padding: '5rem 0' }}>
        <h1
          style={{
            fontSize: '2.25rem',
            fontWeight: 'bold',
            padding: '0.75rem 0',
            textAlign: 'center',
            textShadow: '0 0 10px rgba(255, 255, 255, 0.5)',
          }}
        >
          <span
            style={{
              background:
                'linear-gradient(to right, #ff6b6b, #ffa500, #ffff00, #00ff00, #00ffff, #0000ff, #ff00ff)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            Self-serve intelligence for anyone
          </span>
        </h1>
        <div className="text-center">
          <Image
            src="/static/assets/images/images/underline.png"
            alt="underline"
            style={{ display: 'block', margin: '2.5rem auto 0' }}
            preview={false}
          />
        </div>

        <div style={{ padding: '0 1.25rem' }}>
          <div
            className="accordion-container"
            style={{
              backgroundColor: 'white',
              border: '1px solid #e8e8e8',
              borderRadius: '0.5rem',
              maxWidth: '32rem',
              margin: 'auto',
              padding: '1rem',
            }}
          >
            <Accordion
              title="Dashboards"
              content="Explore data and find insights from interactive dashboards."
            />
            <Accordion
              title="Chart Builder"
              content="Drag and drop to create robust charts and tables."
            />
            <Accordion
              title="Data Engineering Lab"
              content="Write custom SQL queries, browse database metadata, use Jinja templating, and more."
            />
            <Accordion
              title="Datasets"
              content="Create physical and virtual datasets to scale chart creation with unified metric definitions."
            />
            <Accordion
              title="LLM and GenAI integration"
              content="Write custom queries, browse database metadata, use latest templates."
            />
          </div>
        </div>
      </div>

      <div style={{ backgroundColor: 'white', paddingBottom: 35 }}>
        <div className="text-center">
          <Image
            src="/static/assets/images/images/overline.png"
            alt="overline"
            preview={false}
            style={{
              display: 'block',
              marginLeft: 'auto',
              marginRight: 'auto',
            }}
          />
        </div>
        <Title level={1} style={{ textAlign: 'center', padding: '12px 0' }}>
          <span
            style={{
              backgroundImage: 'linear-gradient(to right, #ff8b13, #ffa500)',
              WebkitBackgroundClip: 'text',
              color: 'transparent',
            }}
          >
            Key features
          </span>
        </Title>
        <div className="text-center">
          <Image
            src="/static/assets/images/images/underline.png"
            alt="underline"
            preview={false}
            style={{
              display: 'block',
              marginLeft: 'auto',
              marginRight: 'auto',
              marginBottom: 40,
            }}
          />
        </div>

        <Row
          gutter={[16, 16]}
          style={{ maxWidth: 720, margin: '0 auto', padding: '0 16px' }}
        >
          <Col span={24}>
            <Row
              align="middle"
              gutter={8}
              style={{ display: 'flex', flexWrap: 'nowrap' }}
            >
              <Col>
                <Image
                  src="/static/assets/images/images/check-icon.svg"
                  alt="svg"
                  preview={false}
                  width={16}
                  height={16}
                />
              </Col>
              <Col>
                <Paragraph
                  style={{ fontSize: 20, fontWeight: 'bold', margin: 0 }}
                >
                  40+ pre-installed visualizations
                </Paragraph>
              </Col>
            </Row>
          </Col>
          <Col span={24}>
            <Row
              align="middle"
              gutter={8}
              style={{ display: 'flex', flexWrap: 'nowrap' }}
            >
              <Col>
                <Image
                  src="/static/assets/images/images/check-icon.svg"
                  alt="svg"
                  preview={false}
                  width={16}
                  height={16}
                />
              </Col>
              <Col>
                <Paragraph style={{ fontSize: 20, margin: 0 }}>
                  Support for{' '}
                  <span style={{ fontWeight: 'bold' }}>drag-and-drop</span> and{' '}
                  <span style={{ fontWeight: 'bold' }}>SQL queries</span>
                </Paragraph>
              </Col>
            </Row>
          </Col>
          <Col span={24}>
            <Row
              align="middle"
              gutter={8}
              style={{ display: 'flex', flexWrap: 'nowrap' }}
            >
              <Col>
                <Image
                  src="/static/assets/images/images/check-icon.svg"
                  alt="svg"
                  preview={false}
                  width={16}
                  height={16}
                />
              </Col>
              <Col>
                <Paragraph style={{ fontSize: 20, margin: 0 }}>
                  Data caching{' '}
                  <span style={{ fontWeight: 'bold' }}>
                    for the faster load time of charts and dashboards
                  </span>{' '}
                  and <span style={{ fontWeight: 'bold' }}>SQL queries</span>
                </Paragraph>
              </Col>
            </Row>
          </Col>
          <Col span={24}>
            <Row
              align="middle"
              gutter={8}
              style={{ display: 'flex', flexWrap: 'nowrap' }}
            >
              <Col>
                <Image
                  src="/static/assets/images/images/check-icon.svg"
                  alt="svg"
                  preview={false}
                  width={16}
                  height={16}
                />
              </Col>
              <Col>
                <Paragraph style={{ fontSize: 20, margin: 0 }}>
                  Jinja templating and dashboard filters{' '}
                  <span style={{ fontWeight: 'bold' }}>drag-and-drop</span> for
                  creating interactive dashboards
                </Paragraph>
              </Col>
            </Row>
          </Col>
          <Col span={24}>
            <Row
              align="middle"
              gutter={8}
              style={{ display: 'flex', flexWrap: 'nowrap' }}
            >
              <Col>
                <Image
                  src="/static/assets/images/images/check-icon.svg"
                  alt="svg"
                  preview={false}
                  width={16}
                  height={16}
                />
              </Col>
              <Col>
                <Paragraph style={{ fontSize: 20, margin: 0 }}>
                  <span style={{ fontWeight: 'bold' }}>CSS templates </span>  to
                  customize charts and dashboards to your brand’s look and feel
                </Paragraph>
              </Col>
            </Row>
          </Col>
          <Col span={24}>
            <Row
              align="middle"
              gutter={8}
              style={{ display: 'flex', flexWrap: 'nowrap' }}
            >
              <Col>
                <Image
                  src="/static/assets/images/images/check-icon.svg"
                  alt="svg"
                  preview={false}
                  width={16}
                  height={16}
                />
              </Col>
              <Col>
                <Paragraph style={{ fontSize: 20, margin: 0 }}>
                  <span style={{ fontWeight: 'bold' }}>
                    Cross-filters, drill-to-detail, and drill-by{' '}
                  </span>{' '}
                   features for deeper data analysis
                </Paragraph>
              </Col>
            </Row>
          </Col>
          <Col span={24}>
            <Row
              align="middle"
              gutter={8}
              style={{ display: 'flex', flexWrap: 'nowrap' }}
            >
              <Col>
                <Image
                  src="/static/assets/images/images/check-icon.svg"
                  alt="svg"
                  preview={false}
                  width={16}
                  height={16}
                />
              </Col>
              <Col>
                <Paragraph style={{ fontSize: 20, margin: 0 }}>
                  <span style={{ fontWeight: 'bold' }}>Virtual datasets </span>{' '}
                   for ad-hoc data exploration
                </Paragraph>
              </Col>
            </Row>
          </Col>
          <Col span={24}>
            <Row
              align="middle"
              gutter={8}
              style={{ display: 'flex', flexWrap: 'nowrap' }}
            >
              <Col>
                <Image
                  src="/static/assets/images/images/check-icon.svg"
                  alt="svg"
                  preview={false}
                  width={16}
                  height={16}
                />
              </Col>
              <Col>
                <Paragraph style={{ fontSize: 20, margin: 0 }}>
                  <span style={{ fontWeight: 'bold' }}>
                    Access to new functionalities through
                  </span>{' '}
                   feature flags
                </Paragraph>
              </Col>
            </Row>
          </Col>
        </Row>
      </div>

      <div style={{ backgroundColor: '#fff' }}>
        <div className="text-center">
          <Image
            src="/static/assets/images/images/overline.png"
            alt="overline"
            preview={false}
            style={{ display: 'block', margin: '16px auto 0' }}
          />
        </div>
        <Title level={1} style={{ textAlign: 'center', padding: '12px 0' }}>
          <span
            style={{
              backgroundImage: 'linear-gradient(to right, #ff6b6b, #ffa500)',
              WebkitBackgroundClip: 'text',
              color: 'transparent',
            }}
          >
            Supported Databases
          </span>
        </Title>
        <div className="text-center">
          <Image
            src="/static/assets/images/images/underline.png"
            alt="underline"
            preview={false}
            style={{ display: 'block', margin: '0 auto 10px' }}
          />
        </div>

        <Row gutter={20} justify="center" style={{ margin: '20px 0' }}>
          <Col style={{ marginBottom: '10px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/postgresql.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '120px',
                    objectFit: 'contain',
                  }}
                />
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/database/MongoDB.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  MongoDB
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/mysql.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '120px',
                    objectFit: 'contain',
                  }}
                />
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/database/Redis.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  Redis
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/database/KeyDB.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  KeyDB
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/database/TimescaleDB.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  TimescaleDB
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/database/SurrealDB.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  SurrealDB
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/database/Directus.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  Directus
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/database/Gitlab.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  Gitlab
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/database/Keycloak.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  Keycloak
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/database/Gitness.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  Gitness
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/database/Teable.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  Teable
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/database/APITable.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  APITable
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/database/Hoppscotch.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  Hoppscotch
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/database/ApacheNiFi.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  ApacheNiFi
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/database/GrowthBook.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  GrowthBook
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/agents1.gif"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  Agents
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/anthropic.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  Claude
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/huggingface.jpeg"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  HuggingFace
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/llama3.jpeg"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  Meta
                </Paragraph>
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                border: '1px solid #ccc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#fff',
                padding: '20px',
                height: '120px',
                width: '120px',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/bloom.jpeg"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
                <Paragraph
                  style={{
                    fontWeight: 'bold',
                    color: '#666',
                    textAlign: 'center',
                  }}
                >
                  BigScience
                </Paragraph>
              </a>
            </div>
          </Col>
        </Row>

        <Row
          gutter={20}
          justify="center"
          style={{ margin: '20px 0', textAlign: 'center' }}
        >
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                height: '120px',
                width: '210px',
                backgroundColor: '#fff',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/footer1.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                height: '120px',
                width: '210px',
                backgroundColor: '#fff',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/footer2.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
              </a>
            </div>
          </Col>
          <Col style={{ marginBottom: '15px' }}>
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                height: '120px',
                width: '210px',
                backgroundColor: '#fff',
              }}
            >
              <a href="#">
                <Image
                  src="/static/assets/images/images/footer3.png"
                  alt="a"
                  preview={false}
                  style={{
                    maxWidth: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
              </a>
            </div>
          </Col>
        </Row>

        <Paragraph style={{ textAlign: 'center', margin: '12px 0' }}>
          All products or name brands are trademarks of their respective
          holders, including The Apache Software Foundation, OpenAI, MistralAI,
          Google, Meta, Qdrant
        </Paragraph>
        <div className="text-center">
          <Image
            src="/static/assets/images/images/underline.png"
            alt="underline"
            preview={false}
            style={{ display: 'block', margin: '0 auto 16px', width: '100%' }}
          />
        </div>
      </div>
    </div>
  );
}

export default Content;
