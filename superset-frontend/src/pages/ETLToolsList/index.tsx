/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import axios from 'axios';
import Loading from 'src/components/Loading';
import SubMenu from 'src/features/home/<USER>';
import ListView from 'src/components/ListView';
import withToasts from 'src/components/MessageToasts/withToasts';
import Icons from 'src/components/Icons';
import DeleteModal from 'src/components/DeleteModal';
import { Tooltip } from 'src/components/Tooltip';
import { styled, t } from '@superset-ui/core';
import { VscDebugStart } from 'react-icons/vsc';
import { RiStopLine } from 'react-icons/ri';
import FrequencyModal from './components/FrequencyModal/FrequencyModal';
import { Tag } from 'antd-v5';
import Pagination from 'src/components/Pagination';

const PAGE_SIZE = 25;

export const TIME_MULTIPLIERS = {
  seconds: 1,
  minutes: 60,
  hours: 3600,
  days: 86400,
};

interface DashboardListProps {
  addDangerToast: (msg: string) => void;
  addSuccessToast: (msg: string) => void;
  user: {
    userId: string | number;
    firstName: string;
    lastName: string;
  };
}

export interface Pipeline {
  pipeline_id: number;
  pipeline_name: string;
  table_name: string;
  frequency: number;
  status: string;
  health: string;
  generated_at: string;
  updated_at: string;
  unit: 'seconds' | 'minutes' | 'hours' | 'days';
}

const Actions = styled.div`
  color: ${({ theme }) => theme.colors.grayscale.base};
`;

function PipelineList(props: DashboardListProps) {
  const { addDangerToast, addSuccessToast } = props;
  const [pipelines, setPipelines] = useState<Pipeline[]>([]);
  const [loading, setLoading] = useState(true);
  const [pipelineToDelete, setPipelineToDelete] = useState<Pipeline | null>(
    null,
  );
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: PAGE_SIZE,
    sortBy: [],
    filters: [],
    totalPages: 0,
  });
  const [frequencyModalVisible, setFrequencyModalVisible] = useState({
    open: false,
    frequency: 1,
    id: 0,
    unit: 'seconds',
  });

  const fetchPipelines = useCallback(
    async ({ pageIndex, pageSize, sortBy, filters }: any) => {
      try {
        setLoading(true);
        const response = await axios.get(
          `${process.env.REACT_APP_LLM_URL}/pipeline/status?page_index=${pageIndex}&page_size=${pageSize}&sort_by=${sortBy}&filters=${filters}`,
        );

        if (response.data && response.data.pipelines) {
          setPipelines(response.data.pipelines);
          setPagination(prev => ({
            ...prev,
            totalPages: response.data.pagination.total_pages,
          }));
        }
      } catch (error) {
        console.error('Error fetching pipelines:', error);
        addDangerToast(t('Failed to fetch pipelines'));
        setPipelines([]);
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  useEffect(() => {
    handleUpdateStatus();
  }, [pagination.pageIndex]);

  const handlePipelineDelete = async (pipeline: Pipeline) => {
    try {
      await axios.delete(
        `${process.env.REACT_APP_LLM_URL}/pipeline/delete/${pipeline.pipeline_id}`,
      );
      setPipelines(
        pipelines.filter(p => p.pipeline_id !== pipeline.pipeline_id),
      );
      handleUpdateStatus();
      addSuccessToast(t('Pipeline deleted successfully'));
    } catch (error) {
      addDangerToast(t('Failed to delete pipeline'));
    }
  };

  const handleEditPipeline = (pipeline: Pipeline) => {
    window.location.assign(`/etl-tools/new?pipelineId=${pipeline.pipeline_id}`);
  };

  const handleStartPipeline = (pipeline: Pipeline) => {
    setFrequencyModalVisible({
      open: true,
      frequency: pipeline.frequency,
      id: pipeline.pipeline_id,
      unit: pipeline.unit,
    });
  };

  const handleStopPipeline = async (pipeline: Pipeline) => {
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_LLM_URL}pipeline/stop`,
        {
          pipeline_id: pipeline.pipeline_id,
          frequency: pipeline.frequency,
        },
      );

      if (response.status === 200) {
        addSuccessToast(t('Pipeline stopped successfully'));
        handleUpdateStatus();
      } else {
        addDangerToast(t('Failed to stop pipeline'));
      }
    } catch (error) {
      addDangerToast(t('Failed to stop pipeline'));
    }
  };

  const columns = useMemo(
    () => [
      {
        Header: t('Pipeline Name'),
        accessor: 'pipeline_name',
        disableSortBy: true,
      },
      {
        Header: t('Table Name'),
        accessor: 'table_name',
        disableSortBy: true,
      },
      {
        Cell: ({ row: { original } }: any) => (
          <span
            className={`status-${original.status?.toLowerCase() || 'unknown'}`}
          >
            {original.status === 'active' ? (
              <Tag
                bordered={false}
                style={{ borderRadius: '10px' }}
                color="green"
              >
                Active
              </Tag>
            ) : original.status === 'inactive' ? (
              <Tag
                bordered={false}
                style={{ borderRadius: '10px' }}
                color="red"
              >
                Inactive
              </Tag>
            ) : (
              original.status || 'Unknown'
            )}
          </span>
        ),
        Header: t('Status'),
        accessor: 'status',
        disableSortBy: true,
      },
      {
        Header: t('Frequency'),
        accessor: 'frequency',
        disableSortBy: true,
        Cell: ({ row: { original } }: any) => (
          <>
            {/* @ts-ignore */}
            {original.frequency / TIME_MULTIPLIERS[original.unit]}{' '}
            {original.unit}
          </>
        ),
      },
      {
        Cell: ({ row: { original } }: any) =>
          original.health.toLocaleUpperCase(),
        Header: t('Health'),
        accessor: 'health',
        disableSortBy: true,
      },
      {
        Cell: ({ row: { original } }: any) => (
          <Actions className="actions">
            <Tooltip title={t('Start')} placement="bottom">
              <span
                role="button"
                tabIndex={0}
                className="action-button"
                onClick={() => handleStartPipeline(original)}
                style={{
                  pointerEvents: original.status === 'active' ? 'none' : 'auto',
                }}
              >
                {/* @ts-ignore */}
                <VscDebugStart
                  color={`${original.status === 'active' ? '#ccc' : 'gray'}`}
                />
              </span>
            </Tooltip>
            <Tooltip title={t('Stop')} placement="bottom">
              <span
                role="button"
                tabIndex={0}
                className="action-button"
                onClick={() => handleStopPipeline(original)}
                style={{
                  pointerEvents:
                    original.status === 'inactive' ? 'none' : 'auto',
                }}
              >
                {/* @ts-ignore */}
                <RiStopLine
                  color={`${original.status === 'inactive' ? '#ccc' : 'gray'}`}
                />
              </span>
            </Tooltip>
            <Tooltip title={t('Edit')} placement="bottom">
              <span
                role="button"
                tabIndex={0}
                className="action-button"
                onClick={() => handleEditPipeline(original)}
                style={{
                  pointerEvents: original.status === 'active' ? 'none' : 'auto',
                }}
              >
                <Icons.EditAlt
                  style={{
                    color: original.status === 'active' ? '#ccc' : 'gray',
                  }}
                />
              </span>
            </Tooltip>
            <Tooltip title={t('Delete')} placement="bottom">
              <span
                role="button"
                tabIndex={0}
                className="action-button"
                onClick={() => setPipelineToDelete(original)}
                style={{
                  pointerEvents: original.status === 'active' ? 'none' : 'auto',
                }}
              >
                <Icons.Trash
                  style={{
                    color: original.status === 'active' ? '#ccc' : 'gray',
                  }}
                />
              </span>
            </Tooltip>
          </Actions>
        ),
        Header: t('Actions'),
        id: 'actions',
        disableSortBy: true,
      },
    ],
    [],
  );

  const handleCloseFrequencyModal = () => {
    setFrequencyModalVisible({
      open: false,
      frequency: 1,
      id: 0,
      unit: 'seconds',
    });
  };

  const handleUpdateStatus = () => {
    fetchPipelines({
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize,
      sortBy: pagination.sortBy,
      filters: pagination.filters,
    });
  };

  return (
    <>
      <style>{`
    .ant-message { z-index: 3001 !important; }
    `}</style>
      <FrequencyModal
        data={frequencyModalVisible}
        onCancel={handleCloseFrequencyModal}
        onSave={handleUpdateStatus}
      />
      <SubMenu
        key={'etl-tools-list'}
        name={t('Streaming Data Pipelines')}
        buttons={[
          {
            name: (
              <>
                <i className="fa fa-plus" style={{ marginTop: '3px' }} />{' '}
                {t('Pipeline')}
              </>
            ),
            buttonStyle: 'primary',
            onClick: () => {
              window.location.assign('/etl-tools/new');
            },
          },
        ]}
      />

      <div className="pipeline-list">
        {loading ? (
          <Loading />
        ) : (
          <>
            <ListView
              className="pipeline-list-view"
              columns={columns}
              count={pipelines.length}
              data={pipelines}
              pageSize={PAGE_SIZE}
              initialSort={[{ id: 'updated_at', desc: true }]}
              defaultViewMode="table"
              loading={loading}
              addDangerToast={addDangerToast}
              addSuccessToast={addSuccessToast}
              refreshData={() => {}}
              emptyState={{
                title: t('No pipelines found'),
                image: 'cubes',
              }}
              fetchData={() => {}}
              isETLTools
              key={'etl-tools-list-view'}
            />
            {pipelines.length > 0 && (
              <div
                className="pagination-container"
                style={{ display: 'flex', justifyContent: 'center' }}
              >
                <Pagination
                  totalPages={pagination.totalPages}
                  currentPage={pagination.pageIndex + 1}
                  onChange={(p: number) => {
                    setPagination(prev => ({ ...prev, pageIndex: p - 1 }));
                  }}
                  hideFirstAndLastPageLinks
                />
              </div>
            )}
          </>
        )}
      </div>

      {pipelineToDelete && (
        <DeleteModal
          description={
            <>
              {t('Are you sure you want to delete pipeline')}{' '}
              <b>{pipelineToDelete.pipeline_name}</b>?
            </>
          }
          onConfirm={() => {
            handlePipelineDelete(pipelineToDelete);
            setPipelineToDelete(null);
          }}
          onHide={() => setPipelineToDelete(null)}
          open
          title={t('Delete Pipeline')}
        />
      )}
    </>
  );
}

export default withToasts(PipelineList);
