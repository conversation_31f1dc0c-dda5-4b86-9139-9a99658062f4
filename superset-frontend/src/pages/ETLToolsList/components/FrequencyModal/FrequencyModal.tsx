import React, { useState, useEffect } from 'react';
import { Modal, Button, Form, Select, Row, Col, InputNumber } from 'antd-v5';
import axios from 'axios';
import { t } from '@superset-ui/core';
import { addSuccessToast } from 'src/components/MessageToasts/actions';
import toast from 'react-hot-toast';
import { TIME_MULTIPLIERS } from '../..';

interface FrequencyModalProps {
  data: { open: boolean; frequency: number; id: number; unit: string };
  onCancel: () => void;
  onSave: ({}: any) => void;
}

const FrequencyModal: React.FC<FrequencyModalProps> = ({
  data,
  onCancel,
  onSave,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (data?.open) {
      form.setFieldsValue({
        // @ts-ignore
        frequency: data?.frequency / TIME_MULTIPLIERS[data?.unit],
        unit: data?.unit,
      });
    }
  }, [data?.open, data?.frequency, data?.unit, form]);

  const handleEdit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      const payload = {
        pipeline_id: data?.id,
        // @ts-ignore
        frequency: values.frequency * TIME_MULTIPLIERS[values.unit],
        unit: values.unit,
      };

      const response = await axios.post(
        `${process.env.REACT_APP_LLM_URL}pipeline/start`,
        payload,
      );

      if (response.status === 200) {
        addSuccessToast('Pipeline started successfully');
        onCancel();
        onSave({
          pipeline_id: data?.id,
          // @ts-ignore
          frequency: values.frequency * TIME_MULTIPLIERS[values.unit],
          unit: values.unit,
          status: 'active',
        });
      }
    } catch (error) {
      toast.error(error?.response?.data?.error || 'Failed to start pipeline');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  useEffect(() => {
    if (data?.open) {
      form.setFieldsValue({
        // @ts-ignore
        frequency: data?.frequency / TIME_MULTIPLIERS[data?.unit],
        unit: data?.unit,
      });
    }
  }, [data?.open, data?.frequency, data?.unit, form]);

  return (
    <Modal
      open={data?.open}
      onCancel={handleCancel}
      footer={null}
      styles={{
        content: {
          padding: 0,
        },
        body: {
          padding: 10,
        },
      }}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleEdit}
        style={{ marginTop: 10 }}
      >
        <Form.Item label="Frequency" required>
          <Row gutter={8}>
            <Col span={12}>
              <Form.Item
                name="frequency"
                rules={[
                  { required: true, message: 'Please enter frequency value' },
                  {
                    type: 'number',
                    min: 1,
                    message: 'Value must be at least 1',
                  },
                ]}
              >
                <InputNumber
                  placeholder="Value"
                  min={1}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="unit"
                rules={[{ required: true, message: 'Please select time unit' }]}
              >
                <Select placeholder="Unit">
                  <Select.Option value="seconds">Seconds</Select.Option>
                  <Select.Option value="minutes">Minutes</Select.Option>
                  <Select.Option value="hours">Hours</Select.Option>
                  <Select.Option value="days">Days</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form.Item>

        <Form.Item>
          <div style={{ textAlign: 'right' }}>
            <Button onClick={handleCancel} style={{ marginRight: 8 }}>
              {t('Cancel')}
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              {t('Save')}
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default React.memo(FrequencyModal);
