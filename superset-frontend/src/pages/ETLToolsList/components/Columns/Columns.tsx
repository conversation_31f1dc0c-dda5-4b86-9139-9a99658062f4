import { useMemo } from 'react';
import { Link } from 'react-router-dom';
import Icons from 'src/components/Icons';
import { Tooltip } from 'src/components/Tooltip';
import { t, styled } from '@superset-ui/core';

interface ColumnsProps {
  handleEditPipeline: (pipeline: any) => void;
  setPipelineToDelete: (pipeline: any) => void;
}

const Actions = styled.div`
  color: ${({ theme }) => theme.colors.grayscale.base};
`;

const Columns: (props: ColumnsProps) => any[] = ({
  handleEditPipeline,
  setPipelineToDelete,
}) => {
  const columns = useMemo(
    () => [
      {
        Cell: ({ row: { original } }: any) => (
          <Link to={`/etl-tools/${original.id}`}>{original.pipeline_name}</Link>
        ),
        Header: t('Pipeline Name'),
        accessor: 'pipeline_name',
      },
      {
        Cell: ({ row: { original } }: any) =>
          original.table_name ? original.table_name : null,
        Header: t('Table Name'),
        accessor: 'table_name',
      },
      {
        Cell: ({ row: { original } }: any) => (
          <span
            className={`status-${original.status?.toLowerCase() || 'unknown'}`}
          >
            {original.status === 'data_fetched'
              ? 'Data Fetched'
              : original.status || 'Unknown'}
          </span>
        ),
        Header: t('Status'),
        accessor: 'status',
      },
      {
        Cell: ({ row: { original } }: any) => original.frequency || null,
        Header: t('Frequency'),
        accessor: 'frequency',
      },
      {
        Cell: ({ row: { original } }: any) =>
          original.health === 'healthy'
            ? 'Healthy'
            : original.health === 'unhealthy'
              ? 'Unhealthy'
              : original.health === 'good'
                ? 'Good'
                : original.health || 'Unknown',
        Header: t('Health'),
        accessor: 'health',
      },
      {
        Cell: ({ row: { original } }: any) => (
          <Actions className="actions">
            <Tooltip title={t('Edit')} placement="bottom">
              <span
                role="button"
                tabIndex={0}
                className="action-button"
                onClick={() => handleEditPipeline(original)}
              >
                <Icons.EditAlt />
              </span>
            </Tooltip>
            <Tooltip title={t('Delete')} placement="bottom">
              <span
                role="button"
                tabIndex={0}
                className="action-button"
                onClick={() => setPipelineToDelete(original)}
              >
                <Icons.Trash />
              </span>
            </Tooltip>
          </Actions>
        ),
        Header: t('Actions'),
        id: 'actions',
        disableSortBy: true,
      },
    ],
    [handleEditPipeline, setPipelineToDelete],
  );

  return columns || [];
};

export default Columns;
