import { useState } from 'react';
import {
  Upload,
  Card,
  Row,
  Col,
  Typo<PERSON>,
  Button,
  message,
  Image,
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import axios from 'axios';
import { RcFile } from 'antd-v5/es/upload';

const { Title } = Typography;

const CustomLogoPage = () => {
  const [logoPreview, setLogoPreview] = useState(
    '/static/assets/images/superset-logo-horiz copy.png',
  );
  const [gifPreview, setGifPreview] = useState(
    '/static/assets/images/loading.gif',
  );
  const [uploading, setUploading] = useState({ logo: false, gif: false });

  const beforeUpload = (file: RcFile, type: 'logo' | 'gif') => {
    const isImage = file.type.startsWith('image/');
    const isGif = type === 'gif' ? file.type === 'image/gif' : true;
    if (!isImage || !isGif) {
      message.error(
        `Please upload ${type === 'gif' ? 'a GIF' : 'an image'} file!`,
      );
      return false;
    }

    const reader = new FileReader();
    reader.onload = () => {
      // @ts-ignore
      setLogoPreview(type === 'logo' ? reader.result : logoPreview);
      // @ts-ignore
      setGifPreview(type === 'gif' ? reader.result : gifPreview);
    };
    reader.readAsDataURL(file);
    return true;
  };

  const handleUpload = async (file: RcFile, type: 'logo' | 'loading_gif') => {
    if (!file) return;
    setUploading(prev => ({ ...prev, [type]: true }));
    try {
      const formData = new FormData();
      formData.append('logo', file);
      formData.append('upload_type', type);

      const response = await axios.post(
        `${process.env.REACT_APP_LLM_URL}/agent/upload_logo`,
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' },
          timeout: 10000,
        },
      );

      if (response.status === 200) {
        message.success(
          `${type === 'logo' ? 'Logo' : 'Loading GIF'} uploaded successfully!`,
        );
      }
    } catch (error) {
      console.error('Upload error:', error);
      message.error(
        `Failed to upload ${type === 'logo' ? 'logo' : 'GIF'}. Please try again.`,
      );
    } finally {
      setUploading(prev => ({ ...prev, [type]: false }));
    }
  };

  const handleLogoUpload = (file: any) => {
    handleUpload(file.file, 'logo');
  };

  const handleGifUpload = (file: any) => {
    handleUpload(file.file, 'loading_gif');
  };

  return (
    <div
      style={{
        padding: '24px',
        background: '#f5f5f5',
        minHeight: '100vh',
        display: 'flex',
        justifyContent: 'center',
      }}
    >
      <div style={{ maxWidth: '1200px', width: '100%' }}>
        <Title
          level={2}
          style={{
            textAlign: 'center',
            marginBottom: '32px',
            color: '#1f1f1f',
          }}
        >
          Customize Your Site Appearance
        </Title>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Card
              title="Upload Site Logo"
              bordered={false}
              style={{
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                borderRadius: '8px',
              }}
              headStyle={{
                background: '#e6f7ff',
                borderBottom: '1px solid #bae7ff',
                fontSize: '18px',
                fontWeight: 'bold',
              }}
            >
              <Upload
                showUploadList={false}
                beforeUpload={file => beforeUpload(file, 'logo')}
                accept="image/*"
                customRequest={handleLogoUpload}
                disabled={uploading.logo}
              >
                <Button
                  icon={<UploadOutlined />}
                  type="primary"
                  style={{ width: '100%', height: '40px', borderRadius: '6px' }}
                  loading={uploading.logo}
                >
                  {uploading.logo ? 'Uploading...' : 'Click to Upload Logo'}
                </Button>
              </Upload>
              {logoPreview && (
                <div style={{ marginTop: '24px', textAlign: 'center' }}>
                  <Title level={5} style={{ color: '#595959' }}>
                    Preview:
                  </Title>
                  <Image
                    src={logoPreview}
                    alt="Site Logo Preview"
                    style={{
                      height: '120px',
                      border: '1px solid #d9d9d9',
                      borderRadius: '6px',
                      padding: '8px',
                      background: '#fff',
                    }}
                    preview={false}
                  />
                </div>
              )}
            </Card>
          </Col>
          <Col xs={24} md={12}>
            <Card
              title="Upload Loading GIF"
              bordered={false}
              style={{
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                borderRadius: '8px',
              }}
              headStyle={{
                background: '#e6f7ff',
                borderBottom: '1px solid #bae7ff',
                fontSize: '18px',
                fontWeight: 'bold',
              }}
            >
              <Upload
                showUploadList={false}
                beforeUpload={file => beforeUpload(file, 'gif')}
                accept="image/gif"
                customRequest={handleGifUpload}
                disabled={uploading.gif}
              >
                <Button
                  icon={<UploadOutlined />}
                  type="primary"
                  style={{ width: '100%', height: '40px', borderRadius: '6px' }}
                  loading={uploading.gif}
                >
                  {uploading.gif
                    ? 'Uploading...'
                    : 'Click to Upload Loading GIF'}
                </Button>
              </Upload>
              {gifPreview && (
                <div style={{ marginTop: '24px', textAlign: 'center' }}>
                  <Title level={5} style={{ color: '#595959' }}>
                    Preview:
                  </Title>
                  <Image
                    src={gifPreview}
                    alt="Loading GIF Preview"
                    style={{
                      height: '120px',
                      border: '1px solid #d9d9d9',
                      borderRadius: '6px',
                      padding: '8px',
                      background: '#fff',
                    }}
                    preview={false}
                  />
                </div>
              )}
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default CustomLogoPage;
