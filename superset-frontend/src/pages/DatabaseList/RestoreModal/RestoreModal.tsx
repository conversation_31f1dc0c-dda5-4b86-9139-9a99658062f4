import { Modal, Button, Input, Upload, Typography, UploadProps } from 'antd-v5';
import { InboxOutlined, DatabaseOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onRestore?: (databaseName: string, file: File) => void;
};

const RestoreModal: React.FC<Props> = ({ isOpen, onClose, onRestore }) => {
  const [databaseName, setDatabaseName] = useState('');
  const [file, setFile] = useState<File | null>(null);
  const { Dragger } = Upload;
  const { Title, Text } = Typography;
  const [loading, setLoading] = useState(false);

  const handleDatabaseNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\s+/g, '_');
    setDatabaseName(value);
  };

  const handleRestore = async () => {
    try {
      console.log('Data Base Name', databaseName.trim());
      if (!databaseName.trim()) {
        toast.error('Please enter a database name');
        return;
      }

      if (!file) {
        toast.error('Please upload a dump file');
        return;
      }

      if (onRestore) {
        onRestore(databaseName, file);
      }

      setLoading(true);

      const formData = new FormData();

      formData.append('db_name', databaseName);
      formData.append('db_user', 'superset');
      formData.append('file', file);

      const result = await axios.post(
        `${process.env.REACT_APP_LLM_URL}/preference/restore-db`,
        formData,
      );

      if (result && result.status === 200) {
        toast.success(`Restoring database: ${databaseName}`);
      }

      onClose();
      setFile(null);
      setLoading(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
      toast.error(
        `${error.response?.data?.error || 'Error While Uploading File Upload'} `,
      );
    }
  };

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.dump',
    beforeUpload: (file: File) => {
      setFile(file);
      return false;
    },
    maxCount: 1,
    onRemove: () => {
      setFile(null);
    },
  };

  return (
    <Modal
      title={
        <Title level={3} style={{ padding: 8 }}>
          <DatabaseOutlined /> Restore Database
        </Title>
      }
      open={isOpen}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          Cancel
        </Button>,
        <Button
          key="restore"
          type="primary"
          onClick={handleRestore}
          disabled={!databaseName.trim() || !file}
          loading={loading}
        >
          Restore Database
        </Button>,
      ]}
      width={500}
      centered
      styles={{
        content: { backgroundColor: '#FFF' },
        header: { backgroundColor: '#F7F7F7', marginTop: 20 },
        mask: { backgroundColor: 'rgba(0, 0, 0, 0.45)' },
      }}
    >
      <div style={{ padding: '10px 0' }}>
        <Text strong>Database Name</Text>
        <Input
          placeholder="Enter database name"
          prefix={<DatabaseOutlined />}
          value={databaseName}
          onChange={handleDatabaseNameChange}
          style={{ marginBottom: 16, marginTop: 8, backgroundColor: '#FFF' }}
        />

        <Text strong>Upload Dump File</Text>
        <Dragger {...uploadProps} style={{ marginTop: 8 }}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">
            Click or drag dump file to this area to upload
          </p>
          <p className="ant-upload-hint">Support for only .dump file</p>
        </Dragger>

        {file && (
          <div style={{ marginTop: 16 }}>
            <Text type="success">Selected file: {file.name}</Text>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default RestoreModal;
