{"name": "superset", "version": "0.0.0-dev", "description": "Superset is a data exploration platform designed to be visual, intuitive, and interactive.", "keywords": ["big", "data", "exploratory", "analysis", "react", "d3", "airbnb", "nerds", "database", "flask"], "homepage": "https://superset.apache.org/", "bugs": {"url": "https://github.com/apache/superset/issues"}, "repository": {"type": "git", "url": "https://github.com/apache/superset.git", "directory": "superset-frontend"}, "license": "Apache-2.0", "author": {"name": "Apache"}, "directories": {"doc": "docs", "test": "spec"}, "workspaces": ["packages/*", "plugins/*", "src/setup/*"], "scripts": {"_prettier": "prettier './({src,spec,cypress-base,plugins,packages,.storybook}/**/*{.js,.jsx,.ts,.tsx,.css,.less,.scss,.sass}|package.json)'", "build": "cross-env NODE_OPTIONS=--max_old_space_size=8192 NODE_ENV=production BABEL_ENV=\"${BABEL_ENV:=production}\" webpack --color --mode production", "build-dev": "cross-env NODE_OPTIONS=--max_old_space_size=8192 NODE_ENV=development webpack --mode=development --color", "build-instrumented": "cross-env NODE_ENV=production BABEL_ENV=instrumented webpack --mode=production --color", "build-storybook": "storybook build", "build-translation": "scripts/po2json.sh", "bundle-stats": "cross-env BUNDLE_ANALYZER=true npm run build && npx open-cli ../superset/static/stats/statistics.html", "core:cover": "cross-env NODE_ENV=test NODE_OPTIONS=\"--max-old-space-size=4096\" jest --coverage --coverageThreshold='{\"global\":{\"statements\":100,\"branches\":100,\"functions\":100,\"lines\":100}}' --collectCoverageFrom='[\"packages/**/src/**/*.{js,ts}\", \"!packages/superset-ui-demo/**/*\"]' packages", "cover": "cross-env NODE_ENV=test NODE_OPTIONS=\"--max-old-space-size=4096\" jest --coverage", "dev": "webpack --mode=development --color --watch", "dev-server": "cross-env NODE_ENV=development BABEL_ENV=development node --max_old_space_size=4096 ./node_modules/webpack-dev-server/bin/webpack-dev-server.js --mode=development", "eslint": "eslint --ignore-path=.eslint<PERSON>ore --ext .js,.jsx,.ts,tsx --quiet", "format": "npm run _prettier -- --write", "lint": "npm run eslint -- . && npm run type", "lint-fix": "npm run eslint -- . --fix", "lint-stats": "eslint -f ./scripts/eslint-metrics-uploader.js --ignore-path=.eslintignore --ext .js,.jsx,.ts,.tsx . ", "plugins:build": "node ./scripts/build.js", "plugins:build-assets": "node ./scripts/copyAssets.js", "plugins:build-storybook": "cd packages/superset-ui-demo && npm run build-storybook", "plugins:create-conventional-version": "npm run prune && lerna version --conventional-commits --create-release github --no-private --yes --tag-version-prefix=\"plugins-and-packages-v\"", "plugins:create-minor-version": "npm run prune && lerna version minor --no-private --yes --tag-version-prefix=\"plugins-and-packages-v\"", "plugins:create-patch-version": "npm run prune && lerna version patch --no-private --yes --tag-version-prefix=\"plugins-and-packages-v\"", "plugins:publish-all": "npm run prune && npm run plugins:build && lerna publish from-package --force-publish --yes", "plugins:release-conventional": "npm run prune && npm run plugins:build && lerna publish --conventional-commits --create-release github --yes", "plugins:release-from-tag": "npm run prune && npm run plugins:build && lerna publish from-package --yes", "plugins:storybook": "cd packages/superset-ui-demo && npm run storybook", "prettier": "npm run _prettier -- --write", "prettier-check": "npm run _prettier -- --check", "prod": "npm run build", "prune": "rm -rf ./{packages,plugins}/*/{node_modules,lib,esm,tsconfig.tsbuildinfo,package-lock.json} ./.temp_cache", "storybook": "cross-env NODE_ENV=development BABEL_ENV=development storybook dev -p 6006", "tdd": "cross-env NODE_ENV=test NODE_OPTIONS=\"--max-old-space-size=8192\" jest --watch", "test": "cross-env NODE_ENV=test NODE_OPTIONS=\"--max-old-space-size=8192\" jest --max-workers=80% --silent", "type": "tsc --noEmit", "update-maps": "jupyter nbconvert --to notebook --execute --inplace 'plugins/legacy-plugin-chart-country-map/scripts/Country Map GeoJSON Generator.ipynb' -Xfrozen_modules=off", "validate-release": "../RELEASING/validate_this_release.sh"}, "browserslist": ["last 3 chrome versions", "last 3 firefox versions", "last 3 safari versions", "last 3 edge versions"], "dependencies": {"@ant-design/icons": "^5.2.6", "@deck.gl/widgets": "^9.1.12", "@emotion/cache": "^11.4.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.3.0", "@fontsource/fira-code": "^5.0.18", "@fontsource/inter": "^5.0.20", "@react-spring/web": "^9.7.5", "@reduxjs/toolkit": "^1.9.3", "@rjsf/core": "^5.21.1", "@rjsf/utils": "^5.24.3", "@rjsf/validator-ajv8": "^5.22.3", "@scarf/scarf": "^1.4.0", "@superset-ui/chart-controls": "file:./packages/superset-ui-chart-controls", "@superset-ui/core": "file:./packages/superset-ui-core", "@superset-ui/legacy-plugin-chart-calendar": "file:./plugins/legacy-plugin-chart-calendar", "@superset-ui/legacy-plugin-chart-chord": "file:./plugins/legacy-plugin-chart-chord", "@superset-ui/legacy-plugin-chart-country-map": "file:./plugins/legacy-plugin-chart-country-map", "@superset-ui/legacy-plugin-chart-horizon": "file:./plugins/legacy-plugin-chart-horizon", "@superset-ui/legacy-plugin-chart-map-box": "file:./plugins/legacy-plugin-chart-map-box", "@superset-ui/legacy-plugin-chart-paired-t-test": "file:./plugins/legacy-plugin-chart-paired-t-test", "@superset-ui/legacy-plugin-chart-parallel-coordinates": "file:./plugins/legacy-plugin-chart-parallel-coordinates", "@superset-ui/legacy-plugin-chart-partition": "file:./plugins/legacy-plugin-chart-partition", "@superset-ui/legacy-plugin-chart-rose": "file:./plugins/legacy-plugin-chart-rose", "@superset-ui/legacy-plugin-chart-world-map": "file:./plugins/legacy-plugin-chart-world-map", "@superset-ui/legacy-preset-chart-deckgl": "file:./plugins/legacy-preset-chart-deckgl", "@superset-ui/legacy-preset-chart-nvd3": "file:./plugins/legacy-preset-chart-nvd3", "@superset-ui/plugin-chart-cartodiagram": "file:./plugins/plugin-chart-cartodiagram", "@superset-ui/plugin-chart-echarts": "file:./plugins/plugin-chart-echarts", "@superset-ui/plugin-chart-handlebars": "file:./plugins/plugin-chart-handlebars", "@superset-ui/plugin-chart-pivot-table": "file:./plugins/plugin-chart-pivot-table", "@superset-ui/plugin-chart-table": "file:./plugins/plugin-chart-table", "@superset-ui/plugin-chart-word-cloud": "file:./plugins/plugin-chart-word-cloud", "@superset-ui/switchboard": "file:./packages/superset-ui-switchboard", "@types/d3-format": "^3.0.1", "@types/d3-time-format": "^4.0.3", "@visx/axis": "^3.8.0", "@visx/grid": "^3.5.0", "@visx/responsive": "^3.0.0", "@visx/scale": "^3.5.0", "@visx/tooltip": "^3.0.0", "@visx/xychart": "^3.5.1", "abortcontroller-polyfill": "^1.7.8", "ace-builds": "^1.36.3", "ag-grid-community": "33.1.1", "ag-grid-react": "33.1.1", "antd": "4.10.3", "antd-v5": "npm:antd@^5.18.0", "bootstrap": "^3.4.1", "brace": "^0.11.1", "chrono-node": "^2.7.8", "classnames": "^2.2.5", "core-js": "^3.38.1", "d3-color": "^3.1.0", "d3-scale": "^2.1.2", "dayjs": "^1.11.13", "dom-to-image-more": "^3.2.0", "dom-to-pdf": "^0.3.2", "echarts": "^5.6.0", "emotion-rgba": "0.0.12", "eslint-plugin-i18n-strings": "file:eslint-rules/eslint-plugin-i18n-strings", "fast-glob": "^3.3.2", "fs-extra": "^11.2.0", "fuse.js": "^7.0.0", "geolib": "^2.0.24", "geostyler": "^12.0.2", "geostyler-data": "^1.0.0", "geostyler-openlayers-parser": "^4.3.0", "geostyler-style": "^7.5.0", "geostyler-wfs-parser": "^2.0.3", "global-box": "^2.0.2", "googleapis": "^130.0.0", "http-proxy-middleware": "^3.0.3", "immer": "^10.1.1", "interweave": "^13.1.0", "jquery": "^3.7.1", "js-levenshtein": "^1.1.6", "js-yaml-loader": "^1.2.2", "json-bigint": "^1.0.0", "json-stringify-pretty-compact": "^2.0.0", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "luxon": "^3.5.0", "mammoth": "^1.9.0", "mapbox-gl": "^2.10.0", "markdown-to-jsx": "^7.7.4", "match-sorter": "^6.3.4", "memoize-one": "^5.2.1", "mousetrap": "^1.6.5", "mustache": "^4.2.0", "nanoid": "^5.0.9", "ol": "^7.5.2", "papaparse": "^5.5.2", "polished": "^4.3.1", "prop-types": "^15.8.1", "query-string": "^6.13.7", "rc-trigger": "^5.3.4", "re-resizable": "^6.10.1", "react": "^17.0.2", "react-ace": "^10.1.0", "react-chartjs-2": "^5.3.0", "react-checkbox-tree": "^1.8.0", "react-color": "^2.13.8", "react-diff-viewer-continued": "^3.4.0", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^11.1.3", "react-dom": "^17.0.2", "react-draggable": "^4.4.6", "react-hot-loader": "^4.13.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-intersection-observer": "^9.10.2", "react-js-cron": "^2.1.2", "react-json-tree": "^0.17.0", "react-lines-ellipsis": "^0.15.4", "react-loadable": "^5.5.0", "react-markdown": "^9.0.0", "react-reader": "^2.0.13", "react-redux": "^7.2.9", "react-resize-detector": "^7.1.2", "react-reverse-portal": "^2.1.2", "react-router-dom": "^5.3.4", "react-search-input": "^0.11.3", "react-sortable-hoc": "^2.0.0", "react-speech-recognition": "^3.10.0", "react-split": "^2.0.9", "react-syntax-highlighter": "^15.4.5", "react-table": "^7.8.0", "react-transition-group": "^4.4.5", "react-type-animation": "^3.2.0", "react-ultimate-pagination": "^1.3.2", "react-virtualized-auto-sizer": "^1.0.25", "react-window": "^1.8.10", "reactflow": "^11.11.4", "redux": "^4.2.1", "redux-localstorage": "^0.4.1", "redux-thunk": "^2.1.0", "redux-undo": "^1.0.0-beta9-9-7", "regenerator-runtime": "^0.14.1", "rimraf": "^6.0.1", "rison": "^0.1.1", "scroll-into-view-if-needed": "^3.1.0", "simple-zstd": "^1.4.2", "tinycolor2": "^1.4.2", "urijs": "^1.19.8", "use-event-callback": "^0.1.0", "use-immer": "^0.9.0", "use-query-params": "^1.1.9", "yargs": "^17.7.2"}, "devDependencies": {"@applitools/eyes-storybook": "^3.50.9", "@babel/cli": "^7.22.6", "@babel/compat-data": "^7.22.6", "@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.25.9", "@babel/node": "^7.22.6", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/plugin-transform-runtime": "^7.25.9", "@babel/preset-env": "^7.26.7", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@babel/register": "^7.23.7", "@babel/runtime": "^7.26.0", "@babel/runtime-corejs3": "^7.26.0", "@babel/types": "^7.26.9", "@cypress/react": "^8.0.2", "@emotion/babel-plugin": "^11.13.5", "@emotion/jest": "^11.13.0", "@hot-loader/react-dom": "^17.0.2", "@istanbuljs/nyc-config-typescript": "^1.0.1", "@mihkeleidast/storybook-addon-source": "^1.0.1", "@storybook/addon-actions": "8.1.11", "@storybook/addon-controls": "8.1.11", "@storybook/addon-essentials": "8.1.11", "@storybook/addon-links": "8.1.11", "@storybook/addon-mdx-gfm": "8.1.11", "@storybook/components": "8.1.11", "@storybook/preview-api": "8.1.11", "@storybook/react": "8.1.11", "@storybook/react-webpack5": "8.1.11", "@svgr/webpack": "^8.1.0", "@testing-library/dom": "^8.20.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^12.1.5", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^12.8.3", "@types/classnames": "^2.2.10", "@types/dom-to-image": "^2.6.7", "@types/enzyme": "^3.10.18", "@types/fetch-mock": "^7.3.2", "@types/jest": "^29.5.12", "@types/jquery": "^3.5.8", "@types/js-levenshtein": "^1.1.3", "@types/json-bigint": "^1.0.4", "@types/math-expression-evaluator": "^1.3.3", "@types/mousetrap": "^1.6.15", "@types/papaparse": "^5.3.15", "@types/react": "^17.0.83", "@types/react-dom": "^17.0.26", "@types/react-gravatar": "^2.6.14", "@types/react-json-tree": "^0.6.11", "@types/react-loadable": "^5.5.11", "@types/react-redux": "^7.1.10", "@types/react-resizable": "^3.0.8", "@types/react-router-dom": "^5.3.3", "@types/react-speech-recognition": "^3.9.5", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-table": "^7.7.20", "@types/react-transition-group": "^4.4.12", "@types/react-ultimate-pagination": "^1.2.4", "@types/react-virtualized-auto-sizer": "^1.0.4", "@types/react-window": "^1.8.8", "@types/redux-localstorage": "^1.0.8", "@types/redux-mock-store": "^1.0.6", "@types/rison": "0.1.0", "@types/sinon": "^17.0.3", "@types/tinycolor2": "^1.4.3", "@types/yargs": "12 - 18", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@wojtekmaj/enzyme-adapter-react-17": "^0.8.0", "autoprefixer": "^10.4.20", "babel-jest": "^29.7.0", "babel-loader": "^9.1.3", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-jsx-remove-data-test-id": "^3.0.0", "babel-plugin-lodash": "^3.3.4", "babel-plugin-typescript-to-proptypes": "^2.0.0", "cheerio": "1.0.0-rc.10", "copy-webpack-plugin": "^12.0.2", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.0", "enzyme": "^3.11.0", "enzyme-matchers": "^7.1.2", "eslint": "^8.56.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^7.2.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-cypress": "^3.6.0", "eslint-plugin-file-progress": "^1.5.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-jest": "^27.8.0", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-lodash": "^7.4.0", "eslint-plugin-no-only-tests": "^3.3.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-prefer-function-component": "^3.3.0", "eslint-plugin-storybook": "^0.8.0", "eslint-plugin-testing-library": "^6.4.0", "eslint-plugin-theme-colors": "file:eslint-rules/eslint-plugin-theme-colors", "fetch-mock": "^9.1.0", "fork-ts-checker-webpack-plugin": "^9.0.2", "history": "^5.3.0", "html-webpack-plugin": "^5.6.3", "imports-loader": "^5.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-html-reporter": "^3.10.2", "jest-websocket-mock": "^2.5.0", "jsdom": "^26.0.0", "lerna": "^8.1.7", "less": "^4.2.0", "less-loader": "^12.2.0", "mini-css-extract-plugin": "^2.9.0", "open-cli": "^8.0.0", "po2json": "^0.4.5", "postcss": "^8.4.47", "prettier": "3.3.3", "prettier-plugin-packagejson": "^2.5.3", "process": "^0.11.10", "react-resizable": "^3.0.5", "redux-mock-store": "^1.5.4", "sinon": "^18.0.0", "source-map": "^0.7.4", "source-map-support": "^0.5.21", "speed-measure-webpack-plugin": "^1.5.0", "storybook": "8.1.11", "style-loader": "^4.0.0", "tailwindcss": "^3.4.14", "thread-loader": "^4.0.4", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "tscw-config": "^1.1.2", "typescript": "5.1.6", "vm-browserify": "^1.1.2", "webpack": "^5.98.0", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.15.1", "webpack-manifest-plugin": "^5.0.0", "webpack-sources": "^3.2.3", "webpack-visualizer-plugin2": "^1.1.0"}, "engines": {"node": "^20.16.0", "npm": "^10.8.1"}, "overrides": {"core-js": "^3.38.1", "d3-color": "^3.1.0", "puppeteer": "^22.4.1", "underscore": "^1.13.7"}, "readme": "ERROR: No README data found!", "scarfSettings": {"allowTopLevel": true}, "_id": "superset@0.0.0-dev"}