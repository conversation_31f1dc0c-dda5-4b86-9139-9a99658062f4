/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
import { useEffect, useMemo, useState } from 'react';
import rehypeSanitize, { defaultSchema } from 'rehype-sanitize';
import remarkGfm from 'remark-gfm';
import { mergeWith } from 'lodash';
import { FeatureFlag, isFeatureEnabled } from '../utils';
import { ImageEditModal } from './ImageEditModal';
import { ImageWithControls } from './ImageControls';
import { Button } from 'antd-v5';
import { PlusOutlined } from '@ant-design/icons';
import toast from 'react-hot-toast';

interface SafeMarkdownProps {
  source: string;
  htmlSanitization?: boolean;
  htmlSchemaOverrides?: typeof defaultSchema;
  onSourceUpdate?: (newSource: string) => void;
  isEditing?: boolean;
}
interface ImageProps {
  src: string;
  alt: string;
  height: number | null;
  width: number | null;
  margin: number;
  align: 'left' | 'right';
  isNextLine: boolean;
}

export function getOverrideHtmlSchema(
  originalSchema: typeof defaultSchema,
  htmlSchemaOverrides: SafeMarkdownProps['htmlSchemaOverrides'],
) {
  // Create a base schema that includes style attributes for all HTML elements
  const baseSchema = {
    ...originalSchema,
    attributes: {
      ...originalSchema.attributes,
      '*': [...(originalSchema.attributes?.['*'] || []), 'style', 'class'],
    },
  };

  return mergeWith(baseSchema, htmlSchemaOverrides, (objValue, srcValue) =>
    Array.isArray(objValue) ? objValue.concat(srcValue) : undefined,
  );
}

type OpenState = {
  type: 'edit' | 'add' | null;
  value: boolean;
};

function SafeMarkdown({
  source,
  htmlSanitization = true,
  htmlSchemaOverrides = {},
  onSourceUpdate,
  isEditing,
}: SafeMarkdownProps) {
  const escapeHtml = isFeatureEnabled(FeatureFlag.EscapeMarkdownHtml);
  const [rehypeRawPlugin, setRehypeRawPlugin] = useState<any>(null);
  const [ReactMarkdown, setReactMarkdown] = useState<any>(null);
  const [open, setOpen] = useState<OpenState>({ type: null, value: false });
  const [editingImage, setEditingImage] = useState<ImageProps | null>(null);

  useEffect(() => {
    Promise.all([import('rehype-raw'), import('react-markdown')]).then(
      ([rehypeRaw, ReactMarkdown]) => {
        setRehypeRawPlugin(() => rehypeRaw.default);
        setReactMarkdown(() => ReactMarkdown.default);
      },
    );
  }, []);

  const handleDeleteImage = (
    e: React.MouseEvent<HTMLButtonElement>,
    img: ImageProps,
  ) => {
    e.stopPropagation();
    if (!onSourceUpdate) return;
    const { src, alt } = img;
    const escape = (str: string) => str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    const patterns = [
      new RegExp(
        `<img\\s+[^>]*src=["']${escape(src)}["'][^>]*alt=["']${escape(alt)}["'][^>]*>`,
        'g',
      ),
      new RegExp(`!\\[${escape(alt)}\\]\\(${escape(src)}\\)`, 'g'),
      new RegExp(`<img\\s+[^>]*src=["']${escape(src)}["'][^>]*>`, 'g'),
    ];

    let newSource = source;
    for (const regex of patterns) {
      if (regex.test(newSource)) {
        newSource = newSource.replace(regex, '');
        break;
      }
    }

    onSourceUpdate(newSource);
  };

  const handleOk = ({
    alt: newAlt,
    height,
    width,
    imageUrl,
    align,
    margin,
    isNextLine,
  }: {
    alt: string;
    height: number | null;
    width: number | null;
    imageUrl: string | null;
    align: 'left' | 'right';
    margin: number;
    isNextLine: boolean;
  }) => {
    if (!editingImage || !onSourceUpdate) {
      setOpen({ type: null, value: false });
      return;
    }

    if (
      (open.type === 'add' && height === null && width === null) ||
      (open.type === 'edit' && height === null && width === null)
    ) {
      toast.error('Height and width must be specified');
      return;
    }

    if (open.type === 'add' && !imageUrl) {
      toast.error('Image must be uploaded');
      return;
    }

    const { src, alt } = editingImage;

    const newImageHtml = imageUrl
      ? `${isNextLine ? '<br/> \n <br/> \n' : ''} <img src="${process.env.REACT_APP_LLM_URL}agent/preview-image?file_path=${imageUrl}"   alt="${newAlt || alt}"${height ? ` height="${height}"` : ''}${width ? ` width="${width}"` : ''} ${align ? `align="${align}"` : ''}  style="margin: ${margin}px !important;" />`
      : `${isNextLine ? '<br/> \n <br/> \n' : ''} <img src="${src}"   alt="${newAlt || alt}"${height ? ` height="${height}"` : ''}${width ? ` width="${width}"` : ''} ${align ? `align="${align}"` : ''}  style="margin: ${margin}px !important;" />`;

    const imgHtmlRegex = new RegExp(
      `<img\\s+[^>]*src=["']${src.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}["'][^>]*alt=["']${alt.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}["'][^>]*>`,
      'g',
    );

    const imgMarkdownRegex = new RegExp(
      `!\\[${alt.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\]\\(${src.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\)`,
      'g',
    );

    const imgSrcOnlyRegex = new RegExp(
      `<img\\s+[^>]*src=["']${src.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}["'][^>]*>`,
      'g',
    );

    let newSource = source;

    if (imgHtmlRegex.test(source)) {
      newSource = source.replace(imgHtmlRegex, newImageHtml);
    } else if (imgMarkdownRegex.test(source)) {
      newSource = source.replace(imgMarkdownRegex, newImageHtml);
    } else if (imgSrcOnlyRegex.test(source)) {
      newSource = source.replace(imgSrcOnlyRegex, newImageHtml);
    } else {
      newSource = source + '\n' + newImageHtml;
    }

    onSourceUpdate(newSource);

    setOpen({ type: null, value: false });
    setEditingImage(null);
  };

  const handleEditImage = (
    e: React.MouseEvent<HTMLButtonElement>,
    img: ImageProps,
  ) => {
    e.stopPropagation();
    setEditingImage(img);
    setOpen({ type: 'edit', value: true });
  };

  const handleAddImage = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setEditingImage({
      src: '',
      alt: '',
      height: null,
      width: null,
      margin: 0,
      align: 'left',
      isNextLine: false,
    });
    setOpen({ type: 'add', value: true });
  };

  const handleCloseImageModel = () => {
    setOpen({ type: null, value: false });
    setEditingImage(null);
  };

  const components = {
    img: ({ src, alt, ...props }: any) => (
      <ImageWithControls
        src={src}
        alt={alt}
        align={props.node.properties.align}
        {...props}
        isEditing={isEditing}
        onEdit={e =>
          handleEditImage(e, {
            src,
            alt,
            height: props.node.properties.height,
            width: props.node.properties.width,
            margin: props.node.properties.margin,
            align: props.node.properties.align,
            isNextLine: props.node.properties.isNextLine,
          })
        }
        onDelete={e =>
          handleDeleteImage(e, {
            src,
            alt,
            height: props.node.properties.height,
            width: props.node.properties.width,
            margin: props.node.properties.margin,
            align: props.node.properties.align,
            isNextLine: props.node.properties.isNextLine,
          })
        }
      />
    ),
  };

  const rehypePlugins = useMemo(() => {
    const rehypePlugins: any = [];
    if (!escapeHtml && rehypeRawPlugin) {
      rehypePlugins.push(rehypeRawPlugin);
      if (htmlSanitization) {
        const schema = getOverrideHtmlSchema(
          defaultSchema,
          htmlSchemaOverrides,
        );
        rehypePlugins.push([rehypeSanitize, schema]);
      }
    }
    return rehypePlugins;
  }, [escapeHtml, htmlSanitization, htmlSchemaOverrides, rehypeRawPlugin]);

  if (!ReactMarkdown || !rehypeRawPlugin) {
    return null;
  }

  return (
    <>
      {isEditing && (
        <div style={{ textAlign: 'right', margin: 12 }}>
          <Button
            type="default"
            icon={<PlusOutlined />}
            onClick={handleAddImage}
          >
            Add Image
          </Button>
        </div>
      )}
      <ReactMarkdown
        rehypePlugins={rehypePlugins}
        remarkPlugins={[remarkGfm]}
        skipHtml={false}
        transformLinkUri={null}
        components={components}
      >
        {source}
      </ReactMarkdown>

      <ImageEditModal
        visible={open.value}
        image={editingImage}
        onSubmit={handleOk}
        onCancel={handleCloseImageModel}
        type={open.type}
      />
    </>
  );
}

export default SafeMarkdown;
