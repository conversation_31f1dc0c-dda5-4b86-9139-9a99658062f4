// import React from 'react';
// import { Button } from 'antd-v5';
// import { IoMdResize } from 'react-icons/io';
// import { MdDelete } from 'react-icons/md';
// import { RiImageEditFill } from 'react-icons/ri';

// interface Props extends React.ImgHTMLAttributes<HTMLImageElement> {
//   isEditing?: boolean;
//   onEdit?: React.MouseEventHandler<HTMLButtonElement>;
//   onResize?: React.MouseEventHandler<HTMLButtonElement>;
//   onDelete?: React.MouseEventHandler<HTMLButtonElement>;
// }

// export const ImageWithControls = ({
//   isEditing,
//   onEdit,
//   onResize,
//   onDelete,
//   style,
//   ...imgProps
// }: Props) => {
//   const buttons = [
//     { icon: <IoMdResize />, onClick: onResize },
//     { icon: <RiImageEditFill />, onClick: onEdit },
//     { icon: <MdDelete />, onClick: onDelete },
//   ];

//   return (
//     <span
//       style={{
//         position: 'relative',
//         verticalAlign: 'top',
//         ...style,
//       }}
//       className="image-controls-9981"
//     >
//       <img {...imgProps} />
//       {isEditing && (
//         <span
//           style={{
//             position: 'absolute',
//             top: -20,
//             right: -20,
//             zIndex: 1000,
//             backgroundColor: 'white',
//             border: '1px dashed #20a7c9',
//             borderRadius: '5px',
//             display: 'flex',
//             flexDirection: 'column',
//             gap: '5px',
//             padding: '5px',
//           }}
//         >
//           {buttons.map((button, index) => (
//             <Button
//               key={index}
//               icon={button.icon}
//               size="small"
//               onClick={button.onClick}
//             />
//           ))}
//         </span>
//       )}
//     </span>
//   );
// };

import React from 'react';
import { Button } from 'antd-v5';
import { MdDelete } from 'react-icons/md';
import { RiImageEditFill } from 'react-icons/ri';

interface Props extends React.ImgHTMLAttributes<HTMLImageElement> {
  isEditing?: boolean;
  onEdit?: React.MouseEventHandler<HTMLButtonElement>;
  onDelete?: React.MouseEventHandler<HTMLButtonElement>;
  align?: 'left' | 'right';
}

export const ImageWithControls = ({
  isEditing,
  onEdit,
  onDelete,
  align,
  ...imgProps
}: Props) => {
  const buttons = [
    { icon: <RiImageEditFill />, onClick: onEdit },
    { icon: <MdDelete />, onClick: onDelete },
  ];

  return (
    <span
      style={{
        position: 'relative',
        width: 'fit-content',
        display: 'inline-block',
        float: align === 'right' ? 'right' : 'left',
      }}
      className="image-controls-9981"
    >
      <img {...imgProps} />
      {isEditing && (
        <div
          style={{
            position: 'absolute',
            top: -20,
            right: -20,
            zIndex: 1000,
            backgroundColor: 'white',
            border: '1px dashed #20a7c9',
            borderRadius: '5px',
            display: 'flex',
            flexDirection: 'column',
            gap: '5px',
            padding: '5px',
          }}
        >
          {buttons.map((button, index) => (
            <Button
              key={index}
              icon={button.icon}
              size="small"
              onClick={button.onClick}
            />
          ))}
        </div>
      )}
    </span>
  );
};
