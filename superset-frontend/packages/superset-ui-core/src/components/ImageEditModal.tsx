import {
  Modal,
  Input,
  InputNumber,
  Form,
  Upload,
  Button,
  UploadProps,
  Select,
  UploadFile,
} from 'antd-v5';
import { useState, useEffect } from 'react';
import { UploadOutlined } from '@ant-design/icons';
import toast from 'react-hot-toast';
import Checkbox from 'antd-v5/es/checkbox/Checkbox';

interface Props {
  visible: boolean;
  image: {
    src: string;
    alt: string;
    height: number | null;
    width: number | null;
    align: 'left' | 'right';
  } | null;
  onSubmit: ({
    alt,
    height,
    width,
    imageUrl,
    align,
    margin,
    isNextLine,
  }: {
    alt: string;
    height: number | null;
    width: number | null;
    imageUrl: string | null;
    align: 'left' | 'right';
    margin: number;
    isNextLine: boolean;
  }) => void;
  onCancel: () => void;
  type: 'edit' | 'add' | null;
}

export const ImageEditModal = ({
  visible,
  image,
  onSubmit,
  onCancel,
  type,
}: Props) => {
  const [imageState, setImageState] = useState({
    alt: '',
    width: null as number | null,
    height: null as number | null,
    imageUrl: null as string | null,
    loading: false,
    align: 'left' as 'left' | 'right',
    margin: 0,
    isNextLine: false,
    fileList: [] as UploadFile<any>[],
  });

  const { alt, width, height, imageUrl, loading, align, margin, isNextLine } =
    imageState;

  const setAlt = (value: string) =>
    setImageState(prev => ({ ...prev, alt: value }));
  const setWidth = (value: number | null) =>
    setImageState(prev => ({ ...prev, width: value }));
  const setHeight = (value: number | null) =>
    setImageState(prev => ({ ...prev, height: value }));
  const setImageUrl = (value: string | null) =>
    setImageState(prev => ({ ...prev, imageUrl: value }));
  const setLoading = (value: boolean) =>
    setImageState(prev => ({ ...prev, loading: value }));
  const setAlign = (value: 'left' | 'right') =>
    setImageState(prev => ({ ...prev, align: value }));
  const setIsNextLine = (value: boolean) =>
    setImageState(prev => ({ ...prev, isNextLine: value }));
  // @ts-ignore
  const setMargin = (value: number) =>
    setImageState(prev => ({ ...prev, margin: value }));

  useEffect(() => {
    if (image) {
      setAlt(image.alt);
      setWidth(image.width);
      setHeight(image.height);
      setAlign(image.align);
    }
  }, [image]);

  const uploadProps: UploadProps = {
    name: 'image',
    action: `${process.env.REACT_APP_LLM_URL}agent/image_upload`,
    accept: 'image/jpg, image/png , image/gif, image/jpeg',
    style: { height: '100%', background: 'transparent' },
    fileList: imageState.fileList,
    onRemove() {
      setImageUrl(null);
      setImageState(prev => ({ ...prev, fileList: [] }));
      return true;
    },
  };

  const handleChange: UploadProps['onChange'] = info => {
    setImageState(prev => ({
      ...prev,
      fileList: info.fileList,
    }));

    if (info.file.status === 'uploading') {
      setLoading(true);
      return;
    }

    if (info.file.status === 'done') {
      const uploadedImageUrl = info.file.response?.file_path;

      if (uploadedImageUrl) {
        setLoading(false);
        setImageUrl(uploadedImageUrl);
      } else {
        toast.error('Failed to upload image');
      }
    }

    if (info.file.status === 'error') {
      setLoading(false);
      toast.error('Upload failed');
    }
  };

  const handleOk = () =>
    onSubmit({
      alt,
      height,
      width,
      imageUrl,
      align,
      margin,
      isNextLine,
    });
  const handleCancel = () => {
    setImageState({
      alt: '',
      width: null,
      height: null,
      imageUrl: null,
      loading: false,
      align: 'left',
      isNextLine: false,
      fileList: [],
      margin: 0,
    });
    onCancel();
  };

  const title = type === 'edit' ? 'Edit Image' : 'Add Image';

  const handleSubmit = () => {
    handleOk();
    if (!visible) {
      handleCancel();
    }
  };

  return (
    <Modal
      open={visible}
      title={title}
      onOk={handleSubmit}
      onCancel={handleCancel}
      centered
      styles={{
        header: { backgroundColor: 'white' },
      }}
    >
      <Form layout="vertical">
        <Form.Item label="Image">
          <Upload {...uploadProps} onChange={handleChange}>
            <Button
              icon={<UploadOutlined />}
              loading={loading}
              disabled={!!imageUrl}
            >
              Click to Upload
            </Button>
          </Upload>
        </Form.Item>
        <Form.Item label="Align">
          <Select
            options={[
              { value: 'left', label: 'Left' },
              { value: 'right', label: 'Right' },
            ]}
            variant="outlined"
            value={align}
            onChange={value => setAlign(value as 'left' | 'right')}
            style={{
              backgroundColor: 'white',
            }}
            dropdownStyle={{
              backgroundColor: 'white',
            }}
          />
        </Form.Item>

        <Form.Item label="Alt Text">
          <Input
            value={alt}
            onChange={e => setAlt(e.target.value)}
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item label="Height (px)">
          <InputNumber
            min={1}
            value={height ?? undefined}
            onChange={value => setHeight(value ?? null)}
            style={{ width: '100%' }}
          />
        </Form.Item>
        <Form.Item label="Width (px)">
          <InputNumber
            min={1}
            value={width ?? undefined}
            onChange={value => setWidth(value ?? null)}
            style={{ width: '100%' }}
          />
        </Form.Item>
        {/* <Form.Item label="Margin (px)">
          <InputNumber
            value={margin ?? undefined}
            onChange={value => setMargin(value ?? 0)}
            style={{ width: '100%' }}
          />
        </Form.Item> */}

        <Checkbox
          checked={isNextLine}
          onChange={e => setIsNextLine(e.target.checked)}
        >
          Add image to the next line.
        </Checkbox>
      </Form>
    </Modal>
  );
};
