"""
Task Manager for Data Pipeline System

This module provides a unified interface for managing pipeline tasks,
automatically selecting between Celery (if Redis is available) or 
Simple Threading scheduler as fallback.

Functions:
    - start_pipeline: Start a pipeline with specified frequency
    - stop_pipeline: Stop a specific pipeline
    - stop_all_pipelines: Stop all running pipelines
    - get_pipeline_status: Get status of pipeline tasks
    
Variables:
    - SCHEDULER_TYPE: Current scheduler being used ("celery" or "simple")
    - REDIS_AVAILABLE: Boolean indicating if Redis/Celery is available
"""

import os
import redis
from typing import Dict, Any, Optional
from dotenv import load_dotenv

load_dotenv()

# Check Redis availability
REDIS_AVAILABLE = False
SCHEDULER_TYPE = "simple"

try:
    # Try to connect to Redis
    redis_host = os.getenv("REDIS_HOST", "localhost")
    redis_port = int(os.getenv("REDIS_PORT", 6379))
    redis_db = int(os.getenv("REDIS_DB", 0))
    
    redis_client = redis.Redis(
        host=redis_host,
        port=redis_port,
        db=redis_db,
        socket_connect_timeout=2,
        socket_timeout=2
    )
    
    # Test Redis connection
    redis_client.ping()
    REDIS_AVAILABLE = True
    SCHEDULER_TYPE = "celery"
    print(f"[TASK_MANAGER] Redis available at {redis_host}:{redis_port}, using Celery scheduler")
    
except Exception as e:
    print(f"[TASK_MANAGER] Redis not available ({e}), falling back to simple scheduler")
    REDIS_AVAILABLE = False
    SCHEDULER_TYPE = "simple"


def start_pipeline(pipeline_id: int, frequency: int = 60) -> Dict[str, Any]:
    """
    Start a pipeline with specified frequency.
    
    Args:
        pipeline_id (int): ID of the pipeline to start
        frequency (int): Frequency in seconds for data fetching (default: 60)
        
    Returns:
        Dict containing success status, task_id, and message
    """
    try:
        if SCHEDULER_TYPE == "celery" and REDIS_AVAILABLE:
            # Use Celery for task management
            return start_pipeline_celery(pipeline_id, frequency)
        else:
            # Use simple threading scheduler
            return start_pipeline_simple(pipeline_id, frequency)
            
    except Exception as e:
        print(f"[TASK_MANAGER] Error starting pipeline {pipeline_id}: {e}")
        return {
            "success": False,
            "error": f"Failed to start pipeline: {str(e)}"
        }


def stop_pipeline(pipeline_id: int) -> Dict[str, Any]:
    """
    Stop a specific pipeline.
    
    Args:
        pipeline_id (int): ID of the pipeline to stop
        
    Returns:
        Dict containing success status and message
    """
    try:
        if SCHEDULER_TYPE == "celery" and REDIS_AVAILABLE:
            # Use Celery for task management
            return stop_pipeline_celery(pipeline_id)
        else:
            # Use simple threading scheduler
            return stop_pipeline_simple(pipeline_id)
            
    except Exception as e:
        print(f"[TASK_MANAGER] Error stopping pipeline {pipeline_id}: {e}")
        return {
            "success": False,
            "error": f"Failed to stop pipeline: {str(e)}"
        }


def stop_all_pipelines() -> Dict[str, Any]:
    """
    Stop all running pipelines.
    
    Returns:
        Dict containing success status and message
    """
    try:
        if SCHEDULER_TYPE == "celery" and REDIS_AVAILABLE:
            # Use Celery for task management
            return stop_all_pipelines_celery()
        else:
            # Use simple threading scheduler
            return stop_all_pipelines_simple()
            
    except Exception as e:
        print(f"[TASK_MANAGER] Error stopping all pipelines: {e}")
        return {
            "success": False,
            "error": f"Failed to stop all pipelines: {str(e)}"
        }


def get_pipeline_status(pipeline_id: Optional[int] = None) -> Dict[str, Any]:
    """
    Get status of pipeline tasks.
    
    Args:
        pipeline_id (int, optional): Specific pipeline ID to check, 
                                   if None returns all active pipelines
        
    Returns:
        Dict containing pipeline status information
    """
    try:
        if SCHEDULER_TYPE == "celery" and REDIS_AVAILABLE:
            # Use Celery for task management
            return get_pipeline_status_celery(pipeline_id)
        else:
            # Use simple threading scheduler
            return get_pipeline_status_simple(pipeline_id)
            
    except Exception as e:
        print(f"[TASK_MANAGER] Error getting pipeline status: {e}")
        return {
            "success": False,
            "error": f"Failed to get pipeline status: {str(e)}"
        }


# Import scheduler-specific functions
if SCHEDULER_TYPE == "celery" and REDIS_AVAILABLE:
    try:
        from .pipeline_tasks import (
            start_pipeline_celery,
            stop_pipeline_celery,
            stop_all_pipelines_celery,
            get_pipeline_status_celery
        )
        print("[TASK_MANAGER] Celery task functions imported successfully")
    except ImportError as e:
        print(f"[TASK_MANAGER] Failed to import Celery tasks ({e}), falling back to simple scheduler")
        SCHEDULER_TYPE = "simple"
        REDIS_AVAILABLE = False

# Always import simple scheduler as fallback
from .simple_scheduler import (
    start_pipeline_simple,
    stop_pipeline_simple,
    stop_all_pipelines_simple
)

def get_pipeline_status_simple(pipeline_id: Optional[int] = None) -> Dict[str, Any]:
    """
    Get pipeline status using simple scheduler.
    """
    from .simple_scheduler import active_tasks, task_threads
    
    if pipeline_id:
        # Get specific pipeline status
        if pipeline_id in active_tasks:
            thread = task_threads.get(pipeline_id)
            return {
                "success": True,
                "pipeline_id": pipeline_id,
                "status": "active" if active_tasks[pipeline_id]["running"] else "inactive",
                "task_data": active_tasks[pipeline_id],
                "thread_alive": thread.is_alive() if thread else False
            }
        else:
            return {
                "success": True,
                "pipeline_id": pipeline_id,
                "status": "inactive",
                "message": f"Pipeline {pipeline_id} is not running"
            }
    else:
        # Get all active pipelines
        task_info = {}
        for pid, task_data in active_tasks.items():
            thread = task_threads.get(pid)
            task_info[pid] = {
                "task_data": task_data,
                "thread_alive": thread.is_alive() if thread else False,
                "thread_name": thread.name if thread else None
            }
        
        return {
            "success": True,
            "active_tasks_count": len(active_tasks),
            "active_tasks": task_info,
            "scheduler_type": SCHEDULER_TYPE
        }


def get_pipeline_status_celery(pipeline_id: Optional[int] = None) -> Dict[str, Any]:
    """
    Get pipeline status using Celery (placeholder for future implementation).
    """
    # TODO: Implement Celery status checking
    return {
        "success": True,
        "message": "Celery status checking not yet implemented",
        "scheduler_type": SCHEDULER_TYPE
    }


# Export main interface functions and variables
__all__ = [
    'start_pipeline',
    'stop_pipeline', 
    'stop_all_pipelines',
    'get_pipeline_status',
    'SCHEDULER_TYPE',
    'REDIS_AVAILABLE'
]

