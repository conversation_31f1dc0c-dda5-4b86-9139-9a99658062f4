import os
import json
import requests
import pandas as pd
import psycopg2
import base64
import hashlib
from datetime import datetime
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv
from cryptography.fernet import Fernet

load_dotenv()

# Database configuration
is_docker = os.getenv("IS_DOCKER", "false").lower() == "true"
host = "db" if is_docker else "localhost"

superset_db = {
    "dbname": "superset",
    "user": "superset",
    "password": "superset",
    "host": host,
    "port": 5432,
}


class APIKeyManager:
    """
    Handles secure encryption and decryption of API keys.
    """

    def __init__(self):
        # Generate or get encryption key
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)

    def _get_or_create_encryption_key(self) -> bytes:
        """
        Get encryption key from environment or create a new one.
        In production, this should be stored securely (e.g., AWS KMS, HashiCorp Vault).
        """
        # Try to get key from environment
        env_key = os.getenv("API_ENCRYPTION_KEY")
        if env_key:
            try:
                return base64.urlsafe_b64decode(env_key.encode())
            except Exception:
                print("Invalid encryption key in environment, generating new one")

        # Generate new key if not found
        key = Fernet.generate_key()
        print(f"Generated new encryption key. Set API_ENCRYPTION_KEY={key.decode()} in your environment")
        return key

    def encrypt_api_key(self, api_key: str) -> str:
        """
        Encrypt API key for secure storage.

        Args:
            api_key: Plain text API key

        Returns:
            Encrypted API key as base64 string
        """
        if not api_key:
            return None

        try:
            encrypted_key = self.cipher_suite.encrypt(api_key.encode())
            return base64.urlsafe_b64encode(encrypted_key).decode()
        except Exception as e:
            print(f"Error encrypting API key: {e}")
            return None

    def decrypt_api_key(self, encrypted_api_key: str) -> str:
        """
        Decrypt API key for use in API calls.

        Args:
            encrypted_api_key: Encrypted API key as base64 string

        Returns:
            Plain text API key
        """
        if not encrypted_api_key:
            return None

        try:
            # Decode from base64
            encrypted_data = base64.urlsafe_b64decode(encrypted_api_key.encode())
            # Decrypt
            decrypted_key = self.cipher_suite.decrypt(encrypted_data)
            return decrypted_key.decode()
        except Exception as e:
            print(f"Error decrypting API key: {e}")
            return None


# Global instance
api_key_manager = APIKeyManager()


class DataFetcher:
    """
    Handles dynamic API calls, data cleaning, and schema detection for the pipeline system.
    """
    
    def __init__(self):
        self.connection = psycopg2.connect(**superset_db)
    
    def normalize_response_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recursively normalize fields:
        - Parse stringified dicts/lists
        - Flatten lists with a single object
        """
        for key, value in data.items():
            # Handle stringified JSON
            if isinstance(value, str):
                try:
                    parsed = json.loads(value.replace("'", '"'))
                    value = parsed
                    data[key] = parsed
                except Exception:
                    continue

            # Flatten single-item lists
            if isinstance(value, list) and len(value) == 1 and isinstance(value[0], dict):
                data[key] = value[0]

            # Recurse into nested dicts
            elif isinstance(value, dict):
                data[key] = self.normalize_response_fields(value)

        return data

    
    def fetch_api_data(self, api_url: str, encrypted_api_key: Optional[str] = None,
                      headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Fetch data from any API endpoint with optional authentication.

        Args:
            api_url: The API endpoint URL
            encrypted_api_key: Optional encrypted API key for authentication
            headers: Optional custom headers

        Returns:
            Dict containing the API response and metadata
        """
        try:
            # Clean up URL - remove any trailing quotes
            api_url = api_url.rstrip('"\'')
            
            # Decrypt API key if provided
            api_key = None
            if encrypted_api_key:
                try:
                    api_key = api_key_manager.decrypt_api_key(encrypted_api_key)
                    if api_key:
                        print(f"API key decrypted successfully")
                    else:
                        print(f"Failed to decrypt API key")
                except Exception as e:
                    print(f"Error decrypting API key: {e}")
                    # Continue with None api_key

            # Handle API key substitution in URL
            final_url = api_url
            if api_key and '{api_key}' in api_url:
                final_url = api_url.replace('{api_key}', api_key)
                print(f"Substituted decrypted API key in URL")
            elif '{api_key}' in api_url and not api_key:
                # If URL contains {api_key} placeholder but no key provided
                print(f"Warning: URL contains {api_key} placeholder but no valid API key was provided")

            # Prepare headers
            request_headers = headers.copy() if headers else {}
            if not request_headers:
                request_headers = {'Content-Type': 'application/json'}

            # Add API key to headers if provided (and not already in URL)
            if api_key and '{api_key}' not in api_url:
                # Common API key header patterns
                if 'Authorization' not in request_headers:
                    request_headers['Authorization'] = f'Bearer {api_key}'
                if 'X-API-Key' not in request_headers:
                    request_headers['X-API-Key'] = api_key

            print(f"Making API request to: {final_url}")
            print(f"Request headers: {request_headers}")

            # Make the API request
            response = requests.get(final_url, headers=request_headers, timeout=30)
            response.raise_for_status()
            
            # Try to parse as JSON
            try:
                data = response.json()
            except json.JSONDecodeError:
                # If not JSON, return as text
                data = {"raw_text": response.text}
            
            return {
                "success": True,
                "data": data,
                "status_code": response.status_code,
                "content_type": response.headers.get('Content-Type', '')
            }
            
        except requests.exceptions.RequestException as e:
            error_msg = f"{e}"
            print(f"API request error: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "status_code": getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None
            }
        except Exception as e:
            error_msg = f"Unexpected error: {e}"
            print(f"Error in fetch_api_data: {error_msg}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": error_msg
            }
    
    def clean_and_normalize_data(self, raw_data: Any) -> pd.DataFrame:
        """
        Clean and normalize the fetched data using pandas.

        Args:
            raw_data: Raw data from API response

        Returns:
            Cleaned pandas DataFrame
        """
        try:
            # Handle different data types
            if isinstance(raw_data, dict):
                # Normalize response fields if needed (e.g., stringified JSON)                
                raw_data = self.normalize_response_fields(raw_data)

                # If it's a dictionary, try to extract the main data
                if 'data' in raw_data:
                    data_to_process = raw_data['data']
                elif 'results' in raw_data:
                    data_to_process = raw_data['results']
                elif 'items' in raw_data:
                    data_to_process = raw_data['items']
                else:
                    data_to_process = raw_data

                # Convert to DataFrame
                if isinstance(data_to_process, list):
                    # Handle empty list
                    if not data_to_process:
                        return pd.DataFrame({'message': ['No data available']})
                    df = pd.DataFrame(data_to_process)
                else:
                    df = pd.json_normalize(data_to_process)

            elif isinstance(raw_data, list):
                # Handle empty list
                if not raw_data:
                    return pd.DataFrame({'message': ['No data available']})
                df = pd.DataFrame(raw_data)
            else:
                # For other types, create a single-column DataFrame
                df = pd.DataFrame({'value': [raw_data]})

            # Check if DataFrame is empty after creation
            if df.empty:
                return pd.DataFrame({'message': ['No data could be processed']})

            # Basic cleaning operations
            # Remove completely empty columns
            df = df.dropna(axis=1, how='all')

            # Clean column names (remove special characters, spaces)
            if not df.empty:
                df.columns = df.columns.astype(str)  # Ensure all column names are strings
                df.columns = df.columns.str.replace(r'[^\w]', '_', regex=True)
                df.columns = df.columns.str.lower()

                # Handle missing values
                # Fill numeric columns with 0, text columns with empty string
                for col in df.columns:
                    try:
                        if df[col].dtype in ['int64', 'float64']:
                            df[col] = df[col].fillna(0)
                        else:
                            df[col] = df[col].fillna('')
                    except Exception as col_error:
                        print(f"Error processing column {col}: {col_error}")
                        # Convert problematic columns to string
                        df[col] = df[col].astype(str).fillna('')

            return df

        except Exception as e:
            print(f"Error cleaning data: {e}")
            # Return DataFrame with error information
            return pd.DataFrame({'error': [str(e)], 'raw_data_type': [str(type(raw_data))]})
    
    def detect_schema(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Auto-detect schema from the cleaned DataFrame.

        Args:
            df: Cleaned pandas DataFrame

        Returns:
            Schema information including column types and sample data
        """
        # Convert sample data safely to avoid unhashable type errors
        sample_data = []
        if not df.empty:
            try:
                sample_records = df.head(3).to_dict('records')
                for record in sample_records:
                    safe_record = {}
                    for key, value in record.items():
                        # Convert unhashable types to strings
                        if isinstance(value, (list, dict, set)):
                            safe_record[key] = str(value)
                        elif pd.isna(value):
                            safe_record[key] = None
                        else:
                            safe_record[key] = value
                    sample_data.append(safe_record)
            except Exception as e:
                print(f"Error creating sample data: {e}")
                sample_data = []

        schema = {
            "columns": {},
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "sample_data": sample_data
        }

        for col in df.columns:
            try:
                # Get sample values safely
                sample_values = []
                try:
                    col_sample = df[col].dropna().head(3)
                    for val in col_sample:
                        if isinstance(val, (list, dict, set)):
                            sample_values.append(str(val))
                        elif pd.isna(val):
                            sample_values.append(None)
                        else:
                            sample_values.append(val)
                except Exception:
                    sample_values = ["<error_reading_value>"]

                col_info = {
                    "type": str(df[col].dtype),
                    "null_count": int(df[col].isnull().sum()),
                    "unique_count": int(df[col].nunique()),
                    "sample_values": sample_values
                }

                # Suggest SQL type based on pandas dtype
                if df[col].dtype == 'int64':
                    # Use BIGINT for int64 to handle large values like timestamps and IDs
                    col_info["sql_type"] = "BIGINT"
                elif df[col].dtype == 'int32':
                    col_info["sql_type"] = "INTEGER"
                elif df[col].dtype in ['float64', 'float32']:
                    col_info["sql_type"] = "REAL"
                elif df[col].dtype == 'bool':
                    col_info["sql_type"] = "BOOLEAN"
                elif df[col].dtype == 'datetime64[ns]':
                    col_info["sql_type"] = "TIMESTAMP"
                else:
                    col_info["sql_type"] = "TEXT"

                schema["columns"][str(col)] = col_info

            except Exception as e:
                print(f"Error processing column {col}: {e}")
                # Add a safe fallback for problematic columns
                schema["columns"][str(col)] = {
                    "type": "object",
                    "null_count": 0,
                    "unique_count": 0,
                    "sample_values": ["<error_processing_column>"],
                    "sql_type": "TEXT"
                }

        return schema
    
    def save_pipeline_metadata(self, pipeline_name: str, api_url: str,
                             cleaned_data: pd.DataFrame, schema: Dict[str, Any],
                             api_key: Optional[str] = None,
                             headers: Optional[Dict[str, str]] = None) -> int:
        """
        Save pipeline metadata to the database.

        Args:
            pipeline_name: Name of the pipeline
            api_url: API endpoint URL
            cleaned_data: Cleaned DataFrame
            schema: Detected schema
            api_key: Optional API key (will be hashed)
            headers: Optional headers

        Returns:
            Pipeline ID
        """
        try:
            with self.connection.cursor() as cursor:
                # Encrypt API key for secure storage
                encrypted_api_key = None
                if api_key:
                    encrypted_api_key = api_key_manager.encrypt_api_key(api_key)
                    print(f"API key encrypted for secure storage")

                # Prepare sample response (first 5 rows) with safe JSON serialization
                sample_response = []
                if not cleaned_data.empty:
                    try:
                        print(f"Cleaned data: {cleaned_data}")
                        raw_records = cleaned_data.head(5).to_dict('records')
                        print(f"Raw records: {raw_records}")
                        for record in raw_records:
                            safe_record = {}
                            for key, value in record.items():
                                try:
                                    if pd.isna(value):
                                        safe_record[str(key)] = None
                                    elif isinstance(value, (list, dict, set)):
                                        safe_record[str(key)] = str(value)
                                    elif isinstance(value, (np.generic,)):
                                        safe_record[str(key)] = value.item()
                                    elif isinstance(value, (np.ndarray, pd.Series)):
                                        safe_record[str(key)] = value.tolist()
                                    else:
                                        safe_record[str(key)] = value
                                except Exception as inner_e:
                                    print(f"Serialization error for {key}: {inner_e}")
                                    safe_record[str(key)] = str(value)
                            sample_response.append(safe_record)
                    except Exception as e:
                        print(f"Error creating sample response: {e}")
                        sample_response = [{"error": "Could not create sample data"}]

                # Ensure schema is JSON serializable
                try:
                    json.dumps(schema)  # Test serialization
                    schema_json = json.dumps(schema)
                except (TypeError, ValueError) as e:
                    print(f"Schema serialization error: {e}")
                    schema_json = json.dumps({"error": "Schema could not be serialized", "columns": {}})

                # Ensure headers are JSON serializable
                headers_json = None
                if headers:
                    try:
                        headers_json = json.dumps(headers)
                    except (TypeError, ValueError) as e:
                        print(f"Headers serialization error: {e}")
                        headers_json = json.dumps({"error": "Headers could not be serialized"})

                insert_query = """
                INSERT INTO superset_data_pipeline
                (pipeline_name, api_curl, fetch_response, updated_schema, api_key, headers)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING pipeline_id;
                """

                cursor.execute(insert_query, (
                    pipeline_name,
                    api_url,
                    json.dumps(sample_response),
                    schema_json,
                    encrypted_api_key,
                    headers_json
                ))

                pipeline_id = cursor.fetchone()[0]
                self.connection.commit()

                return pipeline_id

        except Exception as e:
            self.connection.rollback()
            raise Exception(f"Error saving pipeline metadata: {e}")
    
    def process_api_pipeline(self, pipeline_name: str, api_url: str,
                           api_key: Optional[str] = None,
                           headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Complete pipeline processing: fetch, clean, detect schema, and save.
        
        Args:
            pipeline_name: Name of the pipeline
            api_url: API endpoint URL
            api_key: Optional API key
            headers: Optional headers
            
        Returns:
            Processing results including pipeline_id and schema
        """
        try:
            # Step 1: Fetch data from API (encrypt API key first)
            encrypted_api_key = None
            if api_key:
                encrypted_api_key = api_key_manager.encrypt_api_key(api_key)

            api_response = self.fetch_api_data(api_url, encrypted_api_key, headers)
            
            if not api_response.get("success"):
                return {
                    "success": False,
                    "error": f"API fetch failed: {api_response.get('error')}"
                }
            
            # Step 2: Clean and normalize data
            cleaned_df = self.clean_and_normalize_data(api_response["data"])
            
            if cleaned_df.empty:
                return {
                    "success": False,
                    "error": "No data could be extracted from API response"
                }
            
            # Step 3: Detect schema
            schema = self.detect_schema(cleaned_df)
            
            # Step 4: Save pipeline metadata
            pipeline_id = self.save_pipeline_metadata(
                pipeline_name, api_url, cleaned_df, schema, api_key, headers
            )
            
            return {
                "success": True,
                "pipeline_id": pipeline_id,
                "schema": schema,
                "sample_data": cleaned_df.head(5).to_dict('records'),
                "message": f"Pipeline '{pipeline_name}' created successfully with ID {pipeline_id}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def __del__(self):
        """Close database connection when object is destroyed."""
        if hasattr(self, 'connection'):
            self.connection.close()


