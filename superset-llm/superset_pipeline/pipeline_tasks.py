"""
Celery Tasks for Data Pipeline System

This module contains Celery task definitions for the data pipeline system.
These tasks handle background data fetching and processing when Redis/Celery is available.

Tasks:
    - fetch_and_store_pipeline_data: Celery task for fetching and storing data
    - continuous_pipeline_task: Celery task for continuous data fetching
    
Functions:
    - start_pipeline_celery: Start a pipeline using Celery
    - stop_pipeline_celery: Stop a Celery pipeline task
    - stop_all_pipelines_celery: Stop all Celery pipeline tasks
    - get_pipeline_status_celery: Get status of Celery pipeline tasks
"""

import os
import json
from datetime import datetime
from typing import Dict, Any
from celery import Celery
from dotenv import load_dotenv

from .superset_data_fetching import DataFetcher
from .dbs_pipeline import DatabaseManager

load_dotenv()

# Initialize Celery app
celery_app = Celery('pipeline_tasks')

# Configure Celery
redis_host = os.getenv("REDIS_HOST", "localhost")
redis_port = int(os.getenv("REDIS_PORT", 6379))
redis_db = int(os.getenv("REDIS_DB", 0))

celery_app.conf.update(
    broker_url=f'redis://{redis_host}:{redis_port}/{redis_db}',
    result_backend=f'redis://{redis_host}:{redis_port}/{redis_db}',
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=True
)

# Global dictionary to track active pipeline tasks
active_celery_tasks = {}


@celery_app.task(bind=True, name='fetch_and_store_pipeline_data')
def fetch_and_store_pipeline_data(self, pipeline_id: int):
    """
    Celery task to fetch data from API and store in target table.
    
    Args:
        pipeline_id (int): ID of the pipeline to process
        
    Returns:
        Dict containing task result and status
    """
    try:
        print(f"[CELERY] Starting data fetch for pipeline {pipeline_id}")
        
        db_manager = DatabaseManager()
        data_fetcher = DataFetcher()
        
        # Get pipeline configuration
        pipeline = db_manager.get_pipeline_by_id(pipeline_id)
        if not pipeline:
            error_msg = f"Pipeline {pipeline_id} not found"
            print(f"[CELERY] {error_msg}")
            return {"status": "error", "message": error_msg}

        print(f"[CELERY] Pipeline config: API={pipeline['api_curl']}, Table={pipeline.get('table_name')}")

        # Update status to indicate processing
        db_manager.update_pipeline_status(pipeline_id, "active", "processing")

        # Fetch data from API
        print(f"[CELERY] Calling API: {pipeline['api_curl']}")
        api_response = data_fetcher.fetch_api_data(
            pipeline["api_curl"],
            pipeline.get("api_key"),  # This is encrypted
            pipeline.get("headers")
        )

        if not api_response.get("success"):
            error_msg = f"API fetch failed: {api_response.get('error', 'Unknown error')}"
            print(f"[CELERY] {error_msg}")
            db_manager.update_pipeline_status(pipeline_id, "active", "error")
            return {"status": "error", "message": error_msg}

        # Get selected fields for the pipeline
        selected_fields = json.loads(pipeline.get("unselected_schema", "[]"))
        if not selected_fields:
            error_msg = "No fields selected for pipeline"
            print(f"[CELERY] {error_msg}")
            db_manager.update_pipeline_status(pipeline_id, "active", "error")
            return {"status": "error", "message": error_msg}

        # Transform and store data
        print(f"[CELERY] Transforming data with selected fields: {selected_fields}")
        transformed_data = data_fetcher.transform_data_for_selected_fields(
            api_response["data"], selected_fields
        )

        if not transformed_data:
            error_msg = "No data to store after transformation"
            print(f"[CELERY] {error_msg}")
            db_manager.update_pipeline_status(pipeline_id, "active", "warning")
            return {"status": "warning", "message": error_msg}

        # Store data in target table
        table_name = pipeline["table_name"]
        print(f"[CELERY] Storing {len(transformed_data)} records in table {table_name}")
        
        store_result = db_manager.store_data_in_table(table_name, transformed_data)
        
        if store_result.get("success"):
            print(f"[CELERY] Successfully stored data for pipeline {pipeline_id}")
            db_manager.update_pipeline_status(pipeline_id, "active", "healthy")
            return {
                "status": "success",
                "message": f"Stored {len(transformed_data)} records",
                "records_count": len(transformed_data)
            }
        else:
            error_msg = f"Failed to store data: {store_result.get('error', 'Unknown error')}"
            print(f"[CELERY] {error_msg}")
            db_manager.update_pipeline_status(pipeline_id, "active", "error")
            return {"status": "error", "message": error_msg}

    except Exception as e:
        error_msg = f"Task execution error: {str(e)}"
        print(f"[CELERY] {error_msg}")
        try:
            db_manager = DatabaseManager()
            db_manager.update_pipeline_status(pipeline_id, "active", "error")
        except:
            pass
        return {"status": "error", "message": error_msg}


@celery_app.task(bind=True, name='continuous_pipeline_task')
def continuous_pipeline_task(self, pipeline_id: int, frequency: int):
    """
    Celery task for continuous data fetching at specified frequency.
    
    Args:
        pipeline_id (int): ID of the pipeline to process
        frequency (int): Frequency in seconds for data fetching
        
    Returns:
        Dict containing task result and status
    """
    try:
        print(f"[CELERY] Starting continuous task for pipeline {pipeline_id} with frequency {frequency}s")
        
        # Execute the data fetch and store
        result = fetch_and_store_pipeline_data.delay(pipeline_id)
        task_result = result.get(timeout=300)  # 5 minute timeout
        
        print(f"[CELERY] Pipeline {pipeline_id} execution result: {task_result}")
        
        # Schedule next execution
        continuous_pipeline_task.apply_async(
            args=[pipeline_id, frequency],
            countdown=frequency
        )
        
        return {
            "status": "success",
            "message": f"Pipeline {pipeline_id} executed, next run in {frequency}s",
            "execution_result": task_result
        }
        
    except Exception as e:
        error_msg = f"Continuous task error: {str(e)}"
        print(f"[CELERY] {error_msg}")
        return {"status": "error", "message": error_msg}


def start_pipeline_celery(pipeline_id: int, frequency: int = 60) -> Dict[str, Any]:
    """
    Start a pipeline using Celery.
    
    Args:
        pipeline_id (int): ID of the pipeline to start
        frequency (int): Frequency in seconds for data fetching
        
    Returns:
        Dict containing success status, task_id, and message
    """
    try:
        if pipeline_id in active_celery_tasks:
            return {"success": False, "error": f"Pipeline {pipeline_id} is already running"}
        
        db_manager = DatabaseManager()
        
        # Update pipeline status to active
        db_manager.update_pipeline_status(pipeline_id, "active", "starting")
        
        # Start the continuous task
        task = continuous_pipeline_task.delay(pipeline_id, frequency)
        
        # Track the task
        active_celery_tasks[pipeline_id] = {
            "task_id": task.id,
            "frequency": frequency,
            "started_at": datetime.now().isoformat()
        }
        
        print(f"[CELERY] Started pipeline {pipeline_id} with task ID {task.id}")
        
        return {
            "success": True,
            "task_id": task.id,
            "message": f"Pipeline {pipeline_id} started with Celery"
        }
        
    except Exception as e:
        print(f"[CELERY] Error starting pipeline {pipeline_id}: {e}")
        return {"success": False, "error": str(e)}


def stop_pipeline_celery(pipeline_id: int) -> Dict[str, Any]:
    """
    Stop a Celery pipeline task.
    
    Args:
        pipeline_id (int): ID of the pipeline to stop
        
    Returns:
        Dict containing success status and message
    """
    try:
        db_manager = DatabaseManager()
        
        # Update pipeline status to inactive
        db_manager.update_pipeline_status(pipeline_id, "inactive", "stopped")
        
        # Stop the task if it exists
        if pipeline_id in active_celery_tasks:
            task_id = active_celery_tasks[pipeline_id]["task_id"]
            celery_app.control.revoke(task_id, terminate=True)
            del active_celery_tasks[pipeline_id]
            print(f"[CELERY] Stopped pipeline {pipeline_id} (task {task_id})")
        
        return {"success": True, "message": f"Pipeline {pipeline_id} stopped"}
        
    except Exception as e:
        print(f"[CELERY] Error stopping pipeline {pipeline_id}: {e}")
        return {"success": False, "error": str(e)}


def stop_all_pipelines_celery() -> Dict[str, Any]:
    """
    Stop all Celery pipeline tasks.
    
    Returns:
        Dict containing success status and message
    """
    try:
        db_manager = DatabaseManager()
        stopped_count = 0
        
        # Stop all active tasks
        for pipeline_id in list(active_celery_tasks.keys()):
            result = stop_pipeline_celery(pipeline_id)
            if result["success"]:
                stopped_count += 1
        
        # Also update any pipelines in database that might be marked as active
        with db_manager.connection.cursor() as cursor:
            cursor.execute("""
                UPDATE superset_data_pipeline 
                SET status = 'inactive', health = 'stopped'
                WHERE status = 'active'
            """)
            db_manager.connection.commit()
        
        return {
            "success": True, 
            "message": f"Stopped {stopped_count} active Celery pipelines"
        }
        
    except Exception as e:
        print(f"[CELERY] Error stopping all pipelines: {e}")
        return {"success": False, "error": str(e)}


def get_pipeline_status_celery(pipeline_id: int = None) -> Dict[str, Any]:
    """
    Get status of Celery pipeline tasks.
    
    Args:
        pipeline_id (int, optional): Specific pipeline ID to check
        
    Returns:
        Dict containing pipeline status information
    """
    try:
        if pipeline_id:
            # Get specific pipeline status
            if pipeline_id in active_celery_tasks:
                task_data = active_celery_tasks[pipeline_id]
                task_id = task_data["task_id"]
                
                # Check task status in Celery
                task = celery_app.AsyncResult(task_id)
                
                return {
                    "success": True,
                    "pipeline_id": pipeline_id,
                    "status": "active",
                    "task_id": task_id,
                    "task_status": task.status,
                    "task_data": task_data
                }
            else:
                return {
                    "success": True,
                    "pipeline_id": pipeline_id,
                    "status": "inactive",
                    "message": f"Pipeline {pipeline_id} is not running"
                }
        else:
            # Get all active pipelines
            task_info = {}
            for pid, task_data in active_celery_tasks.items():
                task_id = task_data["task_id"]
                task = celery_app.AsyncResult(task_id)
                task_info[pid] = {
                    "task_data": task_data,
                    "task_status": task.status,
                    "task_id": task_id
                }
            
            return {
                "success": True,
                "active_tasks_count": len(active_celery_tasks),
                "active_tasks": task_info,
                "scheduler_type": "celery"
            }
            
    except Exception as e:
        print(f"[CELERY] Error getting pipeline status: {e}")
        return {"success": False, "error": str(e)}


# Export functions
__all__ = [
    'fetch_and_store_pipeline_data',
    'continuous_pipeline_task',
    'start_pipeline_celery',
    'stop_pipeline_celery',
    'stop_all_pipelines_celery',
    'get_pipeline_status_celery'
]
