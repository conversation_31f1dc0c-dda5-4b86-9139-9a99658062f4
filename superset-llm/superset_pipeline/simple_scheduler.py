import os
import time
import threading
import json
from datetime import datetime
from typing import Dict, Any
from dotenv import load_dotenv

from .superset_data_fetching import DataFetcher
from .dbs_pipeline import DatabaseManager

load_dotenv()

# Global variables to track running tasks
active_tasks = {}
task_threads = {}


class SimpleScheduler:
    """
    Simple background task scheduler that doesn't require Redis/Celery.
    Uses threading for background processing.
    """
    
    def __init__(self):
        self.running = True
        self.tasks = {}
    
    def fetch_and_store_data(self, pipeline_id: int):
        """
        Fetch data from API and store in target table.
        """
        db_manager = DatabaseManager()
        data_fetcher = DataFetcher()

        try:
            print(f"[FETCH] Starting data fetch for pipeline {pipeline_id}")

            # Get pipeline configuration
            pipeline = db_manager.get_pipeline_by_id(pipeline_id)
            if not pipeline:
                error_msg = f"Pipeline {pipeline_id} not found"
                print(f"[FETCH] {error_msg}")
                return {"status": "error", "message": error_msg}

            print(f"[FETCH] Pipeline config: API={pipeline['api_curl']}, Table={pipeline.get('table_name')}")

            # Update status to indicate processing
            db_manager.update_pipeline_status(pipeline_id, "active", "processing")

            # Fetch data from API (API key is already encrypted in database)
            print(f"[FETCH] Calling API: {pipeline['api_curl']}")
            api_response = data_fetcher.fetch_api_data(
                pipeline["api_curl"],
                pipeline.get("api_key"),  # This is encrypted
                pipeline.get("headers")
            )

            if not api_response.get("success"):
                error_msg = f"API fetch failed: {api_response.get('error')}"
                print(f"[FETCH] {error_msg}")
                raise Exception(error_msg)

            print(f"[FETCH] API call successful, processing data...")

            # Clean and normalize data
            cleaned_df = data_fetcher.clean_and_normalize_data(api_response["data"])
            
            print("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++")
            print("cleand dataframe :", cleaned_df.columns)
            print("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++")

            if cleaned_df.empty:
                msg = f"No data fetched for pipeline {pipeline_id}"
                print(f"[FETCH] {msg}")
                db_manager.update_pipeline_status(pipeline_id, "active", "healthy")
                return {"status": "success", "message": "No new data"}

            print(f"[FETCH] Cleaned data: {len(cleaned_df)} rows, columns: {list(cleaned_df.columns)}")

            # Get selected fields from schema
            schema = pipeline.get("updated_schema", {})
            selected_fields = schema.get("selected_fields", [])
            
            print("+++++++++++++++++++++++++++++++++++++++++++++++++++++++")
            print("slected fields :", selected_fields)
            print("+++++++++++++++++++++++++++++++++++++++++++++++++++++++")

            if not selected_fields:
                error_msg = "No selected fields defined for pipeline"
                print(f"[FETCH] {error_msg}")
                raise Exception(error_msg)

            print(f"[FETCH] Selected fields: {selected_fields}")
            print(f"[FETCH] Target table: {pipeline['table_name']}")

            # Insert data into target table
            success = db_manager.insert_data_to_table(
                pipeline["table_name"],
                cleaned_df,
                pipeline_id,
                selected_fields
            )

            if success:
                db_manager.update_pipeline_status(pipeline_id, "active", "healthy")
                result = {
                    "status": "success",
                    "message": f"Successfully processed {len(cleaned_df)} rows",
                    "rows_processed": len(cleaned_df)
                }
                print(f"[FETCH] {result['message']}")
                return result
            else:
                error_msg = "Failed to insert data into target table"
                print(f"[FETCH] {error_msg}")
                raise Exception(error_msg)

        except Exception as e:
            # Update status to error
            db_manager.update_pipeline_status(pipeline_id, "active", "unhealthy")
            error_msg = f"Error in pipeline {pipeline_id}: {e}"
            print(f"[FETCH] {error_msg}")
            import traceback
            print(f"[FETCH] Traceback: {traceback.format_exc()}")
            return {"status": "error", "message": str(e)}
    
    def continuous_task(self, pipeline_id: int, frequency: int):
        """
        Continuous task that runs at specified frequency.
        """
        print(f"Starting continuous task for pipeline {pipeline_id} with frequency {frequency}s")

        while pipeline_id in active_tasks and active_tasks[pipeline_id]["running"]:
            try:
                print(f"[{datetime.now()}] Pipeline {pipeline_id} - Starting fetch cycle")

                # Check if pipeline is still active
                db_manager = DatabaseManager()
                pipeline = db_manager.get_pipeline_by_id(pipeline_id)

                if not pipeline:
                    print(f"Pipeline {pipeline_id} not found in database, stopping task")
                    break

                if pipeline["status"] != "active":
                    print(f"Pipeline {pipeline_id} status is {pipeline['status']}, stopping task")
                    break

                # Check if pipeline has required configuration
                if not pipeline.get("table_name"):
                    print(f"Pipeline {pipeline_id} has no table_name configured")
                    time.sleep(frequency)
                    continue

                if not pipeline.get("updated_schema"):
                    print(f"Pipeline {pipeline_id} has no schema configured")
                    time.sleep(frequency)
                    continue

                selected_fields = pipeline["updated_schema"].get("selected_fields", [])
                if not selected_fields:
                    print(f"Pipeline {pipeline_id} has no selected fields configured")
                    time.sleep(frequency)
                    continue

                print(f"Pipeline {pipeline_id} config: table={pipeline['table_name']}, fields={selected_fields}")

                # Execute the data fetch and store
                result = self.fetch_and_store_data(pipeline_id)
                print(f"[{datetime.now()}] Pipeline {pipeline_id} execution result: {result}")

                # Wait for the specified frequency
                print(f"Pipeline {pipeline_id} waiting {frequency} seconds until next fetch...")
                for i in range(frequency):
                    if pipeline_id not in active_tasks or not active_tasks[pipeline_id]["running"]:
                        print(f"Pipeline {pipeline_id} stopped during wait period")
                        break
                    time.sleep(1)

            except Exception as e:
                print(f"Error in continuous task for pipeline {pipeline_id}: {e}")
                import traceback
                print(f"Traceback: {traceback.format_exc()}")
                # Wait before retrying
                print(f"Pipeline {pipeline_id} waiting {frequency * 2} seconds before retry...")
                time.sleep(frequency * 2)

        # Clean up when task stops
        if pipeline_id in active_tasks:
            del active_tasks[pipeline_id]
        if pipeline_id in task_threads:
            del task_threads[pipeline_id]

        print(f"Continuous task for pipeline {pipeline_id} stopped")


def start_pipeline_simple(pipeline_id: int, frequency: int = 60):
    """
    Start a pipeline using simple threading.
    """
    try:
        if pipeline_id in active_tasks:
            return {"success": False, "error": f"Pipeline {pipeline_id} is already running"}
        
        db_manager = DatabaseManager()
        
        # Update pipeline status to active
        db_manager.update_pipeline_status(pipeline_id, "active", "starting")
        
        # Create scheduler and start task
        scheduler = SimpleScheduler()
        
        # Mark task as active
        active_tasks[pipeline_id] = {
            "running": True,
            "frequency": frequency,
            "started_at": datetime.now().isoformat()
        }
        
        # Start the background thread
        thread = threading.Thread(
            target=scheduler.continuous_task,
            args=(pipeline_id, frequency),
            daemon=True
        )
        thread.start()
        
        task_threads[pipeline_id] = thread
        
        print(f"Started pipeline {pipeline_id} with frequency {frequency}s using simple scheduler")
        return {"success": True, "task_id": f"simple_{pipeline_id}"}
        
    except Exception as e:
        print(f"Error starting pipeline {pipeline_id}: {e}")
        return {"success": False, "error": str(e)}


def stop_pipeline_simple(pipeline_id: int):
    """
    Stop a running pipeline task.
    """
    try:
        db_manager = DatabaseManager()
        
        # Update pipeline status to inactive
        db_manager.update_pipeline_status(pipeline_id, "inactive", "stopped")
        
        # Stop the task if it exists
        if pipeline_id in active_tasks:
            active_tasks[pipeline_id]["running"] = False
            print(f"Stopped pipeline {pipeline_id}")
        
        return {"success": True, "message": f"Pipeline {pipeline_id} stopped"}
        
    except Exception as e:
        print(f"Error stopping pipeline {pipeline_id}: {e}")
        return {"success": False, "error": str(e)}


def stop_all_pipelines_simple():
    """
    Stop all running pipeline tasks.
    """
    try:
        db_manager = DatabaseManager()
        stopped_count = 0
        
        # Stop all active tasks
        for pipeline_id in list(active_tasks.keys()):
            result = stop_pipeline_simple(pipeline_id)
            if result["success"]:
                stopped_count += 1
        
        # Also update any pipelines in database that might be marked as active
        with db_manager.connection.cursor() as cursor:
            cursor.execute("""
                UPDATE superset_data_pipeline 
                SET status = 'inactive', health = 'stopped'
                WHERE status = 'active'
            """)
            db_manager.connection.commit()
        
        return {
            "success": True, 
            "message": f"Stopped {stopped_count} active pipelines"
        }
        
    except Exception as e:
        print(f"Error stopping all pipelines: {e}")
        return {"success": False, "error": str(e)}


def get_pipeline_status_simple():
    """
    Get status of all active pipeline tasks.
    """
    return {
        "active_tasks": len(active_tasks),
        "tasks": active_tasks,
        "scheduler_type": "simple_threading"
    }
