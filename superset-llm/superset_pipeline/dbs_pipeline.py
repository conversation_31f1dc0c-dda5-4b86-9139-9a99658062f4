import ast
import os
import json
import psycopg2
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Union
from dotenv import load_dotenv
from psycopg2.extras import execute_values
 
load_dotenv()
 
# Database configuration
is_docker = os.getenv("IS_DOCKER", "false").lower() == "true"
host = "db" if is_docker else "localhost"
 
superset_db = {
    "dbname": "superset",
    "user": "superset",
    "password": "superset",
    "host": host,
    "port": 5432,
}
 
 
class DatabaseManager:
    """
    Handles all database-related operations for the pipeline system.
    """
 
    def __init__(self):
        self.connection = psycopg2.connect(**superset_db)
        


    
    def check_table_exists(self, table_name: str) -> bool:
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = %s
                    )
                """,
                    (table_name,),
                )
                return cursor.fetchone()[0]
        except Exception as e:
            print(f"Error checking table existence: {e}")
            return False
   
    def get_pipeline_by_id(self, pipeline_id: int) -> Optional[Dict[str, Any]]:
        """
        Retrieve pipeline configuration by ID.
 
        Args:
            pipeline_id: Pipeline ID
 
        Returns:
            Pipeline configuration or None if not found
        """
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT pipeline_id, pipeline_name, api_curl, updated_schema,
                           db, table_name, frequency, unselected_schema,
                           api_key, headers, status, health , fetch_response
                    FROM superset_data_pipeline
                    WHERE pipeline_id = %s
                """,
                    (pipeline_id,),
                )
  
                def deep_parse(value):
                    """
                    Recursively parse stringified lists/dicts into actual Python objects.
                    """
                    if isinstance(value, str):
                        # Replace single quotes with double quotes if needed (common in APIs)
                        try:
                            # Try using json.loads first
                            return deep_parse(json.loads(value))
                        except (json.JSONDecodeError, TypeError):
                            try:
                                # Fallback: use ast.literal_eval for safe evaluation
                                parsed = ast.literal_eval(value)
                                return deep_parse(parsed)
                            except (ValueError, SyntaxError):
                                return value  # Return as-is if it can't be parsed
                    elif isinstance(value, list):
                        return [deep_parse(item) for item in value]
                    elif isinstance(value, dict):
                        return {k: deep_parse(v) for k, v in value.items()}
                    return value
 
                row = cursor.fetchone()
                if row:
                    # Safe JSON parsing with error handling
                    def safe_json_parse(json_str, field_name):
                        if json_str:
                            try:
                                # If it's already a dict/list, return as-is
                                if isinstance(json_str, (dict, list)):
                                    return deep_parse(json_str)
                                # If it's a string, parse it
                                parsed = json.loads(json_str)
                                return deep_parse(parsed)
                            except (json.JSONDecodeError, TypeError) as e:
                                print(f"Error parsing JSON for {field_name}: {e}")
                                return None
                        return None
 
                        
                    return {
                        "pipeline_id": row[0],
                        "pipeline_name": row[1],
                        "api_curl": row[2],
                        "updated_schema": safe_json_parse(row[3], "updated_schema"),
                        "db": row[4],
                        "table_name": row[5],
                        "frequency": row[6],
                        "unselected_schema": safe_json_parse(
                            row[7], "unselected_schema"
                        ),
                        "api_key": row[8],
                        "headers": safe_json_parse(row[9], "headers"),
                        "status": row[10],
                        "health": row[11],
                        "fetch_response": safe_json_parse(row[12], "fetch_response"),
                    }
                return None
 
        except Exception as e:
            print(f"Error retrieving pipeline: {e}")
            import traceback
 
            print(f"Traceback: {traceback.format_exc()}")
            return None
 
    def update_pipeline_schema(
        self,
        pipeline_id: int,
        selected_fields: List[str],
        db_name: str,
        table_name: str,
    ) -> bool:
        """
        Update pipeline with selected schema fields and target table info.
        Handles all schema edge cases and ensures data integrity.
 
        Args:
            pipeline_id: Pipeline ID
            selected_fields: List of selected field names
            db_name: Target database name
            table_name: Target table name
 
        Returns:
            Success status
        """
        try:
            print(
                f"Updating schema for pipeline {pipeline_id} with fields: {selected_fields}"
            )
 
            if not selected_fields:
                print("No fields selected, cannot update schema")
                return False
 
            # Get current pipeline data with error handling
            with self.connection.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT updated_schema, unselected_schema, pipeline_name
                    FROM superset_data_pipeline
                    WHERE pipeline_id = %s
                """,
                    (pipeline_id,),
                )
 
                result = cursor.fetchone()
                if not result:
                    print(f"Pipeline {pipeline_id} not found in database")
                    return False
 
                # Handle various schema formats and potential corruption
                try:
                    current_schema = (
                        json.loads(result[0])
                        if result[0] and isinstance(result[0], str)
                        else {}
                    )
                    if isinstance(result[0], dict):
                        current_schema = result[0]
 
                    unselected_schema = (
                        json.loads(result[1])
                        if result[1] and isinstance(result[1], str)
                        else {}
                    )
                    if isinstance(result[1], dict):
                        unselected_schema = result[1]
 
                    pipeline_name = result[2]
                except json.JSONDecodeError:
                    print(
                        f"Invalid JSON in schema for pipeline {pipeline_id}, attempting recovery"
                    )
                    current_schema = (
                        {"columns": {}}
                        if not isinstance(result[0], dict)
                        else result[0]
                    )
                    unselected_schema = (
                        {"columns": {}}
                        if not isinstance(result[1], dict)
                        else result[1]
                    )
 
            # Merge all available columns from both schemas with validation
            all_columns = {}
 
            # Process current schema columns
            if isinstance(current_schema, dict) and "columns" in current_schema:
                all_columns.update(current_schema.get("columns", {}))
            elif isinstance(current_schema, dict) and current_schema:
                # Handle legacy format without "columns" key
                temp_columns = {
                    k: v
                    for k, v in current_schema.items()
                    if k not in ["total_columns", "selected_fields"]
                }
                all_columns.update(temp_columns)
 
            # Process unselected schema columns
            if isinstance(unselected_schema, dict) and "columns" in unselected_schema:
                all_columns.update(unselected_schema.get("columns", {}))
            elif isinstance(unselected_schema, dict) and unselected_schema:
                # Handle legacy format without "columns" key
                temp_columns = {
                    k: v
                    for k, v in unselected_schema.items()
                    if k not in ["unselected_fields"]
                }
                all_columns.update(temp_columns)
 
            if not all_columns:
                print(f"No columns found in schemas, attempting to recover")
                # If we have no schema at all, create a minimal schema with TEXT columns
                all_columns = {
                    field: {"type": "string", "sql_type": "TEXT"}
                    for field in selected_fields
                }
 
            # Validate and filter selected fields
            valid_fields = []
            for field in selected_fields:
                if field in all_columns:
                    valid_fields.append(field)
                else:
                    # Auto-create missing fields with TEXT type
                    all_columns[field] = {"type": "string", "sql_type": "TEXT"}
                    valid_fields.append(field)
                    print(f"Auto-created missing field: {field}")
 
            # Create updated schema with selected fields
            updated_schema = {
                "columns": {field: all_columns[field] for field in valid_fields},
                "total_columns": len(valid_fields),
                "selected_fields": valid_fields,
            }
 
            # Create unselected schema with remaining fields
            unselected_fields = [
                field for field in all_columns if field not in valid_fields
            ]
            new_unselected_schema = {
                "columns": {field: all_columns[field] for field in unselected_fields},
                "unselected_fields": unselected_fields,
            }
 
            # Update the pipeline with a single transaction
            with self.connection.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE superset_data_pipeline
                    SET updated_schema = %s, unselected_schema = %s,
                        db = %s, table_name = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE pipeline_id = %s
                """,
                    (
                        json.dumps(updated_schema),
                        json.dumps(new_unselected_schema),
                        db_name,
                        table_name,
                        pipeline_id,
                    ),
                )
                self.connection.commit()
 
                # Verify update was successful
                if cursor.rowcount <= 0:
                    print(
                        f"Update operation affected 0 rows for pipeline {pipeline_id}"
                    )
                    return False
 
                print(
                    f"Successfully updated schema for pipeline {pipeline_id} ({pipeline_name})"
                )
                print(
                    f"Selected {len(valid_fields)} fields, unselected {len(unselected_fields)} fields"
                )
                return True
 
        except Exception as e:
            self.connection.rollback()
            import traceback
 
            print(f"Error updating pipeline schema: {e}")
            print(f"Traceback: {traceback.format_exc()}")
            return False
 
    def get_existing_table_columns(self, table_name: str) -> Dict[str, str]:
        """
        Get existing columns and their types from a table.
 
        Args:
            table_name: Table name to check
 
        Returns:
            Dictionary mapping column names to their PostgreSQL data types
        """
        try:
            with self.connection.cursor() as cursor:
                # Use information_schema for better compatibility
                cursor.execute(
                    """
                    SELECT column_name, data_type, character_maximum_length
                    FROM information_schema.columns
                    WHERE table_name = %s
                    ORDER BY ordinal_position
                """,
                    (table_name,),
                )
 
                columns = {}
                for col_name, data_type, max_length in cursor.fetchall():
                    # Handle types with length specification
                    if max_length is not None and data_type == "character varying":
                        columns[col_name] = f"{data_type}({max_length})"
                    else:
                        columns[col_name] = data_type
 
                return columns
        except Exception as e:
            print(f"Error getting table columns: {e}")
            return {}
 
    def add_columns_to_table(
        self, table_name: str, new_columns: Dict[str, Dict]
    ) -> bool:
        """
        Add new columns to existing table using a single transaction.
        Handles special characters in column names by properly quoting them.
 
        Args:
            table_name: Table name to modify
            new_columns: Dictionary of new columns with their schema info
 
        Returns:
            Success status
        """
        try:
            if not new_columns:
                return True
            
            print("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++")
            print(f"Adding new columns to table {table_name}: {new_columns}")
            print("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++")
 
            # Build a single ALTER TABLE statement with multiple ADD COLUMN clauses
            alter_statements = []
            for col_name, col_info in new_columns.items():
                if not col_name or not col_name.strip():
                    print(f"Skipping invalid/empty column name: '{col_name}'")
                    continue  # Skip empty or invalid names

                sql_type = col_info.get("sql_type", "TEXT")

                # Replace dots with underscores for PostgreSQL compatibility
                safe_col_name = col_name.replace(".", "_")

                alter_statements.append(
                    f'ADD COLUMN IF NOT EXISTS "{safe_col_name}" {sql_type}'
                )
 
            # Execute as a single transaction
            with self.connection.cursor() as cursor:
                sql = f'ALTER TABLE "{table_name}" {", ".join(alter_statements)}'
                print(f"Executing SQL: {sql}")
                cursor.execute(sql)
                self.connection.commit()
                return True
 
        except Exception as e:
            self.connection.rollback()
            import traceback
 
            print(f"Error adding columns: {e}")
            print(f"Traceback: {traceback.format_exc()}")
            return False
 
    def alter_column_types(
        self, table_name: str, columns_to_alter: Dict[str, str]
    ) -> bool:
        """
        Alter column types in a table using a single transaction.
 
        Args:
            table_name: Table name to modify
            columns_to_alter: Dictionary mapping column names to new types
 
        Returns:
            Success status
        """
        try:
            if not columns_to_alter:
                return True
 
            # Build a single ALTER TABLE statement with multiple ALTER COLUMN TYPE clauses
            alter_statements = []
            for col_name, new_type in columns_to_alter.items():
                # Use USING clause for safe type conversion
                if new_type in ("BIGINT", "INTEGER"):
                    alter_statements.append(
                        f"ALTER COLUMN {col_name} TYPE {new_type} USING {col_name}::{new_type}"
                    )
                elif new_type in ("JSONB", "JSON"):
                    alter_statements.append(
                        f"ALTER COLUMN {col_name} TYPE {new_type} USING {col_name}::{new_type}"
                    )
                else:
                    alter_statements.append(f"ALTER COLUMN {col_name} TYPE {new_type}")
 
            # Execute as a single transaction
            with self.connection.cursor() as cursor:
                sql = f"ALTER TABLE {table_name} {', '.join(alter_statements)}"
                cursor.execute(sql)
                self.connection.commit()
                return True
 
        except Exception as e:
            self.connection.rollback()
            print(f"Error altering column types: {e}")
            return False
 
    def create_or_update_target_table(
        self, db_name: str, table_name: str, schema: Dict[str, Any]
    ) -> bool:
        """
        Create target table or update existing table with new columns and fix column types.
        Handles special characters in column names and column name conflicts.
 
        Args:
            db_name: Database name
            table_name: Table name to create or update
            schema: Schema information with selected fields
 
        Returns:
            Success status
        """
        try:
            columns = schema.get("columns", {})
            if not columns:
                print(f"No columns defined in schema")
                return False
 
            # Check if table exists
            with self.connection.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = %s
                    )
                """,
                    (table_name,),
                )
                table_exists = cursor.fetchone()[0]
 
            if table_exists:
                # Get existing columns with types
                existing_columns = self.get_existing_table_columns(table_name)
 
                # Find new columns to add and columns with type mismatches
                new_columns = {}
                columns_to_alter = {}
 
                for col_name, col_info in columns.items():
                    # Replace dots with underscores for PostgreSQL compatibility
                    safe_col_name = col_name.replace(".", "_")
                    expected_sql_type = col_info.get("sql_type", "TEXT").upper()
 
                    if safe_col_name not in existing_columns:
                        # New column to add
                        new_columns[col_name] = col_info
                    else:
                        # Check for type mismatch that needs upgrading
                        current_type = existing_columns[safe_col_name].upper()
                        if (
                            current_type == "INTEGER"
                            and expected_sql_type == "BIGINT"
                            or current_type == "TEXT"
                            and expected_sql_type in ("JSONB", "JSON")
                            or current_type == "DOUBLE PRECISION"
                            and expected_sql_type == "NUMERIC"
                        ):
                            columns_to_alter[safe_col_name] = expected_sql_type
 
                # Add new columns if any
                if new_columns and not self.add_columns_to_table(
                    table_name, new_columns
                ):
                    return False
 
                # Alter column types if needed
                if columns_to_alter and not self.alter_column_types(
                    table_name, columns_to_alter
                ):
                    return False
 
                return True
            else:
                # Create new table with a single transaction
                column_definitions = []
 
                # Check for column name conflicts with reserved names
                reserved_names = ["id", "pipeline_id", "pipeline_fetch_timestamp"]
 
                for col_name, col_info in columns.items():
                    # Replace dots with underscores for PostgreSQL compatibility
                    safe_col_name = col_name.replace(".", "_")
                    sql_type = col_info.get("sql_type", "TEXT")
 
                    # Handle column name conflicts with reserved names
                    if safe_col_name.lower() in [r.lower() for r in reserved_names]:
                        # Rename conflicting columns by adding a suffix
                        new_col_name = f"{safe_col_name}_data"
                        print(
                            f"Renaming column '{safe_col_name}' to '{new_col_name}' to avoid conflict with reserved name"
                        )
                        column_definitions.append(f'"{new_col_name}" {sql_type}')
                    else:
                        column_definitions.append(f'"{safe_col_name}" {sql_type}')
 
                # Add metadata columns
                column_definitions.extend(
                    [
                        "pipeline_fetch_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                        "pipeline_id INTEGER",
                    ]
                )
 
                create_table_sql = f"""
                CREATE TABLE "{table_name}" (
                    id SERIAL PRIMARY KEY,
                    {', '.join(column_definitions)}
                )
                """
 
                with self.connection.cursor() as cursor:
                    print(f"Creating table with SQL: {create_table_sql}")
                    cursor.execute(create_table_sql)
                    self.connection.commit()
                    return True
 
        except Exception as e:
            self.connection.rollback()
            import traceback
 
            print(f"Error creating/updating table: {e}")
            print(f"Traceback: {traceback.format_exc()}")
            return False

    def insert_data_to_table(
        self,
        table_name: str,
        data: pd.DataFrame,
        pipeline_id: int,
        selected_fields: List[str],
    ) -> bool:
        try:
            if data.empty:
                print("No data to insert")
                return True

            # Normalize columns for safer matching
            normalized_data_cols = {col.strip().rstrip("."): col for col in data.columns}
            normalized_selected_fields = [f.strip().rstrip(".") for f in selected_fields]
            
            available_fields = [
                normalized_data_cols[norm_col]
                for norm_col in normalized_selected_fields
                if norm_col in normalized_data_cols]

            print("+++++++++++++++++++++++++++++++++++++++++++++++++")
            print("available fileds :", available_fields)
            print("+++++++++++++++++++++++++++++++++++++++++++++++++")
            
            if not available_fields:
                print("No matching fields between selected fields and data")
                return False

            filtered_data = data[available_fields].copy()
            print("new filter data :", filtered_data)
            filtered_data["pipeline_id"] = pipeline_id

            # Get table schema
            existing_columns = self.get_existing_table_columns(table_name)
            column_types = {col.lower(): dtype.lower() for col, dtype in existing_columns.items()}
            print(f"[DEBUG] Existing Table Columns: {column_types}")

            # Reserved column name handling
            reserved_names = ["id", "pipeline_id", "pipeline_fetch_timestamp"]
            column_mapping = {}

            for col in filtered_data.columns:
                normalized_col = col.strip().rstrip(".")            # Remove extra dot at end
                safe_col = normalized_col.replace(".", "_")         # Replace internal dots

                if safe_col.lower() in [r.lower() for r in reserved_names]:
                    renamed = f"{safe_col}_data"
                    if renamed.lower() in column_types:
                        column_mapping[col] = renamed
                    else:
                        print(f"⚠️ Warning: '{col}' conflicts with reserved name but fallback column '{renamed}' not found")
                        continue
                else:
                    if safe_col.lower() in column_types:
                        column_mapping[col] = safe_col
                    else:
                        print(f"⚠️ Warning: Column '{col}' mapped to '{safe_col}' not found in DB")
                        continue

            # Rename DataFrame columns to match database schema
            filtered_data = filtered_data.rename(columns=column_mapping)
            print("Renamed data columns:", filtered_data.columns)

            # Double-check final column names
            final_columns = list(filtered_data.columns)
            
            print("Final Columns" ,final_columns )
            print("Column types :", column_types)

            # Drop columns that aren't in the actual DB table (column_types keys)
            cols_to_drop = [
                col for col in final_columns
                if col.lower() not in column_types and col != "pipeline_id"
            ]

            if cols_to_drop:
                print(f"[DEBUG] Dropping unmatched columns: {cols_to_drop}")
                filtered_data.drop(columns=cols_to_drop, inplace=True)

            # Confirm remaining fields
            print(f"[DEBUG] Final columns to insert: {filtered_data.columns}")

            if filtered_data.empty or len(filtered_data.columns) <= 1:
                print("No valid columns to insert after filtering")
                return False

            print(f"[DEBUG] Incoming Data:\n{json.dumps(filtered_data.to_dict(), indent=2)}")

            def is_nan_safe(val):
                try:
                    return pd.isna(val) if not isinstance(val, (list, dict, np.ndarray)) else False
                except Exception:
                    return False
            
            # Normalize and prepare values
            values = []
            for _, row in filtered_data.iterrows():
                row_values = []
                for col, val in row.items():
                    try:
                        if is_nan_safe(val):
                            val = None
                        elif isinstance(val, (np.integer, np.int64)):
                            val = int(val)
                        elif isinstance(val, (np.floating, np.float64)):
                            val = float(val)
                        elif isinstance(val, np.ndarray):
                            val = json.dumps(val.tolist(), ensure_ascii=False)
                        elif isinstance(val, (list, dict)):
                            val = json.dumps(val, ensure_ascii=False)
                        elif isinstance(val, str):
                            val = val.strip()
                            if (val.startswith("[") and val.endswith("]")) or (val.startswith("{") and val.endswith("}")):
                                try:
                                    parsed = ast.literal_eval(val)
                                    if isinstance(parsed, (list, dict)):
                                        val = json.dumps(parsed, ensure_ascii=False)
                                except Exception:
                                    pass
                    except Exception as parse_err:
                        print(f"[WARN] Failed to normalize value '{val}' in column '{col}' — {parse_err}")
                        val = None
                    row_values.append(val)
                values.append(tuple(row_values))
                
            print(f"[INFO] Prepared {len(values)} rows for insertion into {table_name}")

            # Prepare insert SQL
            columns = list(filtered_data.columns)
            sql_columns = ", ".join(f'"{col}"' for col in columns)
            sql_template = f'INSERT INTO "{table_name}" ({sql_columns}) VALUES %s'

            with self.connection.cursor() as cursor:
                execute_values(cursor, sql_template, values)
                self.connection.commit()
                print(f"✅ Successfully inserted {len(values)} rows into {table_name}")
                return True

        except Exception as e:
            self.connection.rollback()
            import traceback
            print(f"❌ Error inserting data: {e}")
            print(f"Traceback:\n{traceback.format_exc()}")
            return False

    def update_pipeline_status(
        self, pipeline_id: int, status: str, health: str = None
    ) -> bool:
        """
        Update pipeline status and health.
 
        Args:
            pipeline_id: Pipeline ID
            status: New status (active/inactive/error)
            health: New health status (healthy/unhealthy/unknown)
 
        Returns:
            Success status
        """
        try:
            with self.connection.cursor() as cursor:
                if health:
                    cursor.execute(
                        """
                        UPDATE superset_data_pipeline
                        SET status = %s, health = %s, updated_at = CURRENT_TIMESTAMP
                        WHERE pipeline_id = %s
                    """,
                        (status, health, pipeline_id),
                    )
                else:
                    cursor.execute(
                        """
                        UPDATE superset_data_pipeline
                        SET status = %s, updated_at = CURRENT_TIMESTAMP
                        WHERE pipeline_id = %s
                    """,
                        (status, pipeline_id),
                    )
 
                self.connection.commit()
                return True
 
        except Exception as e:
            self.connection.rollback()
            print(f"Error updating pipeline status: {e}")
            return False
 
    def get_active_pipelines(self) -> List[Dict[str, Any]]:
        """
        Get all active pipelines for background processing.
 
        Returns:
            List of active pipeline configurations
        """
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT pipeline_id, pipeline_name, api_curl, updated_schema,
                           table_name, frequency, api_key, headers
                    FROM superset_data_pipeline
                    WHERE status = 'active'
                """
                )
 
                pipelines = []
                for row in cursor.fetchall():
                    pipelines.append(
                        {
                            "pipeline_id": row[0],
                            "pipeline_name": row[1],
                            "api_curl": row[2],
                            "updated_schema": json.loads(row[3]) if row[3] else None,
                            "table_name": row[4],
                            "frequency": row[5],
                            "api_key": row[6],
                            "headers": json.loads(row[7]) if row[7] else None,
                        }
                    )
 
                return pipelines
 
        except Exception as e:
            print(f"Error getting active pipelines: {e}")
            return []
 
    def fix_existing_table_column_types(self, table_name: str) -> bool:
        """
        Fix column types in existing tables that may have INTEGER columns
        that should be BIGINT for large values.
 
        Args:
            table_name: Table name to fix
 
        Returns:
            Success status
        """
        try:
            # Get current column types
            with self.connection.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT column_name, data_type
                    FROM information_schema.columns
                    WHERE table_name = %s AND table_schema = 'public'
                    AND data_type = 'integer'
                    ORDER BY ordinal_position;
                """,
                    (table_name,),
                )
 
                integer_columns = [row[0] for row in cursor.fetchall()]
 
                # Remove metadata columns that should stay as INTEGER
                metadata_columns = ["id", "pipeline_id"]
                columns_to_fix = [
                    col for col in integer_columns if col not in metadata_columns
                ]
 
                if columns_to_fix:
                    print(
                        f"Found INTEGER columns that may need to be BIGINT: {columns_to_fix}"
                    )
 
                    # Convert INTEGER columns to BIGINT for data columns
                    columns_to_alter = {col: "BIGINT" for col in columns_to_fix}
                    return self.alter_column_types(table_name, columns_to_alter)
                else:
                    print(
                        f"No INTEGER columns found that need fixing in table {table_name}"
                    )
                    return True
 
        except Exception as e:
            print(f"Error fixing table column types: {e}")
            return False
 
    def __del__(self):
        """Close database connection when object is destroyed."""
        if hasattr(self, "connection"):
            self.connection.close()