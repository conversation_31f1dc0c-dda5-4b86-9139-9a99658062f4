# Superset Data Pipeline Package
# This package contains modules for dynamic data streaming pipeline functionality

from .superset_data_fetching import DataFetcher
from .dbs_pipeline import DatabaseManager

# Import task manager functions and variables
from .task_manager import (
    start_pipeline,
    stop_pipeline,
    stop_all_pipelines,
    get_pipeline_status,
    SCHEDULER_TYPE,
    REDIS_AVAILABLE
)

__all__ = [
    'DataFetcher',
    'DatabaseManager',
    'start_pipeline',
    'stop_pipeline',
    'stop_all_pipelines',
    'get_pipeline_status',
    'SCHEDULER_TYPE',
    'REDIS_AVAILABLE'
]
