import re
import os
import json
import logging
import requests
import tempfile
import psycopg2
from datetime import datetime
from dotenv import load_dotenv
from werkzeug.utils import secure_filename
from psycopg2.extras import RealDictCursor
from agents.llm_selector import LLMSelector
from utils.common import superset_connection
from langchain_ollama import OllamaEmbeddings
from langchain_qdrant import QdrantVectorStore
from .sorted_fetch import __smart_fetch_chart_data, __fetch_sample_data_json_data
from langchain_community.document_loaders import PyPDFLoader
from concurrent.futures import ThreadPoolExecutor, as_completed
from langchain_text_splitters import RecursiveCharacterTextSplitter
from typing import List, Tuple, Dict, Any
from qdrant_client.models import Filter, FieldCondition, MatchValue
from langchain_community.document_loaders import (
    TextLoader,
    CSVLoader,
    UnstructuredHTMLLoader,
    WebBaseLoader,
    UnstructuredWordDocumentLoader,
)
from langchain.schema import Document
from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient


load_dotenv()
is_docker = os.getenv("IS_DOCKER", "false").lower() == "true"
llm_host = "host.docker.internal" if is_docker else "localhost"
COOKIES = os.getenv("COOKIES")
OLLAMA_API_PORT = os.getenv("OLLAMA_API_PORT")

host = "db" if is_docker else "localhost"
core_url = "host.docker.internal" if is_docker else "localhost"
NGROK_URL = os.getenv("NGROK_URL", f"http://{llm_host}:{OLLAMA_API_PORT}")
embedding_model = OllamaEmbeddings(model="mxbai-embed-large", base_url=NGROK_URL)
QDRANT_HOST = os.environ.get("QDRANT_HOST", "http://qdrant:6333")
qdrant_client = QdrantClient(QDRANT_HOST)


def get_dashboard_changed_on(dashboard_id):

    cursor = superset_connection.cursor(cursor_factory=RealDictCursor)

    query = """
    SELECT changed_on 
    FROM dashboards
    WHERE id = %s
    """

    cursor.execute(query, (dashboard_id,))
    result = cursor.fetchone()

    if result:
        return result["changed_on"].isoformat()
    else:
        return None


def find_values_by_key(data, key):
    if isinstance(data, dict):
        for k, v in data.items():
            if k == key:
                yield v
            if isinstance(v, (dict, list)):
                yield from find_values_by_key(v, key)
    elif isinstance(data, list):
        for item in data:
            yield from find_values_by_key(item, key)


def get_dashboard_by_id(connection, dashboard_id):
    query = """
        SELECT id, dashboard_title, position_json, json_metadata, changed_on
        FROM dashboards
        WHERE id = %s;
    """

    try:
        with connection.cursor(cursor_factory=RealDictCursor) as cursor:
            cursor.execute(query, (dashboard_id,))
            result = cursor.fetchone()
            return result

    except psycopg2.Error as e:
        connection.rollback()
        print(f"An error occurred while querying the database: {e}")
        return None


def __extract_chart_ids(json_metadata, position_json):
    try:
        if not position_json:
            metadata = json.loads(json_metadata)
            charts_ids_global = metadata.get("global_chart_configuration", {}).get(
                "chartsInScope", []
            )
            return charts_ids_global
        charts_ids = list(find_values_by_key(json.loads(position_json), "chartId"))
        logging.info(f"Charts IDs: {charts_ids}")
        return charts_ids
    except (json.JSONDecodeError, ValueError, AttributeError) as e:
        print(f"An error occurred while parsing JSON metadata: {e}")
        return []


def __get_slices_by_ids(connection, slice_ids, dashboard_id, for_dashboard=False):
    dashboard_rag_updated_at = check_dashboard_embeddings_status(
        dashboard_id, superset_connection
    )

    # If updated_at doesn't exist, consider all slices as "new"
    updated_at_dt = None
    if dashboard_rag_updated_at and dashboard_rag_updated_at.get("updated_at"):
        updated_at_dt = dashboard_rag_updated_at["updated_at"]

    logging.info("\n\n")
    logging.info("*******************************")
    logging.info(f"Dashboard ID  : {dashboard_id}")
    try:
        with connection.cursor(cursor_factory=RealDictCursor) as cursor:
            if for_dashboard:
                query = """
                    SELECT id, slice_name, params, perm, datasource_id, query_context, changed_on
                    FROM slices
                    WHERE id = ANY(%s)
                    AND viz_type != 'mapbox';
                """
                cursor.execute(query, (slice_ids,))
                logging.info(f"IF selected")
            else:
                # No updated_at means all slices are considered
                query = """
                    SELECT id, slice_name, params, perm, datasource_id, query_context, changed_on
                    FROM slices
                    WHERE id = ANY(%s);
                """
                cursor.execute(query, (slice_ids,))
                logging.info(f"ELSE selected")

            results = cursor.fetchall()

            # logging.info(f"Results: {results}")
            logging.info("*******************************")
            logging.info("\n")

            return results

    except psycopg2.Error as e:
        connection.rollback()
        logging.error(f"An error occurred while querying the database: {e}")
        return []


def summarize_intro_text(intro_text, dashboard_id):
    """
    Summarizes key points from dashboard introductory text using an LLM.
    Checks if a summary already exists in the database before calling the LLM.
    """

    try:
        with superset_connection.cursor(cursor_factory=RealDictCursor) as cursor:
            # Check if summary already exists
            check_query = (
                "SELECT summary FROM dashboard_summaries WHERE dashboard_id = %s;"
            )
            cursor.execute(check_query, (dashboard_id,))
            result = cursor.fetchone()

            if result and result.get("summary"):
                return result["summary"]

        # Summary doesn't exist, proceed to call LLM
        llm_selector = LLMSelector()
        llm = llm_selector._initialize_llm(
            model_provider="openai", model_name="gpt-4o-mini"
        )

        prompt = f"""
        Extract key points from the following introductory text of a dashboard. 
        Focus on important definitions, full forms, and key descriptions. 
        Provide the summary as a concise bullet-point list.

        Introductory Text:
        {intro_text}

        Key Points Summary:
        """

        response = llm.invoke(prompt, max_tokens=100).content
        summary = response.strip() if response else "No key points extracted."

        # Save summary in database
        with superset_connection.cursor() as cursor:
            insert_query = """
            INSERT INTO dashboard_summaries (dashboard_id, summary) 
            VALUES (%s, %s) 
            ON CONFLICT (dashboard_id) 
            DO UPDATE SET summary = EXCLUDED.summary;
            """
            cursor.execute(insert_query, (dashboard_id, summary))
            superset_connection.commit()

        return summary

    except Exception as e:
        return f"Error in extracting key points: {e}"


def summarize_uploaded_files(payload: dict):
    """
    Processes files from a JSON payload containing FILEPREVIEW elements, extracts and summarizes content
    using LangChain loaders, stores summaries in the dashboard_file_summaries table,
    and indexes documents in Qdrant with metadata using LangChain's Qdrant integration.
    """
    for preview in payload.values():
        logging.info("--------PREVIEW--------")
        file_path = preview["meta"]["file_path"]
        description = preview["meta"]["description"]
        dashboard_id = preview["dashboard_id"]
        file_collection_name = f"document_summaries_{dashboard_id}"
        file_name = os.path.basename(file_path)

        logging.info(f"Dashboard ID: {dashboard_id}")
        qdrant = QdrantVectorStore(
            client=qdrant_client,
            collection_name=file_collection_name,
            embedding=embedding_model,
        )

        try:
            # Load file based on type
            if file_path.startswith(("http://", "https://")):
                try:
                    loader = WebBaseLoader(file_path)
                except Exception as e:
                    logging.error(f"Error loading URL {file_path}: {e}")
                    continue
            else:
                logging.info(f"File path: {file_path}")
                if not os.path.exists(file_path):
                    logging.info(f"File not found: {file_path}")
                    continue

                file_ext = file_path.split(".")[-1].lower()
                loader = None
                logging.info(f"File extension: {file_ext}")
                match file_ext:
                    case "csv":
                        loader = CSVLoader(file_path)
                    case "pdf":
                        loader = PyPDFLoader(file_path)
                    case "txt" | "text" | "md" | "rtf":
                        loader = TextLoader(file_path)
                    case "doc" | "docx":
                        loader = UnstructuredWordDocumentLoader(file_path)
                    case "html" | "htm":
                        loader = UnstructuredHTMLLoader(file_path)
                    case _:
                        logging.warning(f"Unsupported file type: {file_path}")
                        continue

            # Load text and pages
            documents = loader.load()
            text = "\n".join([doc.page_content for doc in documents]).strip()
            total_pages_new = documents[0].metadata.get("total_pages", len(documents))

            if not text:
                logging.info(f"No extractable content found in {file_path}")
                continue

            # Check existing document in Qdrant
            logging.info(
                f"Checking for existing documents in Qdrant using metadata filter... \n file_path: {file_path} \n file_name: {file_name}"
            )
            filter_conditions = [
                FieldCondition(
                    key="metadata.file_path", match=MatchValue(value=str(file_path))
                ),
                FieldCondition(
                    key="metadata.file_name", match=MatchValue(value=str(file_name))
                ),
            ]
            scroll_filter = Filter(must=filter_conditions)
            scroll_result = qdrant.client.scroll(
                collection_name=file_collection_name,
                scroll_filter=scroll_filter,
                limit=1,
                with_payload=True,
            )
            existing_docs = scroll_result[0]
            logging.info(f"Existing documents found: {len(existing_docs)}")
            if existing_docs:
                meta = existing_docs[0].payload
                existing_total_pages = meta.get("metadata").get("total_pages")
                logging.info(
                    f"Found existing file in Qdrant: {file_name} with {existing_total_pages} pages and total_pages_new: {total_pages_new}"
                )

                if existing_total_pages == total_pages_new:
                    logging.info(
                        f"Skipping {file_path} as it already exists with same page count"
                    )
                    continue
                else:
                    logging.info(
                        f"Page count mismatch. Deleting old vectors for {file_path}..."
                    )
                    qdrant.client.delete(
                        collection_name=file_collection_name,
                        filter=scroll_filter,
                    )

            # Rebuild metadata and re-add document
            metadata = {
                "file_path": file_path,
                "dashboard_id": dashboard_id,
                "file_name": file_name,
                "description": description,
                "total_pages": documents[0].metadata.get("total_pages", len(documents)),
            }

            documents = [
                Document(
                    page_content=doc.page_content, metadata={**doc.metadata, **metadata}
                )
                for doc in documents
            ]
            logging.info(
                f"Documents to be added: {len(documents)} and has total pages: {documents[0].metadata.get('total_pages')}"
            )
            qdrant.add_documents(documents)
            logging.info(f"Document added to Qdrant: {file_path}")

        except Exception as e:
            logging.error(
                f"Error processing file {file_path} in dashboard {dashboard_id}: {e}"
            )


def fetch_dashboard_summary(dashboard_id):
    """
    Fetches summaries for a given dashboard from both dashboard_summaries and dashboard_file_summaries tables.
    Returns a combined summary or appropriate message if no summaries are found.
    """
    dashboard_query = "SELECT summary FROM dashboard_summaries WHERE dashboard_id = %s;"

    files_query = """
    SELECT file_path, description 
    FROM files 
    WHERE dashboard_id = %s 
    ORDER BY file_path;
    """

    try:
        with superset_connection.cursor() as cursor:
            cursor.execute(dashboard_query, (dashboard_id,))
            dashboard_result = cursor.fetchone()

            cursor.execute(files_query, (dashboard_id,))
            file_results = cursor.fetchall()

            response_parts = []

            if dashboard_result:
                response_parts.append(f"DASHBOARD SUMMARY:\n{dashboard_result[0]}")

            if file_results:
                response_parts.append("\n\nFILE SUMMARIES:")
                for file_path, description in file_results:
                    response_parts.append(f"\n\nFile: {file_path}\n{description}")

            if response_parts:
                return "\n".join(response_parts)
            else:
                return "No summaries found for the given dashboard_id."

    except Exception as e:
        print(f"Error fetching summary for dashboard_id {dashboard_id}: {e}")
        return "Error retrieving summary."


def get_chart_names_by_dashboard_id(dashboard_id):
    """
    Fetches the list of chart names associated with a given dashboard ID.

    :param connection: Active PostgreSQL database connection
    :param dashboard_id: ID of the dashboard
    :return: List of chart names
    """

    dashboard = get_dashboard_by_id(superset_connection, dashboard_id)
    if not dashboard:
        print(f"No dashboard found with ID: {dashboard_id}")
        return []

    chart_ids = __extract_chart_ids(
        dashboard["json_metadata"], dashboard["position_json"]
    )
    if not chart_ids:
        print(f"No charts found in dashboard ID: {dashboard_id}")
        return []

    slices = __get_slices_by_ids(superset_connection, chart_ids, dashboard_id)

    charts_name = [slice["slice_name"] for slice in slices if "slice_name" in slice]

    return charts_name


def fetch_description_of_file(file_paths):
    results = {}
    for file_path in file_paths:

        query = "SELECT description FROM files WHERE file_path = %s;"

        try:
            with superset_connection.cursor() as cursor:
                cursor.execute(query, (file_path,))
                result = cursor.fetchone()

                if result:
                    results[file_path] = result[0]
                else:
                    results[file_path] = "No description found for the given file_path."

        except Exception as e:
            print(f"Error fetching description for file_path {file_path}: {e}")
            return "Error retrieving description."

    return json.dumps(results)


def __fetch_csrf_token(dashboard_id):
    url = f"http://{core_url}:8088/api/public/csrf-token"

    headers = {
        "Accept-Encoding": "application/json",
        "Accept-Language": "en-US,en;q=0.9",
        "Connection": "keep-alive",
        "Cookie": COOKIES,
        "Host": f"{core_url}:8088",
        "Referer": f"http://{core_url}:8088/superset/dashboard/{dashboard_id}/",
    }

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        csrf_token = json.loads(response.content)
        return csrf_token.get("csrfToken")
    else:
        return {
            "error": "Failed to fetch CSRF token",
            "status_code": response.status_code,
        }


def __execute_query_in_new_db(slice, dashboard_id):
    try:
        query_context = slice["query_context"]
        query_context = json.loads(query_context)
        if not query_context:
            query_context = slice["params"]

        csrf_token = __fetch_csrf_token(dashboard_id)
        data = __smart_fetch_chart_data(
            slice["id"], dashboard_id, query_context, csrf_token
        )
        return data

    except Exception as e:
        print(f"An error occurred while executing the query in the new database: {e}")
        return []


def extract_markdown_text(payload):
    markdown_data = []
    payload = json.loads(payload)

    for element_id, element_content in payload.items():
        if element_id.startswith("MARKDOWN"):
            code_content = element_content.get("meta", {}).get("code", "")
            refined_lines = clean_markdown_code(code_content)
            markdown_data.extend(refined_lines)

    return markdown_data


def extract_filepreview_metadata(payload: str) -> List[Tuple[str, str, str]]:
    """
    Extracts file path, description, and dashboard ID from FILEPREVIEW elements in a JSON payload.

    Args:
    payload: JSON string containing the data to parse

    Returns:
    List of tuples containing (file_path, description, dashboard_id) for each FILEPREVIEW element
    """
    filepreview_metadata: List[Tuple[str, str, str]] = []
    payload_dict: Dict[str, Any] = json.loads(payload)

    for element_id, element_content in payload_dict.items():
        if element_id.startswith("FILE_PREVIEW"):
            meta = element_content.get("meta", {})
            file_path: str = meta.get(
                "filePath", "/app/superset/static/pdfs/First_page.pdf"
            )
            description: str = meta.get("description", "First Page description")

            filepreview_metadata.append((file_path, description))
    logging.info("----------------")
    logging.info(filepreview_metadata)
    logging.info("----------------")
    return filepreview_metadata


def clean_markdown_code(code_content):
    import re

    clean_text = re.sub(r"<[^>]+>", "", code_content)
    lines = clean_text.split("\n")

    meaningful_lines = []
    for line in lines:
        line = line.strip()

        if not line or re.match(r"^[#\-* ]+$", line):
            continue

        if len(line.split()) == 1 and line.isupper():
            continue

        if re.search(r"(import|plt\.\w+|figure|hist|show\(\)|data\.)", line):
            continue

        meaningful_lines.append(line)

    return meaningful_lines


def table_data_fetch(dashboard_id):
    dashboard = get_dashboard_by_id(superset_connection, dashboard_id)
    csrf_token = __fetch_csrf_token(dashboard_id)
    if dashboard:
        chart_ids = __extract_chart_ids(
            dashboard["json_metadata"], dashboard["position_json"]
        )
        slices = __get_slices_by_ids(
            superset_connection, chart_ids, dashboard_id, for_dashboard=True
        )

    table_names = []

    for slice_ in slices:
        perm = slice_.get("perm", "")
        # Extract table name and ID from perm string
        match = re.match(r"\[.*?\]\.\[(.*?)\]\(id:(\d+)\)", perm)
        if match:
            _, table_id = match.groups()
            table_id = int(table_id)

            with superset_connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(
                    "SELECT table_name, sql, database_id FROM tables WHERE id = %s",
                    (table_id,),
                )
                result = cursor.fetchone()
                if not result:
                    continue

                db_id = result["database_id"]
                table_name = result["table_name"]
                sql_query = result.get("sql")

                if sql_query:
                    # Virtual table — extract physical table(s)
                    physical_tables = re.findall(
                        r'FROM\s+"([^"]+)"', sql_query, re.IGNORECASE
                    )
                    table_names.extend(physical_tables)
                else:
                    # Physical table — fetch DB connection info
                    table_names.append(table_name)

    return list(set(table_names))


def slice_data_fetch(dashboard_id):
    dashboard = get_dashboard_by_id(superset_connection, dashboard_id)
    csrf_token = __fetch_csrf_token(dashboard_id)
    if dashboard:
        chart_ids = __extract_chart_ids(
            dashboard["json_metadata"], dashboard["position_json"]
        )
        slices = __get_slices_by_ids(
            superset_connection, chart_ids, dashboard_id, for_dashboard=True
        )

    data = {}
    for slice_ in slices:
        query_context = slice_["query_context"]
        query_context = json.loads(query_context)
        if not query_context:
            query_context = slice_["params"]
        data_json = __fetch_sample_data_json_data(
            slice_["id"], dashboard_id, query_context, csrf_token, 1
        )
        data[(slice_["slice_name"])] = data_json

    # file_path = os.path.join(f"dashboard_{dashboard_id}_data.json")

    # # Write data to JSON file
    # with open(file_path, "w", encoding="utf-8") as f:
    #     json.dump(data, f, indent=2, ensure_ascii=False)
    return data


def main_dashboard(dashboard_id):
    logging.info("----" * 10)
    dashboard_rag_updated_at = check_dashboard_embeddings_status(
        dashboard_id, superset_connection
    )
    dashboard = get_dashboard_by_id(superset_connection, dashboard_id)
    if not dashboard or "changed_on" not in dashboard:
        return ("Dashboard not found or missing 'changed_on'.", {})

    changed_on_dt = dashboard["changed_on"]
    updated_at_dt = (
        dashboard_rag_updated_at.get("updated_at") if dashboard_rag_updated_at else None
    )
    logging.info(f"changed_on_dt: {changed_on_dt}, updated_at_dt: {updated_at_dt}")

    # Check if any associated file is newer than embeddings
    file_previews = []
    file_changed = False
    try:
        if "position_json" in dashboard:
            extracted_previews = extract_filepreview_metadata(
                dashboard["position_json"]
            )
            for file_path, description in extracted_previews:
                with superset_connection.cursor() as cursor:
                    cursor.execute(
                        """
                        SELECT updated_at FROM files 
                        WHERE file_path = %s AND upload_type = 'dashboard'
                        """,
                        (file_path,),
                    )
                    result = cursor.fetchone()
                    if result:
                        file_updated_at = result[0]
                        logging.info(
                            f"File {file_path} updated at: {file_updated_at} dashboard updated at: {updated_at_dt}"
                        )
                        if updated_at_dt and file_updated_at > updated_at_dt:
                            file_changed = True
                            logging.info(
                                f"File {file_path} is newer than embeddings, appending to file_previews."
                            )
                        else:
                            logging.info(
                                f"File {file_path} is older or updated after embeddings."
                            )
                            file_previews.append(
                                {"file_path": file_path, "description": description}
                            )
    except (json.JSONDecodeError, KeyError) as e:
        logging.error(f"Error extracting file preview metadata: {e}")

    # Skip processing if dashboard and files are both older
    if updated_at_dt and changed_on_dt <= updated_at_dt and not file_changed:
        logging.info(f"Dashboard and files are both older")
        logging.info("Already up to date. No update needed.")
        logging.info("----" * 10)
        return ("Already up to date. No update needed.", {})

    # Proceed only with changed charts or new files
    data = {}

    # Only process changed or new charts
    chart_ids = __extract_chart_ids(
        dashboard.get("json_metadata"), dashboard.get("position_json")
    )
    slices = __get_slices_by_ids(superset_connection, chart_ids, dashboard_id)

    def process_slice(slice_):
        result = __execute_query_in_new_db(slice_, dashboard_id)
        return slice_["slice_name"], f"{(result.get('result')[0].get('data'))}"

    with ThreadPoolExecutor() as executor:
        future_to_slice = {
            executor.submit(process_slice, slice_): slice_ for slice_ in slices
        }
        for future in as_completed(future_to_slice):
            slice_name, result_data = future.result()
            data[slice_name] = result_data

    # Only include updated or new files
    if file_previews:
        data["file_previews"] = file_previews

    data["Introductory_text"] = extract_markdown_text(dashboard["position_json"])
    logging.info("----" * 20)
    return None, data


def check_dashboard_embeddings_status(dashboard_id, connection):
    logging.info("Checking dashboard embeddings status...")

    query = "SELECT embeddings_created, updated_at FROM dashboard_metadata WHERE dashboard_id = %s"

    with connection.cursor(cursor_factory=RealDictCursor) as cursor:
        cursor.execute(query, (str(dashboard_id),))
        result = cursor.fetchone()
        logging.info(f"Query Result: {result}")

        return result if result else None


def is_rag_needed(dashboard_id, connection):
    """
    Returns True if RAG is needed (i.e., dashboard was updated after embeddings were created or no embeddings exist),
    False otherwise.
    """
    logging.info("\n")
    logging.info("--" * 20)

    # Step 1: Get the dashboard's last changed timestamp
    dashboard = get_dashboard_by_id(connection, dashboard_id)
    if not dashboard:
        logging.warning(f"No dashboard found with id: {dashboard_id}")
        return True  # Assume RAG needed if dashboard info is missing

    dashboard_changed_on = dashboard.get("changed_on")
    if not dashboard_changed_on:
        logging.warning("Dashboard 'changed_on' not available.")
        return True

    # Step 2: Get embedding status and updated_at from dashboard_metadata
    embedding_meta = check_dashboard_embeddings_status(dashboard_id, connection)

    # If no embeddings created yet
    if not embedding_meta or not embedding_meta.get("embeddings_created"):
        logging.warning("Embeddings not found or not yet created.")
        return True

    embeddings_updated_at = embedding_meta.get("updated_at")
    if not embeddings_updated_at:
        logging.warning("Embeddings updated_at missing.")
        return True

    # Step 3: Compare timestamps
    is_needed = dashboard_changed_on > embeddings_updated_at
    logging.info(
        f"Dashboard changed on: {dashboard_changed_on}, Embeddings updated at: {embeddings_updated_at}"
    )
    logging.info(f"RAG Needed: {is_needed}")
    logging.info("--" * 20)
    logging.info("\n")

    return is_needed


def update_dashboard_embeddings_status(
    dashboard_id, status, connection, document_length
):
    print(
        f"Inside to insert the value in postgres with {dashboard_id}, {status}, {document_length}"
    )
    query = """
    INSERT INTO dashboard_metadata (dashboard_id, embeddings_created, updated_at, document_length)
    VALUES (%s, %s, %s, %s)
    ON CONFLICT (dashboard_id)
    DO UPDATE SET embeddings_created = %s, updated_at = %s, document_length = %s
    """
    now = datetime.utcnow()
    with connection.cursor() as cursor:
        cursor.execute(
            query,
            (
                str(dashboard_id),
                status,
                now,
                str(document_length),
                status,
                now,
                str(document_length),
            ),
        )
        connection.commit()


def delete_embeddings_from_vectorstore(collection_name, embedding_model):
    from qdrant_client.http import models

    vectorstore = QdrantVectorStore(
        client=qdrant_client,
        collection_name=collection_name,
        embedding=embedding_model,
    )
    logging.info(f"Deleting all embeddings from vectorstore: {collection_name}")
    vectorstore.client.delete(
        collection_name=collection_name,
        points_selector=models.FilterSelector(filter=models.Filter(must=[])),
    )


def delete_embeddings(
    dashboard_id, connection, collection_name, connection_string, embedding_model
):
    query = "DELETE FROM dashboard_metadata WHERE dashboard_id = %s"
    # delete_embeddings_from_vectorstore(collection_name, connection_string, embedding_model)
    try:
        with connection.cursor() as cursor:
            cursor.execute(query, (dashboard_id,))
            connection.commit()
            delete_embeddings_from_vectorstore(collection_name, embedding_model)
            if cursor.rowcount > 0:
                return f"Row with dashboard_id {dashboard_id} was successfully deleted."
            else:
                return f"No row found with dashboard_id {dashboard_id}."
    except Exception as e:
        connection.rollback()
        return f"An error occurred while deleting the row: {e}"


def get_dashboard_embeddings_len(dashboard_id, connection):
    query = "SELECT document_length FROM dashboard_metadata WHERE dashboard_id = %s"
    with connection.cursor(cursor_factory=RealDictCursor) as cursor:
        cursor.execute(query, (str(dashboard_id),))
        result = cursor.fetchone()
        return int(result["document_length"]) if result else None


def file_upload_agent(description_json, files, upload_type, dashboard_id):
    logging.info("----" * 20)
    logging.info("File upload agent started")
    uploaded_files = []

    try:
        with superset_connection.cursor() as cursor:
            for file in files:
                if not file.filename:
                    continue

                filename = secure_filename(file.filename)
                description = description_json.get(file.filename, "")
                logging.info(f"File name: {filename}, Description: {description}")
                if upload_type not in ["chat", "dashboard"]:
                    raise ValueError("upload_type must be either 'chat' or 'dashboard'")

                # Check if file exists
                cursor.execute(
                    "SELECT file_path, description FROM files WHERE file_name = %s AND dashboard_id = %s;",
                    (filename, int(dashboard_id)),
                )
                existing_file = cursor.fetchone()

                is_new_file = False

                if existing_file:
                    logging.info(f"----" * 10)
                    logging.info("File already exists in database.")
                    existing_path, _ = existing_file
                    cursor.execute(
                        """
                        UPDATE files 
                        SET description = %s, updated_at = CURRENT_TIMESTAMP 
                        WHERE file_name = %s
                        """,
                        (description, filename),
                    )
                    file_path = existing_path
                    logging.info(
                        f"Updated description for file: {filename}, {file_path}"
                    )
                    logging.info(f"----" * 10)
                else:
                    # Save new file
                    logging.info(f"----" * 10)
                    logging.info(f"Saving new file: {filename}")
                    temp_dir = tempfile.mkdtemp()
                    file_path = os.path.join(temp_dir, filename)
                    file.save(file_path)

                    cursor.execute(
                        """
                        INSERT INTO files (file_name, file_path, description, upload_type, dashboard_id) 
                        VALUES (%s, %s, %s, %s, %s)
                        RETURNING file_name
                        """,
                        (filename, file_path, description, upload_type, dashboard_id),
                    )
                    cursor.fetchone()[0]
                    is_new_file = True

                uploaded_files.append(
                    {
                        "file_name": filename,
                        "file_path": file_path,
                        "description": description,
                        "updated_at": datetime.utcnow().isoformat(),
                    }
                )
                logging.info(f"File saved: {filename}, {file_path}")
                logging.info(f"----" * 10)

                # Only generate embeddings if this is a new file and upload_type is "chat"
                if upload_type == "chat":
                    file_ext = file_path.split(".")[-1].lower()
                    loader = None

                    match file_ext:
                        case "csv":
                            # loader = CSVLoader(file_path)
                            continue
                        case "pdf":
                            loader = PyPDFLoader(file_path)
                        case "txt" | "text" | "md" | "rtf":
                            loader = TextLoader(file_path)
                        case "doc" | "docx":
                            loader = UnstructuredWordDocumentLoader(file_path)
                        case "html" | "htm":
                            loader = UnstructuredHTMLLoader(file_path)
                        case _:
                            print(f"Unsupported file type: {file_path}")
                            continue

                    documents = loader.load()
                    text = "\n".join([doc.page_content for doc in documents]).strip()

                    logging.info("--------DOCUMENTS--------")
                    logging.info(text[:40])
                    logging.info("----------------")

                    # Embed the documents
                    text_splitter = RecursiveCharacterTextSplitter(
                        chunk_size=400, chunk_overlap=50
                    )
                    splits = text_splitter.split_documents(documents)

                    logging.info("[Start] Embeddings for file....")

                    vectorstore = QdrantVectorStore.from_documents(
                        documents=splits,
                        embedding=embedding_model,
                        collection_name=filename,
                        location=os.environ.get("QDRANT_HOST", "http://qdrant:6333"),
                    )

                    logging.info("[END] Embeddings for file....")

            superset_connection.commit()
            logging.info(
                f"File upload agent completed \n Return object {uploaded_files}"
            )
            logging.info("----" * 20)
            return uploaded_files
    except Exception as e:
        superset_connection.rollback()
        logging.error(f"Error in file upload agent: {e}")
        return []


def get_last_two_conversations(message_id, dashboard_id, model_name):

    # Validate required parameters
    if not all([message_id, dashboard_id, model_name]):
        return {
            "error": "Missing required parameters: message_id, dashboard_id, model_name"
        }

    try:
        with superset_connection.cursor() as cursor:
            # Fetch all chat messages for the dashboard and model
            cursor.execute(
                """
                SELECT id, role, content, datetime, is_liked, is_disliked, comment
                FROM chat_history
                WHERE dashboard_id = %s AND model_name = %s
                ORDER BY datetime ASC
                """,
                (dashboard_id, model_name),
            )
            rows = cursor.fetchall()

            # Convert to dict for easier handling
            messages = [
                {
                    "id": row[0],
                    "role": row[1],
                    "content": row[2],
                    "datetime": row[3],
                    "is_liked": row[4],
                    "is_disliked": row[5],
                    "comment": row[6] if row[6] else "",
                }
                for row in rows
            ]

            # Find index of the current user message (we want to exclude this)
            current_user_index = next(
                (
                    i
                    for i, msg in enumerate(messages)
                    if msg["id"] == message_id and msg["role"] == "user"
                ),
                None,
            )

            if current_user_index is None:
                return {"error": "User message not found"}

            # Get messages before the current user message
            previous_messages = messages[:current_user_index]

            # Find conversation pairs (user-bot pairs) in reverse order
            conversation = []
            pairs_collected = 0
            i = len(previous_messages) - 1

            # Look for pairs in reverse chronological order
            while i >= 1 and pairs_collected < 2:
                # Check if we have a bot message followed by a user message (in reverse chronological order)
                if (
                    previous_messages[i]["role"] == "bot"
                    and previous_messages[i - 1]["role"] == "user"
                ):

                    user_msg = previous_messages[i - 1].copy()
                    bot_msg = previous_messages[i].copy()

                    # Transform the messages to required schema
                    # For user message: role, content, comment (if present)
                    transformed_user = {
                        "role": user_msg["role"],
                        "content": user_msg["content"],
                    }
                    if user_msg["comment"]:
                        transformed_user["comment"] = user_msg["comment"]

                    # For bot message: extract the final response and create schema
                    bot_content = ""
                    try:
                        import json

                        bot_data = json.loads(bot_msg["content"])
                        # Extract the final response from combiner_agent
                        final_response = (
                            bot_data.get("data", [])[-1]
                            .get("combiner_agent", {})
                            .get("agent_outputs", {})
                            .get("combiner_agent", {})
                            .get("response", "")
                        )
                        bot_content = final_response
                    except (json.JSONDecodeError, IndexError, KeyError, AttributeError):
                        # Fallback to original content if parsing fails
                        bot_content = bot_msg["content"][:500]

                    transformed_bot = {"role": bot_msg["role"], "content": bot_content}
                    if bot_msg["comment"]:
                        transformed_bot["comment"] = bot_msg["comment"]
                        if bot_msg["is_liked"]:
                            transformed_bot["is_liked"] = bot_msg["is_liked"]
                        if bot_msg["is_disliked"]:
                            transformed_bot["is_disliked"] = bot_msg["is_disliked"]

                    # Insert at beginning to maintain chronological order
                    conversation.insert(0, transformed_bot)
                    conversation.insert(0, transformed_user)

                    pairs_collected += 1
                    i -= 2  # Move back by 2 to find next pair
                else:
                    i -= 1  # Keep searching in reverse

            return {"chat_history": conversation}

    except Exception as e:
        return {"error": str(e)}
