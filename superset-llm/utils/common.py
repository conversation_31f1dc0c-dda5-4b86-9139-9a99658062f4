import os
import psycopg2
import subprocess
import random
import json
from dotenv import load_dotenv

from qdrant_client import QdrantClient
from qdrant_client.http.models import VectorParams, Distance


QDRANT_HOST = os.environ.get("QDRANT_HOST", "http://qdrant:6333")
qdrant_client = QdrantClient(QDRANT_HOST)

PREDEFINED_QUESTIONS = [
    "What question should I ask and why, then ask yourself that question?",
    "What is the overall battalion lethality rating, how did you reach that answer, and provide recommendations on how to increase the rating?",
    "Which is the best FRT and why?",
    "How many unserviceable vehicles do I have, and how could I improve this?",
    "I have a space on a Challenger course, who should I send and why, and what effect will that have on Battalion lethality index?",
    "Please provide information on the consequences of sending Sgt class 1 VM vehicle inspector to the Falkland Islands for 3 months, and effect on lethality rating.",
    "Who is the most experienced armourer, and why?",
    "Who is the most experienced VM and why?",
    "Who is the most experienced technician and why?",
    "Given current equipment availability, how can I improve overall availability?",
    "How would you suggest reorganizing the FRTs to increase overall lethality rating?",
    "Are the RECs up to date, if not indicate which ones require updating?",
]


load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OLLAMA_API_PORT = os.getenv("OLLAMA_API_PORT")
is_docker = os.getenv("IS_DOCKER", "false").lower() == "true"
llm_host = "host.docker.internal" if is_docker else "localhost"


host = "db" if is_docker else "localhost"
core_url = "host.docker.internal" if is_docker else "localhost"

superset_db = {
    "dbname": "superset",
    "user": "superset",
    "password": "superset",
    "host": host,
    "port": 5432,
}

superset_connection = psycopg2.connect(**superset_db)


def ensure_extension_exists():
    """Check if a column exists in a PostgreSQL table, and add it if missing."""

    try:
        conn = psycopg2.connect(
            host="vectordb",
            port=5432,
            database="vectordb",
            user="testuser",
            password="testpwd",
        )
        cursor = conn.cursor()
        cursor.execute("CREATE EXTENSION IF NOT EXISTS vector")
        cursor.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm;")
        cursor.execute(
            "CREATE INDEX idx_description_trgm ON files USING gin (description gin_trgm_ops);"
        )
        conn.commit()
        cursor.close()
        conn.close()

    except psycopg2.Error as e:
        print(f"Error checking/adding column: {e}")


def create_chat_history_table():
    """
    Create or update the chat_history table with necessary columns for feedback.
    Also creates the agent_performance_log table if it doesn't exist.
    """
    conn = None
    try:
        conn = psycopg2.connect(**superset_db)
        cursor = conn.cursor()

        # First check if chat_history table exists
        cursor.execute(
            """
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_name = 'chat_history'
        );
        """
        )
        table_exists = cursor.fetchone()[0]

        if not table_exists:
            # Create the chat_history table if it doesn't exist
            cursor.execute(
                """
            CREATE TABLE IF NOT EXISTS chat_history (
                id SERIAL PRIMARY KEY,
                dashboard_id INTEGER,
                dataset_id INTEGER,
                model_name VARCHAR(100) NOT NULL,
                role VARCHAR(20) NOT NULL,
                content TEXT NOT NULL,
                datetime TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_liked BOOLEAN DEFAULT FALSE,
                is_disliked BOOLEAN DEFAULT FALSE,
                comment TEXT DEFAULT '',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            )
            print("Created chat_history table")
        else:
            # Add columns if they don't exist
            cursor.execute(
                """
            DO $$
            BEGIN
                IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                              WHERE table_name='chat_history' AND column_name='liked') THEN
                    ALTER TABLE chat_history ADD COLUMN liked BOOLEAN DEFAULT FALSE;
                END IF;

                IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                              WHERE table_name='chat_history' AND column_name='disliked') THEN
                    ALTER TABLE chat_history ADD COLUMN disliked BOOLEAN DEFAULT FALSE;
                END IF;

                IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                              WHERE table_name='chat_history' AND column_name='comment') THEN
                    ALTER TABLE chat_history ADD COLUMN comment TEXT DEFAULT '';
                END IF;

                IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                              WHERE table_name='chat_history' AND column_name='updated_at') THEN
                    ALTER TABLE chat_history ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
                END IF;
            END
            $$;
            """
            )
            print("Updated chat_history table with feedback columns")

        # Create agent_performance_log table
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS agent_performance_log (
            id SERIAL PRIMARY KEY,
            message_id INTEGER,
            agent_name VARCHAR(100),
            query_type VARCHAR(50),
            execution_time FLOAT,
            satisfaction_score FLOAT,
            feedback_type VARCHAR(20),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        )

        # Add foreign key if possible (only if message_id exists in chat_history)
        cursor.execute(
            """
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.table_constraints
                WHERE constraint_name = 'agent_performance_log_message_id_fkey'
            ) THEN
                -- Check if we can safely add the foreign key
                IF EXISTS (
                    SELECT 1 FROM information_schema.columns
                    WHERE table_name = 'chat_history' AND column_name = 'id'
                ) THEN
                    ALTER TABLE agent_performance_log
                    ADD CONSTRAINT agent_performance_log_message_id_fkey
                    FOREIGN KEY (message_id) REFERENCES chat_history(id);
                END IF;
            END IF;
        END
        $$;
        """
        )

        # Commit the transaction
        conn.commit()
        print(
            "Successfully created/updated chat_history and agent_performance_log tables"
        )

    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Error creating/updating chat_history table: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


def insert_chat_history(model_name, role, content, dashboard_id=None, dataset_id=None):
    try:
        cursor = superset_connection.cursor()

        inserted_id = None

        if dashboard_id:
            insert_query = """
                INSERT INTO chat_history (dashboard_id, model_name, role, content)
                VALUES (%s, %s, %s, %s) RETURNING id;
            """
            cursor.execute(insert_query, (dashboard_id, model_name, role, content))
        elif dataset_id:
            insert_query = """
                INSERT INTO chat_history (dataset_id, model_name, role, content)
                VALUES (%s, %s, %s, %s) RETURNING id;
            """
            cursor.execute(insert_query, (dataset_id, model_name, role, content))

        inserted_id = cursor.fetchone()[0]

        superset_connection.commit()
        cursor.close()

        return inserted_id

    except Exception as e:
        print(f"Error inserting into chat_history table: {e}")
        return None


def create_preference_table_if_not_exists():
    """Creates the preference table if it does not already exist."""
    create_table_query = """
    CREATE TABLE IF NOT EXISTS preferences (
        id SERIAL PRIMARY KEY,
        model_provider TEXT NOT NULL,
        model_name TEXT UNIQUE NOT NULL,
        temperature REAL,
        top_p REAL,
        top_k INTEGER,
        repetition_penalty REAL,
        created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    with superset_connection.cursor() as cursor:
        cursor.execute(create_table_query)
    superset_connection.commit()


def create_metadata_table():
    """
    Create the dataset_metadata table if it doesn't already exist.
    """
    query = """
    CREATE TABLE IF NOT EXISTS dataset_metadata (
        dataset_id VARCHAR PRIMARY KEY,
        embeddings_created BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        document_length VARCHAR
    );
    """
    try:
        with superset_connection.cursor() as cursor:
            cursor.execute(query)
            superset_connection.commit()
    except Exception as e:
        print(f"Error creating table `dataset_metadata`: {e}")
        superset_connection.rollback()


def create_metadata_dashboard():
    """
    Create the dataset_metadata table if it doesn't already exist.
    """
    query = """
    CREATE TABLE IF NOT EXISTS dashboard_metadata (
        dashboard_id VARCHAR PRIMARY KEY,
        embeddings_created BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        document_length VARCHAR
    );
    """
    try:
        with superset_connection.cursor() as cursor:
            cursor.execute(query)
            superset_connection.commit()
    except Exception as e:
        print(f"Error creating table `dashboard_metadata`: {e}")
        superset_connection.rollback()


def create_dashboard_summaries_table():
    """
    Create the `dashboard_summaries` table if it doesn't already exist.
    Stores summarized key points from dashboard introductory text.
    """
    query = """
    CREATE TABLE IF NOT EXISTS dashboard_summaries (
        dashboard_id INT PRIMARY KEY,
        summary TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    try:
        with superset_connection.cursor() as cursor:
            cursor.execute(query)
            superset_connection.commit()
    except Exception as e:
        print(f"Error creating table `dashboard_summaries`: {e}")
        superset_connection.rollback()


def create_dashboard_files_table():
    create_table_query = """
    CREATE TABLE IF NOT EXISTS files (
        id SERIAL PRIMARY KEY,
        file_name TEXT NOT NULL,
        file_path TEXT UNIQUE NOT NULL,
        upload_type TEXT NOT NULL,
        dashboard_id INT,
        description TEXT,
        summary TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE (dashboard_id, file_path)
    );
    """

    create_trigger_function = """
    CREATE OR REPLACE FUNCTION update_timestamp()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """

    create_trigger = """
    CREATE OR REPLACE TRIGGER trigger_update_files
    BEFORE UPDATE ON files
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();
    """

    try:
        with superset_connection.cursor() as cursor:
            cursor.execute(create_table_query)
            cursor.execute(create_trigger_function)
            cursor.execute(create_trigger)
        superset_connection.commit()
        print("Unified table `files` ensured.")
    except Exception as e:
        print(f"Error creating unified table: {e}")
        superset_connection.rollback()


def chat_search_live(search_query, dashboard_id=None, dataset_id=None):
    """Fetch live search results from chat history, most asked questions, and predefined questions."""

    # if not dashboard_id and not dataset_id:
    #     return filter_questions(search_query, PREDEFINED_QUESTIONS)

    try:

        results = set()
        keywords = extract_keywords(search_query)

        # Fetch from chat_history
        results.update(search_chat_history(keywords, dashboard_id, dataset_id))

        # Fetch from most asked questions
        if dashboard_id:
            results.update(search_most_asked_questions(keywords, dashboard_id))

        # Fetch from predefined questions
        # results.update(filter_questions(search_query, PREDEFINED_QUESTIONS))

        return list(results)

    except Exception as e:
        print(f"Error fetching chat search results: {e}")
        return []


def extract_keywords(query):
    """Extract relevant keywords from the search query."""
    return [word.strip() for word in query.split() if word.strip()]


def search_chat_history(keywords, dashboard_id=None, dataset_id=None):
    """Search chat history for relevant questions matching keywords."""

    results = set()
    try:
        with superset_connection.cursor() as cursor:
            sql_query = """
                SELECT DISTINCT content, datetime
                FROM chat_history
                WHERE role = 'user'
            """
            params = []

            if dashboard_id:
                sql_query += " AND dashboard_id = %s"
                params.append(dashboard_id)
            if dataset_id:
                sql_query += " AND dataset_id = %s"
                params.append(dataset_id)

            sql_query += " AND ("
            sql_query += " OR ".join(["content ILIKE %s" for _ in keywords])
            sql_query += ") ORDER BY datetime DESC LIMIT 10;"

            params.extend([f"%{kw}%" for kw in keywords])
            cursor.execute(sql_query, tuple(params))
            results.update(row[0] for row in cursor.fetchall())
    except Exception as e:
        import logging

        superset_connection.rollback()
        logging.error(f"Error fetching chat history: {e}")
    return results


def search_most_asked_questions(keywords, dashboard_id):
    """Fetch the most frequently asked questions containing keywords."""

    results = set()
    try:

        with superset_connection.cursor() as cursor:
            query = (
                """
                SELECT content
                FROM chat_history
                WHERE dashboard_id = %s AND role = 'user'
                GROUP BY content
                HAVING """
                + " OR ".join(["content ILIKE %s" for _ in keywords])
                + """
                ORDER BY COUNT(*) DESC
                LIMIT 10;
            """
            )
            params = [dashboard_id] + [f"%{kw}%" for kw in keywords]
            cursor.execute(query, tuple(params))
            results.update(row[0] for row in cursor.fetchall())
    except Exception as e:
        print(f"Error fetching most asked questions: {e}")
        superset_connection.rollback()
    return results


def filter_questions(search_query, questions):
    """Filter predefined or fetched questions to match the search keywords."""

    keywords = extract_keywords(search_query)
    return {q for q in questions if any(kw.lower() in q.lower() for kw in keywords)}


def make_pg_user():
    current_dir = os.getcwd()
    result = subprocess.run(
        ["make", "pg_user"],
        cwd=current_dir,
        capture_output=True,
        text=True,
    )
    if result.returncode != 0:
        # print({"error": result.stderr})
        pass


def create_model_provider_table_if_not_exists():
    """
    Creates the model_providers table if it does not already exist.
    """
    create_table_query = """
    CREATE TABLE IF NOT EXISTS model_providers (
        id SERIAL PRIMARY KEY,
        provider TEXT NOT NULL,
        model_name TEXT NOT NULL,
        display_name TEXT NOT NULL,
        created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE (provider, model_name)
    );
    """
    with superset_connection.cursor() as cursor:
        cursor.execute(create_table_query)
    superset_connection.commit()


MODEL_PROVIDERS = json.loads(os.getenv("MODEL_PROVIDERS"))


def seed_model_providers_table():
    create_model_provider_table_if_not_exists()

    with superset_connection.cursor() as cur:
        for provider, models in MODEL_PROVIDERS.items():
            for model in models:
                cur.execute(
                    """
                    INSERT INTO model_providers (provider, model_name, display_name)
                    VALUES (%s, %s, %s)
                    ON CONFLICT (provider, model_name) DO NOTHING
                """,
                    (provider, model, model),
                )
    superset_connection.commit()


def create_qdrant_collection(collection_name):
    """
    Create the `document_summaries` collection in Qdrant if it doesn't exist.
    This collection stores vectorized summaries of documents.
    """
    try:
        if collection_name not in [
            col.name for col in qdrant_client.get_collections().collections
        ]:
            qdrant_client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=1024,
                    distance=Distance.COSINE,
                ),
            )
            print(f"Created collection `{collection_name}`.")
        else:
            print(f"Collection `{collection_name}` already exists.")
    except Exception as e:
        print(f"Error ensuring collection `{collection_name}` exists: {e}")


def get_last_two_conversations(dashboard_id: int):
    try:
        with superset_connection.cursor() as cursor:

            cursor.execute(
                """
                SELECT role, content FROM chat_history
                WHERE dashboard_id = %s
                ORDER BY datetime DESC
                LIMIT 20;
            """,
                (dashboard_id,),
            )

            rows = cursor.fetchall()
            rows.reverse()  # Ensure chronological order

            pairs = []
            current_pair = {}

            for role, content in rows:
                content = content.strip()

                if role == "user":
                    if "user" not in current_pair:
                        current_pair["user"] = content
                elif role == "bot":
                    if "bot" not in current_pair:
                        try:
                            parsed = json.loads(content)
                            response = parsed["data"][-1]["combiner_agent"][
                                "agent_outputs"
                            ]["combiner_agent"]["response"]
                            current_pair["bot"] = response
                        except Exception as e:
                            print(f"Skipping malformed bot content: {e}")
                            continue

                if "user" in current_pair and "bot" in current_pair:
                    pairs.append(current_pair)
                    current_pair = {}

                if len(pairs) == 2:
                    break
            return pairs

    except Exception as e:
        print(f"Error retrieving conversation: {e}")
        return []
    
def create_logo_table():
    """Create the logo table if it doesn't exist"""
    try:
        with superset_connection.cursor() as cursor:
            create_table_query = """
            CREATE TABLE IF NOT EXISTS logo (
                id SERIAL PRIMARY KEY,
                file_name TEXT NOT NULL,
                file_path TEXT NOT NULL,
                upload_type TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
           
            # Create trigger for updating timestamp
            create_trigger_function = """
            CREATE OR REPLACE FUNCTION update_logo_timestamp()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            """
           
            create_trigger = """
            CREATE OR REPLACE TRIGGER trigger_update_logo
            BEFORE UPDATE ON logo
            FOR EACH ROW
            EXECUTE FUNCTION update_logo_timestamp();
            """
           
            cursor.execute(create_table_query)
            cursor.execute(create_trigger_function)
            cursor.execute(create_trigger)
        superset_connection.commit()
        print("Logo table created successfully.")
    except Exception as e:
        print(f"Error creating logo table: {e}")
        superset_connection.rollback()

def create_superset_data_pipeline_table():
    """
    Creates the superset_data_pipeline table if it does not already exist.
    This table stores pipeline configuration and runtime metadata.
    """
    create_table_query = """
    CREATE TABLE IF NOT EXISTS public.superset_data_pipeline (
        pipeline_id SERIAL PRIMARY KEY,
        unit VARCHAR(50) NOT NULL DEFAULT 'seconds',
        pipeline_name VARCHAR(255) NOT NULL UNIQUE,
        generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status VARCHAR(50) DEFAULT 'inactive',
        health VARCHAR(50) DEFAULT 'unknown',
        api_curl TEXT NOT NULL,
        fetch_response JSONB,
        updated_schema JSONB,
        db VARCHAR(255),
        table_name VARCHAR(255) UNIQUE,
        frequency INTEGER DEFAULT 60,
        unselected_schema JSONB,
        api_key TEXT,
        headers JSONB
    );
    """
    try:
        with superset_connection.cursor() as cursor:
            cursor.execute(create_table_query)
        superset_connection.commit()
        print("superset_data_pipeline table created successfully")
    except Exception as e:
        print(f"Error creating superset_data_pipeline table: {e}")
        superset_connection.rollback()
       
       
        


def database_migrations():
    make_pg_user()
    create_metadata_table()
    # ensure_extension_exists()
    create_metadata_dashboard()
    create_chat_history_table()
    seed_model_providers_table()
    create_dashboard_files_table()
    create_dashboard_summaries_table()
    create_preference_table_if_not_exists()
    create_logo_table()
    create_superset_data_pipeline_table()

