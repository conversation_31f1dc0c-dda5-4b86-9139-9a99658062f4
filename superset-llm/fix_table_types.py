# #!/usr/bin/env python3
# """
# <PERSON>ript to fix column types in existing pipeline tables.
# This script will convert INTEGER columns to BIGINT for data columns
# to handle large values like timestamps and IDs from APIs.
# """

# import sys
# import os

# # Add the parent directory to the path so we can import our modules
# sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# from superset_pipeline.dbs_pipeline import DatabaseManager


# def fix_pipeline_table_types():
#     """Fix column types for all existing pipeline tables."""
#     try:
#         db_manager = DatabaseManager()
        
#         # Get all pipelines to find their table names
#         with db_manager.connection.cursor() as cursor:
#             cursor.execute("""
#                 SELECT DISTINCT table_name 
#                 FROM superset_data_pipeline 
#                 WHERE table_name IS NOT NULL AND table_name != ''
#             """)
            
#             table_names = [row[0] for row in cursor.fetchall()]
            
#         if not table_names:
#             print("No pipeline tables found to fix.")
#             return
            
#         print(f"Found {len(table_names)} pipeline tables to check: {table_names}")
        
#         for table_name in table_names:
#             print(f"\n--- Checking table: {table_name} ---")
            
#             # Check if table exists
#             with db_manager.connection.cursor() as cursor:
#                 cursor.execute("""
#                     SELECT EXISTS (
#                         SELECT FROM information_schema.tables
#                         WHERE table_name = %s AND table_schema = 'public'
#                     );
#                 """, (table_name,))
                
#                 table_exists = cursor.fetchone()[0]
                
#             if not table_exists:
#                 print(f"Table {table_name} does not exist, skipping...")
#                 continue
                
#             # Show current column types
#             with db_manager.connection.cursor() as cursor:
#                 cursor.execute("""
#                     SELECT column_name, data_type, is_nullable
#                     FROM information_schema.columns
#                     WHERE table_name = %s AND table_schema = 'public'
#                     ORDER BY ordinal_position;
#                 """, (table_name,))
                
#                 columns = cursor.fetchall()
#                 print(f"Current columns in {table_name}:")
#                 for col_name, data_type, is_nullable in columns:
#                     print(f"  {col_name}: {data_type} ({'NULL' if is_nullable == 'YES' else 'NOT NULL'})")
            
#             # Fix the column types
#             success = db_manager.fix_existing_table_column_types(table_name)
#             if success:
#                 print(f"✓ Successfully fixed column types for table {table_name}")
#             else:
#                 print(f"✗ Failed to fix column types for table {table_name}")
                
#         print(f"\n--- Completed fixing {len(table_names)} tables ---")
        
#     except Exception as e:
#         print(f"Error fixing table types: {e}")
#         import traceback
#         traceback.print_exc()


# if __name__ == "__main__":
#     print("=== Pipeline Table Column Type Fixer ===")
#     print("This script will fix INTEGER columns to BIGINT for large API values.")
#     print()
    
#     # Ask for confirmation
#     response = input("Do you want to proceed with fixing table column types? (y/N): ")
#     if response.lower() in ['y', 'yes']:
#         fix_pipeline_table_types()
#     else:
#         print("Operation cancelled.")
