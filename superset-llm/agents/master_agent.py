import json
import logging
from datetime import datetime
from .llm_selector import LLMSelector
from langchain.schema import HumanMessage
from langchain_core.runnables import Runnable
from utils.collect_dashboard_data import (
    fetch_dashboard_summary,
    fetch_description_of_file,
    get_chart_names_by_dashboard_id,
    get_last_two_conversations,
)

logging.basicConfig(level=logging.INFO, format="%(levelname)s - %(message)s")


class MasterAgent(Runnable):
    """Master Agent that classifies user queries and selects relevant sub-agents."""

    def __init__(self, db):
        self.db = db

    def invoke(self, state, x):
        """Classifies user queries and determines which agents to invoke."""

        model_providers = state.get("model_providers", "")
        model_name = state.get("model_name", "")
        dashboard_id = state.get("dashboard_id", "")
        csv_file_paths = state.get("csv_file_paths", [])
        pdf_file_paths = state.get("pdf_file_path", [])
        user_message_id = state.get("user_message_id", 0)
        llm = LLMSelector()
        self.llm = llm._initialize_llm(
            model_name=model_name, model_provider=model_providers
        )

        query = state.get("query", "")
        start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        logging.info(f"[START] MasterAgent processing query at {start_time}")

        if self.classify_greeting(query):
            return {
                "query": query,
                "agent_queries": {"greet_agent": query},
                "selected_agents": ["greet_agent"],
            }

        classification_prompt = PromptCreation.get_agent_classification_prompt(
            query,
            dashboard_id,
            user_message_id,
            csv_file_paths,
            pdf_file_paths,
            model_name,
        )
        selected_agents = {}

        try:
            response = self.llm.invoke([HumanMessage(content=classification_prompt)])
            logging.info(response.content)
            agent_mapping = json.loads(response.content.strip())

            if not agent_mapping:
                selected_agents = {"rag_agent": query}
            else:
                selected_agents.update(agent_mapping)

            logging.info(f"Selected Agents: {selected_agents}")
            end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            logging.info(f"[END] MasterAgent completed at {end_time}")

            return {
                "query": query,
                "agent_queries": selected_agents,
                "selected_agents": list(selected_agents.keys()),
            }
        except Exception as e:
            logging.error(f"Error in MasterAgent: {str(e)}")
            return {
                "query": query,
                "agent_queries": {"rag_agent": query},
                "selected_agents": ["rag_agent"],
            }

    def classify_greeting(self, user_query):
        """Classify query as a greeting if it exactly matches a greeting word/phrase."""
        greeting_keywords = {
            "hello",
            "hi",
            "hey",
            "greetings",
            "good morning",
            "good evening",
        }

        normalized_query = user_query.strip().lower()

        if normalized_query in greeting_keywords:
            return "greet_agent"

        return None


class PromptCreation:
    """Generates structured prompts for query classification and sub-agent selection."""

    @staticmethod
    def get_agent_classification_prompt(
        query: str,
        dashboard_id,
        user_message_id,
        csv_file_paths,
        pdf_file_paths,
        model_name,
    ) -> str:

        logging.info("---" * 30)
        logging.info(
            f"csv_file_paths: {csv_file_paths}, fetch_description_of_file(csv_file_paths): {fetch_description_of_file(csv_file_paths)}"
        )
        logging.info(
            f"pdf_file_paths: {pdf_file_paths}, fetch_description_of_file(pdf_file_paths): {fetch_description_of_file(pdf_file_paths)}"
        )
        logging.info(
            f"{get_last_two_conversations(user_message_id, dashboard_id, model_name)}"
        )
        logging.info("---" * 30)
        return f"""
        You are responsible for analyzing and decomposing the user query into smaller, well-defined sub-queries,
        each assigned to the most relevant agent. **DO NOT infer agent capabilities beyond the provided information.**
        Do not wrap the response inside ```json... ```.

        **Available Agents and Their Data Sources:**
        - **database_agent** (SQL queries): {{}}
        - **math_agent** (Arithmetic calculations ONLY)
        - **rag_agent** (Dashboard insights): {fetch_dashboard_summary(dashboard_id)}
        
        [NOTE]
        Previous Context of user: {get_last_two_conversations(user_message_id, dashboard_id, model_name)}
        Dashboard includes charts: {get_chart_names_by_dashboard_id(dashboard_id)}
        If user query has metion of any charts and something that is more related to chart name present above than select rag_agent.


        **Task:**
        - Break down complex user queries into multiple, precise sub-queries. Be sure to consider the recent chat history for context and continuity.
        - If the current query references something mentioned earlier (e.g., "that product" or "those sales"), replace such references with the exact terms or phrases used previously.
        - Assign each sub-query to the most relevant agent **strictly based on the data sources above**.
        - If a query is directly related to dashboards or name of charts has been included in query assign **ONLY** to the **rag_agent**.
        - If the query relates to a file that is part of the dashboard (as described under the rag_agent), then assign it to the rag_agent — even if the word "document" is mentioned, pass complete query to rag_agent.
        - If a query involves data that is not mention in dashboard, assign **ONLY** to the **database_agent**.
        - If uncertain, default to {{"rag_agent": query}}.
        
        **Rules:**
        - **[IMPORTANT]** **Return a JSON object with agent names as keys and sub-queries as values.**
        - **[IMPORTANT]** **DO NOT add explanations, comments, or code snippets. Only JSON output.**
        - **[IMPORTANT] [NEVER]** **Do not wrap the response inside ```json... ```.**
        - **Ensure each agent receives only the sub-query relevant to its provided data.**
        - [IMPORTANT] Don't add **Generated JSON:** kind of pharse in suffix as well as in prefix
        
        **Example Outputs:**
        **Query:** "What is FRT?"
        **Generated JSON:**
        {{
            "rag_agent": "What is FRT?"
        }}
        
        **Query:** "From CSV, how many datapoints are there? And from the dashboard, what is FRT?"
        **Generated JSON:**
        {{
            "csv_agent": "From CSV, How many datapoints are there?",
            "rag_agent": "From the dashboard, what is FRT?"
        }}
        
        **Query:** "From PDF, What is Somatosensory"
        **Generated JSON:**
        {{
            "pdf_agent": "What is Somatosensory?"
        }}
        
        **User Query:** "{query}"
        """
