import re
import json
import logging
import pandas as pd
from typing import List, Optional, Union
from datetime import datetime
from .llm_selector import LLMSelector
from langchain_core.runnables import Runnable


class NewCSVAgent(Runnable):
    def __init__(self):
        """Initialize the CSV Agent with a Pandas DataFrame."""

        self.agent_name = "csv_agent"
        self.df = None
        self.queries = {}
        self.responses = {}
        self.verbose = {}
        self.steps = []
        # self.load_csv()

    def load_csv(self, file_path: str):
        """Loads the CSV file into a Pandas DataFrame."""

        try:
            self.df = pd.read_csv(file_path)

        except Exception as e:
            raise ValueError("Failed to load CSV file.")

    def validate_query(self, query: str) -> str:
        """Validates the generated Pandas query against the DataFrame columns and corrects errors."""

        df_columns = set(self.df.columns)

        column_pattern = re.findall(r"df\[['\"](.*?)['\"]\]", query)

        for col in column_pattern:
            if col not in df_columns:
                query = re.sub(
                    rf"&?\s?\(df\[['\"]{col}['\"]\]\s?[=!<>]=\s?['\"].*?['\"]\)",
                    "",
                    query,
                )
        return query

    def generate_query(self, user_query: str) -> Union[str, List[str]]:
        """Uses LLM to generate a Pandas query and extracts only the valid query."""

        prompt = QueryPromptGenerator.get_prompt(user_query, self.df)

        try:
            response = self.llm.invoke(prompt)

            response = response.content
            if isinstance(response, dict):
                queries = [response.get("query", "")]
            elif isinstance(response, list):
                queries = [q.get("query", "") for q in response if isinstance(q, dict)]
            else:
                queries = [response]

            queries = [self.validate_query(q) for q in queries]

            if not queries:
                raise ValueError("No valid Pandas queries generated.")

            return queries if len(queries) > 1 else queries[0]
        except Exception as e:
            logging.error(f"Failed to generate query: {e}")
            return "Error generating query."

    def execute_query(self, queries: List[str]) -> Optional[List[pd.DataFrame]]:
        """Executes a list of Pandas queries and returns their results as DataFrames."""

        results = []
        queries = json.loads(queries)
        for query in queries:
            try:
                result = eval(query["query"], {"df": self.df})
                results.append(result)
            except Exception as e:
                logging.error(f"Error executing query: {query}, Error: {e}")
                results.append(None)

        return results if any(results) else None

    def generate_final_response(
        self, user_query: str, query_result: Optional[pd.DataFrame]
    ) -> str:
        """Uses LLM to synthesize a final human-readable response from the query result."""

        if query_result is None:
            return "No relevant data found for the query."

        prompt = f"""
        User Query: "{user_query}"
        Extracted Data:
        {query_result}
        
        Generate a concise and natural language answer summarizing the data.
        """
        try:
            response = self.llm.invoke(prompt)
            return response.content
        except Exception as e:
            logging.error(f"Error generating final response: {e}")
            return "Error synthesizing response."

    def process_query(self, user_query: str) -> str:
        """Full pipeline: Generate query, execute it, and return final response."""

        generated_query = self.generate_query(user_query)
        self.steps.append(
            f"""### Generated Pandas Query

        ```python
        {generated_query if generated_query else "# No query generated"}
        ```"""
        )

        query_result = self.execute_query(generated_query)
        self.steps.append(
            f"""### Query Execution Result

        ```json
        {json.dumps(query_result, indent=2) if isinstance(query_result, (dict, list)) else str(query_result)}
        ```
        """
        )
        final_response = self.generate_final_response(user_query, query_result)
        return final_response

    def invoke(self, state, x) -> str:
        """Implements invoke method for LangChain Runnable."""

        start_time = datetime.now()
        formatted_start_time = start_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        self.verbose["start_at"] = formatted_start_time
        logging.info(f"[START] CSV Agent processing query at {formatted_start_time}")

        try:
            model_providers = state.get("model_providers", "")
            model_name = state.get("model_name", "")
            csv_file_paths = state.get("csv_file_paths", [])

            self.load_csv(csv_file_paths[0])

            self.llm = LLMSelector()._initialize_llm(
                model_name=model_name, model_provider=model_providers
            )

            agent_queries = state.get("agent_queries", "")
            user_query = agent_queries.get(f"{self.agent_name}", state.get("query"))
            final_response = self.process_query(user_query)

            state.setdefault("agent_outputs", {})[self.agent_name] = final_response
            self.steps.append(
                f"""### Final Agent Response

                {final_response}
                """
            )
        except Exception as e:
            logging.error(f"Invoke error: {e}")
            final_response = "Error processing query."

        end_time = datetime.now()
        formatted_end_time = end_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        self.verbose["end_at"] = formatted_end_time
        self.verbose["total_time"] = (end_time - start_time).total_seconds()
        self.verbose["steps"] = self.steps
        state.setdefault("verbose", {})[self.agent_name] = self.verbose
        logging.info(f"[END] CSV Agent completed at {formatted_end_time}")


class QueryPromptGenerator:
    """Generates Pandas query prompts with few-shot examples."""

    @staticmethod
    def get_prompt(user_query: str, df: pd.DataFrame) -> str:
        examples = [
            {
                "user_query": "How many Sedan vehicles are there?",
                "queries": [
                    {
                        "query": "df[df['category'] == 'Sedan'].shape[0]",
                        "description": "Count the number of rows where 'Vehicle Type' is 'Sedan'.",
                    }
                ],
            },
            {
                "user_query": "Show me top 5 most expensive products?",
                "queries": [
                    {
                        "query": "df.sort_values(by='Price', ascending=False).head(5)",
                        "description": "Sort by 'Price' in descending order and return top 5 rows.",
                    }
                ],
            },
            {
                "user_query": "How many car with petrol fuel type with drive type Rear are there ?",
                "queries": [
                    {
                        "query": "df[(df['fuel_type'] == 'Petrol') & (df['drive_type'] == 'Rear')].shape[0]",
                        "description": "Sort by 'Price' in descending order and return top 5 rows.",
                    }
                ],
            },
        ]

        example_text = "\n\n".join(
            [
                f"User Query: {ex['user_query']}\nGenerated Queries: {json.dumps(ex['queries'], indent=4)}"
                for ex in examples
            ]
        )

        column_info = {
            col: (
                df[col].unique().tolist()[:5]
                if df[col].nunique() <= 10
                else df[col].sample(5, random_state=1).tolist()
            )
            for col in df.columns
        }
        column_info_str = json.dumps(column_info, indent=4)

        prompt = f"""
        User Query: {user_query}
        Available Columns and Sample Values:
        {column_info_str}
        [IMPORTANT] Always create Pandas queries that extract the most relevant information for the user query.
        [IMPORTANT] Provide only valid Python Pandas query output, no explanations.
        [IMPORTANT] Ensure column names are used exactly as provided in the available columns list.
        [IMPORTANT] Do not wrap the response inside ```python ... ```.
        Generate EXACTLY 1 optimized Pandas query with a description.
        
        Here are some few-shot examples:
        {example_text}
        """
        return prompt
