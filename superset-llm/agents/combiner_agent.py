from .llm_selector import LLMSelector
from langchain.schema import HumanMessage
from langchain_core.runnables import Runnable
from langchain_core.prompts import ChatPromptTemplate
import logging
from datetime import datetime
from utils import fetch_dashboard_summary
from utils.collect_dashboard_data import get_last_two_conversations


class CombinerAgent(LLMSelector, Runnable):
    """Combines responses from multiple agents using an LLM for a natural response."""

    def __init__(self):
        self.agent_name = "combiner_agent"

        self.prompt_template = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    "You are an AI assistant responsible for synthesizing information from multiple sources.",
                ),
                (
                    "human",
                    "Here are the responses from different agents:\n\n{responses}\n\nSummarize the information and provide a final response to the user based on his Query {user_query} in natural language.",
                ),
            ]
        )

    def invoke(self, state, x):
        """Processes agent responses, generates an LLM response, and streams output."""
        model_providers = state.get("model_providers", "")
        model_name = state.get("model_name", "")
        user_message_id = state.get("user_message_id", 0)
        llm = LLMSelector()
        self.llm = llm._initialize_llm(
            model_name=model_name, model_provider=model_providers
        )

        start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        logging.info(f"[START] CombinerAgent processing query at {start_time}")

        responses = state.get("agent_outputs", {})
        selected_agents = state.get("selected_agents", [])
        query = state.get("query")
        dashboard_id = state.get("dashboard_id")

        logging.info("^^^" * 20)
        logging.info(f"Responses: {responses}")
        logging.info("^^^" * 20)

        if len(responses) == 1 and "greet_agent" in responses:
            final_answer_text = responses["greet_agent"]
            state["agent_outputs"][self.agent_name] = {"response": final_answer_text}
            return state

        missing_agents = [agent for agent in selected_agents if agent not in responses]

        consolidated_knowledge = "\n".join(
            [f"{resp} Given by {agent}" for agent, resp in responses.items()]
        )
        if missing_agents:
            consolidated_knowledge += f"\n(Some data sources are temporarily unavailable, but a response has been generated using available knowledge.)"

        if "database_agent" in selected_agents or "rag_agent" in selected_agents:
            dashboard_summary = (
                f"Dashboard Overview:\n{fetch_dashboard_summary(dashboard_id)}"
            )
        else:
            dashboard_summary = ""

        intro = f"""
            You are an expert system with access to multiple data sources, including structured databases, analytical insights, and contextual knowledge. 
            Your goal is to provide a **clear, concise, and authoritative response** without referencing specific data sources. 

            {dashboard_summary}
            PREVIOUS CHAT HISTORY:
            {get_last_two_conversations(user_message_id, dashboard_id, model_name)}

            **Guidelines for Response:**
            - **[IMPORTANT] Always prioritize dashboard definitions** (e.g., "FRT = Forward Repair Team" as per the Dashboard Overview, not what an agent provides).
            - **[IMPORTANT] Never reference data sources explicitly** (e.g., avoid phrases like "According to the database agent" or "The RAG agent states").
            - **[IMPORTANT] Combine all relevant information into a single, concise, and well-structured response, as if delivered by a unified, intelligent assistant.**
            - **[IMPORTANT] Try to accumalate response of every agent and create one answer make relation between them.**
            - **[IMPORTANT] Do not Ignore any agent response.**
            - **[IMPORTANT] Use the agent-generated responses and previous chat history to craft a clear, direct, and context-aware answer.**
            - **[IMPORTANT] Only generate a response if relevant context is retrieved from other agents.**
            - **[IMPORTANT] Do not respond using personal knowledge or out-of-context information.**
            - **[IMPORTANT] Please answer in under 200 words.**

            **Answer:**  
            {consolidated_knowledge}
        """

        prompt = self.prompt_template.format(responses=intro, user_query=query)
        final_answer = self.llm.invoke([HumanMessage(content=prompt)])
        final_answer_text = (
            final_answer.content
            if hasattr(final_answer, "content")
            else str(final_answer)
        )


        state["agent_outputs"][self.agent_name] = {"response": final_answer_text}

        end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        logging.info(f"[END] CombinerAgent completed at {end_time}")

        return state
