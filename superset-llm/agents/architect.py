from typing import List
from langgraph.graph import StateGraph
from utils.common import superset_connection
from psycopg2.extras import RealDictCursor
import psycopg2
import logging

# Your existing agent imports
from .master_agent import MasterAgent
from .math_agent import MathAgent
from .rag_agent import RAGAgent
from .new_db_agent import NewDatabaseAgent
from .new_csv_agent import NewCSVAgent
from .new_pdf_agent import NewPDFAgent
from .combiner_agent import CombinerAgent
from .dashboard_agent import DashboardAgent
from .dashboard_file_agent import DashboardFileAgent
from typing import TypedDict, Annotated
import operator
from config import Config, Database


def merge_dicts(dict1, dict2):
    return {**dict1, **dict2}


class AgentState(TypedDict):
    query: Annotated[str, operator.add]
    selected_agents: Annotated[list[str], operator.add]
    agent_queries: Annotated[dict[str, str], merge_dicts]
    agent_outputs: Annotated[dict[str, str], merge_dicts]
    dashboard_id: int
    model_name: str
    model_providers: str
    user_message_id: int
    verbose: dict[str, dict]
    csv_file_paths: Annotated[list[str], operator.add]
    pdf_file_path: Annotated[list[str], operator.add]
    rag_selected_agents: Annotated[list[str], operator.add]
    rag_agent_queries: Annotated[dict[str, str], merge_dicts]


class AgentOrchestrator:
    """Agent graph orchestrator for handling query execution with dynamic agents."""

    def __init__(
        self,
        dashboard_id: int,
        model_name: str,
        model_providers: str,
        file_paths: List[str],
    ):
        # Prepare environment
        self.csv_file_paths = [f for f in file_paths if f.lower().endswith(".csv")]
        self.pdf_file_paths = [f for f in file_paths if f.lower().endswith(".pdf")]
        self.config = Config()
        db_name = self._get_db_name_from_dashboard_id(dashboard_id)
        self.db_url = self.config.default_connection_string(db_name)

        # Initialize DB
        self.db = Database(self.db_url).db

        # Initialize all agents
        self.master_agent = MasterAgent(self.db)
        self.database_agent = NewDatabaseAgent(self.db)
        self.math_agent = MathAgent()
        self.rag_agent = RAGAgent(
            db_url=self.config.VECTOR_DB_URL, ngrok_url=self.config.NGROK_URL
        )
        self.csv_agent = NewCSVAgent()
        self.pdf_agent = NewPDFAgent()
        self.combiner_agent = CombinerAgent()
        self.dashboard_agent = DashboardAgent(
            db_url=self.config.VECTOR_DB_URL, ngrok_url=self.config.NGROK_URL
        )
        self.dashboard_file_agent = DashboardFileAgent()
        # Compile graph
        # self.graph = self._build_graph()

        # Store model settings
        self.model_name = model_name
        self.model_providers = model_providers
        self.dashboard_id = dashboard_id

    def _get_db_name_from_dashboard_id(self, dashboard_id):
        get_slice_id_query = """
            SELECT slice_id
            FROM dashboard_slices
            WHERE dashboard_id = ANY(%s);
        """
        get_datasource_id_query = """
            SELECT datasource_id
            FROM slices
            WHERE id = ANY(%s);
        """
        get_database_id_query = """
            SELECT database_id
            FROM tables
            WHERE id = ANY(%s);
        """
        get_database_uri_query = """
            SELECT sqlalchemy_uri
            FROM dbs
            WHERE id = ANY(%s);
        """

        try:
            with superset_connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(get_slice_id_query, ([dashboard_id],))
                slice_result = cursor.fetchone()
                if not slice_result:
                    print("No slice found for given dashboard_id")
                    return None

                cursor.execute(get_datasource_id_query, ([slice_result["slice_id"]],))
                datasource_result = cursor.fetchone()
                if not datasource_result:
                    raise ValueError("No datasource found for given slice_id")

                cursor.execute(
                    get_database_id_query, ([datasource_result["datasource_id"]],)
                )
                dbid_result = cursor.fetchone()
                if not dbid_result:
                    raise ValueError("No database ID found for given datasource_id")

                cursor.execute(get_database_uri_query, ([dbid_result["database_id"]],))
                uri_result = cursor.fetchone()
                if not uri_result:
                    raise ValueError("No database URI found for given database_id")

                uri = uri_result["sqlalchemy_uri"]
                db_name = uri.split("/")[-1]

                return db_name

        except (psycopg2.Error, ValueError) as e:
            superset_connection.rollback()
            print(f"An error occurred while querying the database: {e}")
            return None

    def _build_graph(self):
        graph = StateGraph(AgentState)

        # Add nodes
        graph.add_node("master_agent", self.master_agent)
        graph.add_node("database_agent", self.database_agent)
        graph.add_node("math_agent", self.math_agent)
        graph.add_node("rag_agent", self.rag_agent)
        graph.add_node("csv_agent", self.csv_agent)
        graph.add_node("pdf_agent", self.pdf_agent)
        graph.add_node("dashboard_agent", self.dashboard_agent)
        graph.add_node("dashboard_file_agent", self.dashboard_file_agent)
        graph.add_node("combiner_agent", self.combiner_agent)

        # Conditional routing from master_agent
        graph.add_conditional_edges(
            "master_agent",
            self._route_to_agents,
            {
                "database_agent": "database_agent",
                "math_agent": "math_agent",
                "rag_agent": "rag_agent",
                "csv_agent": "csv_agent",
                "pdf_agent": "pdf_agent",
            },
        )

        # Conditional routing from rag_agent
        graph.add_conditional_edges(
            "rag_agent",
            self._route_from_rag_agent,
            {
                "dashboard_agent": "dashboard_agent",
                "dashboard_file_agent": "dashboard_file_agent",
            },
        )

        # Route all terminal agents to combiner
        for name in [
            "database_agent",
            "math_agent",
            "csv_agent",
            "pdf_agent",
            "dashboard_agent",
            "dashboard_file_agent",
        ]:
            graph.add_edge(name, "combiner_agent")

        graph.set_entry_point("master_agent")
        graph.set_finish_point("combiner_agent")

        return graph.compile()

    def _route_to_agents(self, state: AgentState) -> List[str]:
        """Returns agent list from state — logic handled by MasterAgent."""
        return state["selected_agents"]

    def _route_from_rag_agent(self, state: AgentState):
        """
        Determines which agent(s) the rag_agent should route to,
        based on the selected_sources returned in its output state.
        """

        selected = state.get("rag_selected_agents", [])
        logging.info("^^^" * 20)
        logging.info(selected)
        logging.info("^^^" * 20)

        if not selected:
            return ["dashboard_agent"]

        return selected
