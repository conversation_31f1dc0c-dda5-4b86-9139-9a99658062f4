import os
from langchain_openai import ChatOpenAI
from langchain_groq import ChatGroq
from langchain_ollama import ChatOllama
from config import Config
from utils.common import superset_connection


class LLMSelector:
    """Parent class to handle LLM selection dynamically based on user input."""

    def __init__(self):
        self.config = Config()

    def _fetch_preferences(self, model_provider, model_name):
        """Fetches model preferences from the database based on model_provider and model_name."""
        select_query = """
            SELECT temperature, top_p, top_k, repetition_penalty
            FROM preferences WHERE model_provider = %s AND model_name = %s
        """
        with superset_connection.cursor() as cursor:
            try:
                cursor.execute(select_query, (model_provider, model_name))
                record = cursor.fetchone()
                if record:
                    return record[0], record[1], record[2], record[3]
                else:
                    return 0.1, 1.0, 50, 1.0  # Default values if preferences are not found
            except:
                superset_connection.rollback()
                return 0.1, 1.0, 50, 1.0

    def _initialize_llm(self, model_provider, model_name):
        """Dynamically selects the LLM provider based on user input."""

        self.temperature, self.top_p, self.top_k, self.repetition_penalty = (
            self._fetch_preferences(model_provider, model_name)
        )

        model_kwargs = {
            "temperature": self.temperature,
            "top_p": self.top_p,
            "top_k": self.top_k,
            "repetition_penalty": self.repetition_penalty,
        }

        if model_provider == "openai":
            return ChatOpenAI(
                model=model_name,
                api_key=os.environ.get("OPENAI_API_KEY"),
                temperature=self.temperature,
                top_p=self.top_p,
                # model_kwargs=model_kwargs,
            )
        elif model_provider == "groq":
            return ChatGroq(
                model=model_name,
                api_key=os.environ.get("GROQ_API_KEY"),
                temperature=self.temperature,
            )
        elif model_provider == "ollama":
            return ChatOllama(
                model=model_name,
                temperature=self.temperature,
                top_k=self.top_k,
                top_p=self.top_p,
                repeat_penalty=self.repetition_penalty,
                base_url=self.config.NGROK_URL,
            )
        else:
            raise ValueError(f"Unsupported model provider: {model_provider}")
