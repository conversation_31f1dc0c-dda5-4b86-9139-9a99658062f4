import os
import json
import logging
from datetime import datetime
from .llm_selector import LLMSelector
from langchain.schema import HumanMessage
from langchain_core.runnables import Runnable
from utils.collect_dashboard_data import (
    fetch_dashboard_summary,
    get_chart_names_by_dashboard_id,
)

logging.basicConfig(level=logging.INFO, format="%(levelname)s - %(message)s")


class RAGAgent(Runnable):
    """RAG Agent that processes queries related to dashboards and dashboard-attached documents."""

    def __init__(self, db_url, ngrok_url):
        self.db_url = db_url
        self.ngrok_url = ngrok_url
        self.agent_name = "rag_agent"

    def invoke(self, state, x):
        model_providers = state.get("model_providers", "")
        model_name = state.get("model_name", "")
        dashboard_id = state.get("dashboard_id", "")

        llm = LLMSelector()
        self.llm = llm._initialize_llm(
            model_name=model_name, model_provider=model_providers
        )

        query = state.get("query", "")
        start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        logging.info(f"[START] RAGAgent processing query at {start_time}")

        classification_prompt = self.get_rag_classification_prompt(query, dashboard_id)
        selected_sources = {}

        try:
            response = self.llm.invoke([HumanMessage(content=classification_prompt)])
            logging.info("----" * 20)
            logging.info(response.content)
            logging.info("----" * 20)

            selected_sources = {}

            # Parse response safely
            try:
                source_mapping = json.loads(response.content.strip())
            except json.JSONDecodeError as decode_err:
                logging.error(f"Failed to parse JSON: {decode_err}")
                source_mapping = {"dashboard_agent": query, "dashboard_file_agent": {}}

            # Handle dashboard_agent
            if (
                "dashboard_agent" in source_mapping
                and source_mapping["dashboard_agent"].strip()
            ):
                selected_sources["dashboard_agent"] = source_mapping[
                    "dashboard_agent"
                ].strip()

            # Handle dashboard_file_agent
            if "dashboard_file_agent" in source_mapping:
                file_queries = source_mapping["dashboard_file_agent"]
                if isinstance(file_queries, dict):
                    cleaned_file_queries = {
                        fp.strip(): sq.strip()
                        for fp, sq in file_queries.items()
                        if sq.strip()
                    }
                    if cleaned_file_queries:
                        selected_sources["dashboard_file_agent"] = cleaned_file_queries

            # Fallback if nothing selected
            if not selected_sources:
                selected_sources = {
                    "dashboard_agent": query,
                    "dashboard_file_agent": {},
                }

            logging.info("----" * 20)
            logging.info(f"Selected Sources: {selected_sources}")
            logging.info("----" * 20)

            logging.info(f"Selected Data Sources: {selected_sources}")
            end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            logging.info(f"[END] RAGAgent completed at {end_time}")

            return {
                "query": query,
                "rag_agent_queries": selected_sources,
                "rag_selected_agents": list(selected_sources.keys()),
            }

        except Exception as e:
            logging.error(f"Error in RAGAgent: {str(e)}")
            return {
                "query": query,
                "rag_agent_queries": {"dashboard_agent": query},
                "rag_selected_agents": ["dashboard_agent"],
            }

    @staticmethod
    def get_rag_classification_prompt(
        query: str,
        dashboard_id,
    ) -> str:
        dashboard_summary = fetch_dashboard_summary(dashboard_id)
        chart_names = get_chart_names_by_dashboard_id(dashboard_id)

        # Extract file description section
        has_files = "FILE SUMMARIES:" in dashboard_summary
        file_description_section = ""
        if has_files:
            file_description_section = dashboard_summary.split("FILE SUMMARIES:")[
                -1
            ].strip()

            # 2. **Dashboard Summary (Charts):** {dashboard_summary.split("FILE SUMMARIES:")[0].strip()}
        return f"""
            You are a smart agent that classifies user queries into sub-queries based on their relevance to chart-based data or uploaded files.

            **Goal:** 
            Break the user query into specific sub-queries and assign each to either:
            - `dashboard_agent` for chart-related insights.
            - One or more file paths under `dashboard_file_agent` if the content is relevant to document/file descriptions.
            - If unclear, assign to both agents with the original query.

            **Context:**
            1. **Chart Names:** {chart_names}
            {f'3. **Dashboard Files:**{file_description_section}' if has_files else ''}

            **Instructions:**
            - **[IMPORTANT] Do not wrap the response inside ```json... ```. **
            - Strictly follow the JSON format. No explanations or comments. Only JSON output.
            - If query refers to a document based on its title or description, map it to that file’s `file_path` mention in the context [ALWAYS].
            - If multiple documents are referenced, split accordingly and assign to respective paths.
            - Chart/KPI/metric/chart name → assign to `"dashboard_agent"`.
            - Do not add any other field apart from "dashboard_agent" and "dashboard_file_agent" in JSON output.

            **Return Format (JSON only):**
            {{
            "dashboard_agent": "subquery1",
            "dashboard_file_agent": {{
                "/path/to/file1": "related subquery for file1",
                "/path/to/file2": "related subquery for file2"
            }}
            }}
            Examples:
            Query: "Explain trends in the sales chart."
            {{
            "dashboard_agent": "Explain trends in the sales chart."
            }}
            User Query: "{query}"
        """
