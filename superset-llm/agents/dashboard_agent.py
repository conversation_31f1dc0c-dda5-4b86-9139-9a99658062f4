import re
import os
import json
import logging
import psycopg2
from utils import (
    slice_data_fetch,
    get_dashboard_embeddings_len,
    fetch_dashboard_summary,
)
from datetime import datetime
from .llm_selector import LLMSelector
from qdrant_client import QdrantClient
from langchain_ollama import OllamaEmbeddings
from langchain_core.runnables import Runnable
from langchain_qdrant import QdrantVectorStore
from qdrant_client.models import Filter, FieldCondition, MatchValue


QDRANT_HOST = os.environ.get("QDRANT_HOST", "http://qdrant:6333")
qdrant_client = QdrantClient(QDRANT_HOST)


is_docker = os.getenv("IS_DOCKER", "false").lower() == "true"
host = "db" if is_docker else "localhost"

superset_db = {
    "dbname": "superset",
    "user": "superset",
    "password": "superset",
    "host": host,
    "port": 5432,
}

superset_connection = psycopg2.connect(**superset_db)


class DashboardAgent(Runnable):
    """Agent for retrieving and processing dashboard-related queries."""

    def __init__(
        self,
        db_url,
        ngrok_url,
        superset_connection=superset_connection,
    ):
        self.db_url = db_url
        self.agent_name = "dashboard_agent"
        self.superset_connection = superset_connection
        self.embedding_model = OllamaEmbeddings(
            model="mxbai-embed-large", base_url=ngrok_url
        )
        self.verbose = {}
        self.steps = []

    def retrieve_relevant_docs(self, user_query, dashboard_id):
        """Fetches relevant documents from Qdrant."""

        filters_list = self.extract_metadata_filters(user_query, dashboard_id)
        logging.info(f"Filters List: {filters_list}")

        qdrant_filter = None

        if filters_list:
            # Normalize to list of dicts
            if isinstance(filters_list, dict):
                filters_list = [filters_list]

            # Remove None values
            cleaned_filters = []
            for condition in filters_list:
                sub_conditions = []
                for k, v in condition.items():
                    if v is not None:
                        sub_conditions.append(
                            FieldCondition(
                                key=f"metadata.{k}", match=MatchValue(value=v)
                            )
                        )
                if sub_conditions:
                    cleaned_filters.append(sub_conditions)

            # Apply "must" filter (AND logic between all key-value pairs in each dict)
            # and "should" filter (OR logic between filter dicts)
            if len(cleaned_filters) == 1:
                qdrant_filter = Filter(must=cleaned_filters[0])
            elif len(cleaned_filters) > 1:
                qdrant_filter = Filter(
                    should=[Filter(must=conds) for conds in cleaned_filters]
                )

        collection_name = f"dashboard_{dashboard_id}"
        vector_store = QdrantVectorStore(
            client=qdrant_client,
            collection_name=collection_name,
            embedding=self.embedding_model,
        )

        document_length = get_dashboard_embeddings_len(
            dashboard_id, self.superset_connection
        )
        k = 16

        self.steps.append(
            f"""### Applied Filters

            Filters have been created to filter the document based on metadata **if related**:

            {(qdrant_filter)}"""
        )
        retrieved_docs = vector_store.similarity_search_with_score(
            query=user_query,
            k=k,
            filter=qdrant_filter,
        )
        if not retrieved_docs:
            logging.info(
                "No relevant documents found with filter. Retrying with only dashboard_id."
            )
            retrieved_docs = vector_store.similarity_search_with_score(
                query=user_query,
                k=k,
                # score_threshold=0.8,
            )

        filtered_docs = [doc for doc, _ in retrieved_docs]

        self.steps.append(
            f"""### Document Retrieval Summary

        - **Retrieved Docs Count:** {len(retrieved_docs)}
        - **Filtered Docs Count:** {len(filtered_docs)}

        _Data retrieved from RAG and Vector Database._
        """
        )

        return (
            f"INTRODUCTION of Dashboad:\n{fetch_dashboard_summary(dashboard_id)}\n"
            + "\n".join([doc.page_content for doc in filtered_docs])
            if filtered_docs
            else "No relevant documents found."
        )

    def invoke(self, state, x):
        """Processes user queries and generates responses using retrieved context."""

        model_providers = state.get("model_providers", "")
        model_name = state.get("model_name", "")
        llm = LLMSelector()
        self.llm = llm._initialize_llm(
            model_name=model_name, model_provider=model_providers
        )

        start_time = datetime.now()
        formatted_start_time = start_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        self.verbose["start_at"] = f"{formatted_start_time}"
        logging.info(f"[START] RAG Agent processing query at {formatted_start_time}")

        dashboard_id = state.get("dashboard_id", "")
        model_name = state.get("model_name", "")

        agent_queries = state.get("agent_queries", "")
        user_query = agent_queries.get(f"{self.agent_name}", state.get("query"))
        if not all([dashboard_id, model_name, user_query]):
            return {"error": "dashboard_id, model_name, and user_query are required"}

        context = self.retrieve_relevant_docs(user_query, dashboard_id)

        prompt = f"""
            Let's break this down step by step:

            1. What information is relevant from the dashboard?
            2. How does this relate to the user’s query?
            3. Provide a concise yet informative answer.
            4. If No information is relevant, respond with "No relevant information found."

            Context: {context}
            User Query: {user_query}
            Answer:
        """

        final_response = self.llm.invoke(input=prompt)

        state.setdefault("agent_outputs", {})[self.agent_name] = final_response.content

        self.steps.append(
            f"""### Final Agent Response

        {final_response if isinstance(final_response, str) else final_response.content}
        """
        )
        self.verbose["steps"] = self.steps
        end_time = datetime.now()
        formatted_end_time = end_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        self.verbose["end_at"] = f"{formatted_end_time}"
        logging.info(f"[END] RAG Agent completed at {formatted_end_time}")

        self.verbose["total_time"] = (end_time - start_time).total_seconds()
        state.setdefault("verbose", {})[self.agent_name] = self.verbose

    def extract_metadata_filters(self, user_query, dashboard_id):
        """
        Uses an LLM to extract relevant metadata filters from the user query.
        Matches chart names to those available in the dashboard.
        Predicts `position` dynamically as an integer.
        If multiple positions are relevant (e.g., "top 3"), creates multiple separate queries.
        """

        available_charts = slice_data_fetch(dashboard_id)

        logging.info(f"--**" * 20)
        logging.info(f"Available Charts: {available_charts}")
        logging.info(f"--**" * 20)

        few_shot_examples = f"""
        Examples:

        Available charts: ["Weapon System Proficiency", "FRT_Capability", "Driver Performance", "Vehicle Readiness"]

        Query: "Show FRT capability where MTTR is greater than 10"
        Extracted Filters: [[{{"chart_type": "FRT_Capability"}}]]

        Query: "Which FRT has the highest Proficiency?"
        Extracted Filters: [[{{"chart_type": "Weapon System Proficiency", "position": 1}}]]

        Query: "Compare Driver Performance and Vehicle Readiness"
        Extracted Filters: [[{{"chart_type": "Driver Performance"}}, {{"chart_type": "Vehicle Readiness"}}]]

        Query: "Get the bottom-ranked FRT_Capability entry"
        Extracted Filters: [[{{"chart_type": "FRT_Capability", "position": 10}}]]

        Query: "Retrieve data for the 5th best Vehicle Readiness entry"
        Extracted Filters: [[{{"chart_type": "Vehicle Readiness", "position": 5}}]]

        Query: "What is the performance trend of the top 5 drivers?"
        Extracted Filters: [[
            {{"chart_type": "Driver Performance", "position": 1}},
            {{"chart_type": "Driver Performance", "position": 2}},
            {{"chart_type": "Driver Performance", "position": 3}},
            {{"chart_type": "Driver Performance", "position": 4}},
            {{"chart_type": "Driver Performance", "position": 5}}
        ]]
        """

        prompt = f"""
        Based on the following user query, extract relevant metadata filters in JSON format.

        Available charts in the dashboard: {available_charts}

        - Select `chart_type` only from the available charts.
        - If the user specifies a ranking (e.g., "top 3", "5th best"), extract `position` as an **integer**.
        - If multiple positions are needed (e.g., "top 3"), create separate entries.
        - If a user asks for 'top', 'best', or similar terms, retrieve the top 3 positions. If they ask for 'worst', 'bottom', or similar terms, retrieve the last 3 positions.

        {few_shot_examples}

        [IMPORTANT] Provide only Valid JSON output, no explanations.
        [IMPORTANT] Do not add any other field apart from "chart_type" and "position", "position" is also optional field, add whenever required ONLY.
        [IMPORTANT] Only include the "position" field when the query explicitly requires ranking or ordering; omit it entirely if not applicable.
        [IMPORTANT] Do not wrap the response inside `extracted_filters`.
        [IMPORTANT] Do not warp the response inside ```json... ```.

        Query: "{user_query}"
        Extracted Filters:
        """

        filter_response = self.llm.invoke(prompt).content

        try:
            filter_response = filter_response.strip()

            if not (filter_response.startswith("{") or filter_response.startswith("[")):
                logging.error(f"Invalid JSON format from LLM: {filter_response}")
                return None

            filters = json.loads(filter_response)

            if isinstance(filters, dict):
                if "extracted_filters" in filters:
                    filters = filters["extracted_filters"]

                if "chart_type" in filters:
                    return [filters]

            elif isinstance(filters, list):
                if len(filters) == 1 and isinstance(filters[0], list):
                    filters = filters[0]

                valid_filters = [
                    item
                    for item in filters
                    if isinstance(item, dict) and "chart_type" in item
                ]

                if not valid_filters:
                    logging.warning("No valid chart_type found in response.")
                    return None

                return valid_filters

            elif isinstance(filters, dict) and any(
                isinstance(val, list) for val in filters.values()
            ):
                for key, val in filters.items():
                    if isinstance(val, list) and all(
                        isinstance(item, dict) for item in val
                    ):
                        return val

            logging.warning(f"Unexpected filter format: {filters}")
            return None

        except json.JSONDecodeError as e:
            logging.error(
                f"Failed to parse LLM response as JSON: {e}, Response: {filter_response}"
            )
            return None
