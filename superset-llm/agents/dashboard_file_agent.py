import os
import logging
import numpy as np
from datetime import datetime
from qdrant_client import QdrantClient
from sklearn.metrics.pairwise import cosine_similarity
from langchain_qdrant import QdrantVectorStore
from utils.collect_dashboard_data import embedding_model
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable
from .llm_selector import LLMSelector
from qdrant_client.models import Filter, FieldCondition, MatchValue
from utils.common import superset_connection

logging.basicConfig(level=logging.INFO, format="%(levelname)s - %(message)s")

QDRANT_HOST = os.environ.get("QDRANT_HOST", "http://qdrant:6333")
qdrant_client = QdrantClient(QDRANT_HOST)


class DashboardFileAgent(Runnable):
    def __init__(self):
        self.agent_name = "dashboard_file_agent"
        self.retriever = None
        self.llm = None
        self.steps = []
        self.verbose = {}

    def get_rag_response(self, user_query: str) -> str:
        """Performs similarity search filtered by dashboard_id and generates a concise response."""

        responses = {}
        for file_path, query in user_query.items():
            try:
                filter_conditions = []
                if file_path:
                    filter_conditions.append(
                        FieldCondition(
                            key="metadata.file_path",
                            match=MatchValue(value=str(file_path)),
                        )
                    )

                filter = Filter(must=filter_conditions)

                docs = self.vectorstore.similarity_search(
                    query=query, k=12, filter=filter
                )

                logging.info("Docs fetched with file_path filter: %d", len(docs))
                logging.info("Final documents fetched: %d", len(docs))

                if not docs:
                    logging.info(
                        "No relevant documents found with filter. Retrying with only dashboard_id."
                    )

                    docs = self.vectorstore.similarity_search(
                        query=query, k=12, score_threshold=0.8
                    )

                context = "\n\n".join([doc.page_content for doc in docs])

                formatted_metadata = "\n\n".join(
                    [f"Metadata:\n{doc.metadata}" for doc in docs]
                )

                self.steps.append(
                    f"""### for file {file_path} Retrieved Context\n\n{context}\n\n"""
                )

                prompt_template = ChatPromptTemplate.from_messages(
                    [
                        (
                            "system",
                            "You are an intelligent assistant specialized in answering user queries using the provided context. "
                            "Use the retrieved content below to formulate your response. "
                            "Always aim to provide a clear, specific, and informative answer. "
                            "If the answer is not found in the context, say you don’t know. "
                            "Be thorough but avoid unnecessary elaboration. Focus on accuracy and relevance.\n\n{context}",
                        ),
                        ("human", "{input}"),
                    ]
                )

                response = self.llm.invoke(
                    prompt_template.format_messages(context=context, input=query)
                )
                self.steps.append(
                    f"""### for file {file_path} Generated Response\n\n{response.content}\n\n"""
                )
                responses[file_path] = (
                    response.content if hasattr(response, "content") else str(response)
                )
            except Exception as e:
                logging.error(f"Error in RAG response generation: {e}")
                raise
        return responses

    def invoke(self, state, x):
        """LangChain Runnable invoke method with query and metadata-aware vector filtering."""

        start_time = datetime.now()
        file_collection_name = f"document_summaries_{state.get('dashboard_id')}"
        self.vectorstore = QdrantVectorStore(
            client=qdrant_client,
            collection_name=file_collection_name,
            embedding=embedding_model,
        )

        self.verbose["start_at"] = start_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        logging.info(
            f"[START] DashboardFileAgent started at {self.verbose['start_at']}"
        )

        agent_queries = state.get("rag_agent_queries", {})
        user_query = agent_queries.get(self.agent_name, state.get("query"))
        dashboard_id = state.get("dashboard_id")
        logging.info("####" * 20)
        logging.info(f"User Query: {user_query}")
        logging.info("####" * 20)
        if not dashboard_id:
            return {"error": "Missing dashboard_id for filtering dashboard documents."}

        try:
            if not self.llm:
                model_provider = state.get("model_providers", "")
                model_name = state.get("model_name", "")
                self.llm = LLMSelector()._initialize_llm(
                    model_provider=model_provider, model_name=model_name
                )

            answer = self.get_rag_response(user_query)

            self.steps.append(f"### Final Agent Response\n\n{answer}")
            self.verbose["steps"] = self.steps

            end_time = datetime.now()
            self.verbose["end_at"] = end_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            self.verbose["total_time"] = (end_time - start_time).total_seconds()

            logging.info(
                f"[END] DashboardFileAgent completed at {self.verbose['end_at']}"
            )

            state.setdefault("verbose", {})[self.agent_name] = self.verbose
            state.setdefault("agent_outputs", {})[self.agent_name] = answer

            return {"response": answer}

        except Exception as e:
            logging.error(f"DashboardFileAgent error: {e}")
            return {"error": str(e)}


# try:
#     with superset_connection.cursor() as cursor:
#         cursor.execute(
#             """
#             SELECT file_path, description
#             FROM files
#             WHERE dashboard_id = %s
#             """,
#             (int(dashboard_id),),
#         )
#         file_records = cursor.fetchall()

#     if not file_records:
#         logging.warning("No file descriptions found for dashboard.")

#     file_paths, descriptions = zip(*file_records)

#     query_embedding = embedding_model.embed_query(user_query)
#     description_embeddings = embedding_model.embed_documents(
#         list(descriptions)
#     )

#     similarities = cosine_similarity(
#         [query_embedding],  # shape: (1, dim)
#         description_embeddings,  # shape: (n, dim)
#     )[0]

#     best_index = np.argmax(similarities)
#     file_path = file_paths[best_index]

#     logging.info(
#         f"Most relevant file_path by embedding similarity: {file_path}"
#     )

# except Exception as e:
#     logging.warning(
#         f"Error fetching file_path via embedding similarity: {e}"
#     )
#     superset_connection.rollback()
