import logging
from datetime import datetime
from langchain_core.runnables import Runnable

logging.basicConfig(level=logging.INFO, format="%(levelname)s - %(message)s")


class GreetAgent(Runnable):
    """Simple agent that returns greetings without invoking other agents."""

    def __init__(self):
        self.agent_name = "greet_agent"
        self.steps = []
        self.verbose = {}

    def invoke(self, state, x):
        """Simply returns the user query as a greeting response."""
        start_time = datetime.now()
        formatted_start_time = start_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        self.verbose["start_at"] = f"{formatted_start_time}"
        logging.info(f"[START] GreetAgent processing query at {formatted_start_time}")

        query = state.get("query", "")
        agent_queries = state.get("agent_queries", {})
        user_query = agent_queries.get(self.agent_name, query)

        greeting_response = f"Hello! How may I help you today?"

        self.steps.append(f"### Final Agent Response\n\n{greeting_response}")

        end_time = datetime.now()
        self.verbose["end_at"] = end_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        self.verbose["total_time"] = (end_time - start_time).total_seconds()
        self.verbose["steps"] = self.steps

        logging.info(f"[END] GreetAgent completed at {self.verbose['end_at']}")

        state.setdefault("verbose", {})[self.agent_name] = self.verbose
        state.setdefault("agent_outputs", {})[self.agent_name] = greeting_response

        return state
