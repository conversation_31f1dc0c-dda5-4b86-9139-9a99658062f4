import json
from flask import Blueprint, request, jsonify
from superset_pipeline.superset_data_fetching import DataFetcher
from superset_pipeline.dbs_pipeline import DatabaseManager
from superset_pipeline.task_manager import (
    start_pipeline,
    stop_pipeline,
    stop_all_pipelines,
    get_pipeline_status,
    SCHEDULER_TYPE,
    REDIS_AVAILABLE
)

# Create Blueprint
pipeline_bp = Blueprint("pipeline", __name__)


@pipeline_bp.route("/create_and_fetching", methods=["POST"])
def create_and_fetching():
    """
    Create a new pipeline and fetch initial data.
    
    Input:
        - pipeline_name: str
        - api_curl: str  
        - api_key: str (optional)
        - headers: dict (optional)
    
    Functionality:
        - Calls DataFetcher to execute API call
        - Parse and clean data with pandas
        - Auto-detect schema
        - Store cleaned data and metadata in DB
    """
    try:
        data = request.json
        print(f"Received request data: {data}")  # Debug log

        # Validate required fields
        pipeline_name = data.get("pipeline_name")
        api_curl = data.get("api_curl")

        if not pipeline_name or not api_curl:
            return jsonify({
                "success": False,
                "error": "pipeline_name and api_curl are required"
            }), 400

        # Optional fields
        api_key = data.get("api_key")
        headers = data.get("headers", {})

        print(f"Processing pipeline: {pipeline_name}, API: {api_curl}")  # Debug log

        # Initialize DataFetcher
        data_fetcher = DataFetcher()

        # Process the pipeline
        result = data_fetcher.process_api_pipeline(
            pipeline_name=pipeline_name,
            api_url=api_curl,
            api_key=api_key,
            headers=headers
        )

        print(f"Pipeline processing result: {result.get('success', False)}")  # Debug log

        if result["success"]:
            return jsonify({
                "success": True,
                "pipeline_id": result["pipeline_id"],
                "schema": result["schema"],
                "sample_data": result["sample_data"],
                "message": result["message"]
            }), 200
        else:
            return jsonify({
                "success": False,
                "error": result["error"]
            }), 400

    except Exception as e:
        import traceback
        print(f"Exception in create_and_fetching: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}"
        }), 500


@pipeline_bp.route("/schema_selection_and_table_creation", methods=["POST"])
def schema_selection_and_table_creation():
    """
    Handle schema selection and create target table.

    Input:
        - pipeline_id: int
        - selected_fields: list
        - db: str
        - table_name: str

    Functionality:
        - Save selected fields as updated_schema
        - Auto-capture remaining fields as unselected_schema
        - Create new table in selected database using only selected fields
    """
    try:
        data = request.json
        print(f"Schema selection request data: {data}")  # Debug log

        # Validate required fields
        pipeline_id = data.get("pipeline_id")
        selected_fields = data.get("selected_fields")
        db_name = data.get("db")
        table_name = data.get("table_name")
        is_edit = data.get("is_edit", False)
        
        print(f"Pipeline ID: {pipeline_id}, Selected fields: {selected_fields}")  # Debug log

        if not all([pipeline_id, selected_fields, db_name, table_name]):
            return jsonify({
                "success": False,
                "error": "pipeline_id, selected_fields, db, and table_name are required"
            }), 400

        if not isinstance(selected_fields, list) or len(selected_fields) == 0:
            return jsonify({
                "success": False,
                "error": "selected_fields must be a non-empty list"
            }), 400

        # Initialize DatabaseManager
        db_manager = DatabaseManager()
        
        if not is_edit:
            is_exist_table = db_manager.check_table_exists(table_name)
            if is_exist_table:
                return jsonify({
                    "success": False,
                    "error": f"Table {table_name} already exists"
                }), 400

        
        # First, check if pipeline exists and get its current schema
        pipeline = db_manager.get_pipeline_by_id(pipeline_id)
        if not pipeline:
            return jsonify({
                "success": False,
                "error": f"Pipeline {pipeline_id} not found"
            }), 404

        print(f"Current pipeline schema: {pipeline.get('updated_schema')}")  # Debug log

        # Update pipeline with selected schema
        schema_updated = db_manager.update_pipeline_schema(
            pipeline_id=pipeline_id,
            selected_fields=selected_fields,
            db_name=db_name,
            table_name=table_name
        )
        
        

        if not schema_updated:
            return jsonify({
                "success": False,
                "error": "Failed to update pipeline schema"
            }), 400

        # Get updated pipeline to create table
        updated_pipeline = db_manager.get_pipeline_by_id(pipeline_id)
        if not updated_pipeline:
            return jsonify({
                "success": False,
                "error": "Pipeline not found after schema update"
            }), 400

        print(f"Updated pipeline schema: {updated_pipeline.get('updated_schema')}")  # Debug log

        # Create or update target table (handles both new tables and adding columns)
        table_created = db_manager.create_or_update_target_table(
            db_name=db_name,
            table_name=table_name,
            schema=updated_pipeline["updated_schema"]
        )

        if table_created:
            unselected_fields = updated_pipeline.get("unselected_schema", {}).get("unselected_fields", [])

            # Check if table existed before to provide appropriate message
            existing_columns = db_manager.get_existing_table_columns(table_name)
            if existing_columns:
                message = f"Schema updated and table '{table_name}' modified with new columns"
            else:
                message = f"Schema updated and table '{table_name}' created successfully"

            return jsonify({
                "success": True,
                "message": message,
                "selected_fields": selected_fields,
                "unselected_fields": unselected_fields,
                "existing_columns": existing_columns,
                "total_columns_in_table": len(existing_columns) + len([f for f in selected_fields if f not in existing_columns])
            }), 200
        else:
            return jsonify({
                "success": False,
                "error": "Failed to create or update target table"
            }), 400

    except Exception as e:
        import traceback
        print(f"Exception in schema_selection_and_table_creation: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}"
        }), 500

@pipeline_bp.route("/start", methods=["POST"])
def start():
    """
    Start automatic fetching and storage process with flexible time units.
    """
    try:
        data = request.json
       
        pipeline_id = data.get("pipeline_id")
        frequency = data.get("frequency")
        unit = data.get("unit", "seconds").lower()
 
        # Validation
        if not pipeline_id:
            return jsonify({
                "success": False,
                "error": "pipeline_id is required"
            }), 400
       
        if not isinstance(frequency, int) or frequency < 1:
            return jsonify({
                "success": False,
                "error": "frequency must be a positive integer"
            }), 400
       
        frequency_seconds = frequency
 
        # Fetch and validate pipeline config
        db_manager = DatabaseManager()
        pipeline = db_manager.get_pipeline_by_id(pipeline_id)
       
        if not pipeline:
            return jsonify({
                "success": False,
                "error": f"Pipeline {pipeline_id} not found"
            }), 404
       
        if not pipeline.get("table_name") or not pipeline.get("updated_schema"):
            return jsonify({
                "success": False,
                "error": "Pipeline must have table_name and schema configured before starting"
            }), 400
 
        # Update frequency and unit in DB
        with db_manager.connection.cursor() as cursor:
            cursor.execute("""
                UPDATE superset_data_pipeline SET frequency = %s, unit = %s, updated_at = CURRENT_TIMESTAMP
                WHERE pipeline_id = %s
            """, (frequency_seconds, unit, pipeline_id))
            db_manager.connection.commit()
       
        # Start the scheduled task
        result = start_pipeline(pipeline_id, frequency_seconds)
 
        if result["success"]:
            return jsonify({
                "success": True,
                "message": f"Pipeline {pipeline_id} started every {frequency} {unit} using {SCHEDULER_TYPE}",
                "task_id": result["task_id"],
                "frequency": frequency,
                "unit": unit,
                "scheduler": SCHEDULER_TYPE
            }), 200
        else:
            return jsonify({
                "success": False,
                "error": result["error"]
            }), 400
 
    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}"
        }), 500
@pipeline_bp.route("/stop", methods=["POST"])
def stop():
    """
    Stop background streaming/fetching jobs.
    
    Input:
        - pipeline_id: int (optional, if not provided stops all)
    
    Functionality:
        - Stop specific pipeline or all pipelines
        - Cleanly terminate Celery workers
        - Update pipeline status in database
    """
    try:
        data = request.json or {}
        pipeline_id = data.get("pipeline_id")
        
        if pipeline_id:
            # Stop specific pipeline
            result = stop_pipeline(pipeline_id)
            
            if result["success"]:
                return jsonify({
                    "success": True,
                    "message": result["message"]
                }), 200
            else:
                return jsonify({
                    "success": False,
                    "error": result["error"]
                }), 400
        else:
            # Stop all pipelines
            result = stop_all_pipelines()
            
            if result["success"]:
                return jsonify({
                    "success": True,
                    "message": result["message"]
                }), 200
            else:
                return jsonify({
                    "success": False,
                    "error": result["error"]
                }), 400
                
    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}"
        }), 500


@pipeline_bp.route("/save_pipeline", methods=["POST"])
def save_pipeline():
    """
    Save or update pipeline configuration.
    
    Input:
        - pipeline_id: int
        - frequency: int (optional)
        - status: str (optional)
    
    Functionality:
        - Update pipeline frequency and other settings
        - Save configuration changes to database
    """
    try:
        data = request.json
        
        # Validate required fields
        pipeline_id = data.get("pipeline_id")
        
        if not pipeline_id:
            return jsonify({
                "success": False,
                "error": "pipeline_id is required"
            }), 400
        
        # Initialize DatabaseManager
        db_manager = DatabaseManager()
        
        # Check if pipeline exists
        pipeline = db_manager.get_pipeline_by_id(pipeline_id)
        if not pipeline:
            return jsonify({
                "success": False,
                "error": f"Pipeline {pipeline_id} not found"
            }), 404
        
        # Update frequency if provided
        frequency = data.get("frequency")
        if frequency:
            if not isinstance(frequency, int) or frequency < 1:
                return jsonify({
                    "success": False,
                    "error": "frequency must be a positive integer"
                }), 400
            
            # Update frequency in database
            with db_manager.connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE superset_data_pipeline 
                    SET frequency = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE pipeline_id = %s
                """, (frequency, pipeline_id))
                db_manager.connection.commit()
        
        # Update status if provided
        status = data.get("status")
        if status:
            if status not in ["active", "inactive"]:
                return jsonify({
                    "success": False,
                    "error": "status must be 'active' or 'inactive'"
                }), 400
            
            db_manager.update_pipeline_status(pipeline_id, status)
        
        return jsonify({
            "success": True,
            "message": f"Pipeline {pipeline_id} configuration saved successfully"
        }), 200
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}"
        }), 500


@pipeline_bp.route("/get_pipeline/<int:pipeline_id>", methods=["GET"])
def get_pipeline(pipeline_id):
    """
    Get detailed information about a specific pipeline.
    """
    try:
        db_manager = DatabaseManager()
        pipeline = db_manager.get_pipeline_by_id(pipeline_id)

        if not pipeline:
            return jsonify({
                "success": False,
                "error": f"Pipeline {pipeline_id} not found"
            }), 404

        return jsonify({
            "success": True,
            "pipeline": pipeline
        }), 200

    except Exception as e:
        import traceback
        print(f"Exception in get_pipeline: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}"
        }), 500


# @pipeline_bp.route("/debug_schema/<int:pipeline_id>", methods=["GET"])
# def debug_schema(pipeline_id):
#     """
#     Debug endpoint to check pipeline schema structure.
#     """
#     try:
#         db_manager = DatabaseManager()

#         # Get raw data from database
#         with db_manager.connection.cursor() as cursor:
#             cursor.execute("""
#                 SELECT pipeline_id, pipeline_name, api_curl, api_key, headers,
#                        updated_schema, unselected_schema, table_name
#                 FROM superset_data_pipeline
#                 WHERE pipeline_id = %s
#             """, (pipeline_id,))

#             row = cursor.fetchone()
#             if not row:
#                 return jsonify({
#                     "success": False,
#                     "error": f"Pipeline {pipeline_id} not found"
#                 }), 404

#             # Return raw data for debugging
#             return jsonify({
#                 "success": True,
#                 "pipeline_id": row[0],
#                 "pipeline_name": row[1],
#                 "api_curl": row[2],
#                 "api_key": row[3][:10] + "..." if row[3] else None,  # Show only first 10 chars for security
#                 "headers": row[4],
#                 "updated_schema_raw": row[5],
#                 "unselected_schema_raw": row[6],
#                 "table_name": row[7],
#                 "updated_schema_type": str(type(row[5])),
#                 "schema_is_null": row[5] is None,
#                 "api_key_exists": row[3] is not None
#             }), 200

#     except Exception as e:
#         import traceback
#         print(f"Exception in debug_schema: {e}")
#         print(f"Traceback: {traceback.format_exc()}")
#         return jsonify({
#             "success": False,
#             "error": f"Internal server error: {str(e)}"
#         }), 500


# @pipeline_bp.route("/test_db_connection", methods=["GET"])
# def test_db_connection():
#     """
#     Test database connection and table structure.
#     """
#     try:
#         db_manager = DatabaseManager()

#         # Test basic connection
#         with db_manager.connection.cursor() as cursor:
#             cursor.execute("SELECT 1")
#             result = cursor.fetchone()

#             # Check if pipeline table exists
#             cursor.execute("""
#                 SELECT EXISTS (
#                     SELECT FROM information_schema.tables
#                     WHERE table_name = 'superset_data_pipeline'
#                 );
#             """)
#             table_exists = cursor.fetchone()[0]

#             # Get table structure if it exists
#             table_structure = []
#             if table_exists:
#                 cursor.execute("""
#                     SELECT column_name, data_type, is_nullable
#                     FROM information_schema.columns
#                     WHERE table_name = 'superset_data_pipeline'
#                     ORDER BY ordinal_position;
#                 """)
#                 table_structure = cursor.fetchall()

#         return jsonify({
#             "success": True,
#             "connection_test": result[0] == 1,
#             "table_exists": table_exists,
#             "table_structure": [
#                 {"column": col[0], "type": col[1], "nullable": col[2]}
#                 for col in table_structure
#             ]
#         }), 200

#     except Exception as e:
#         import traceback
#         print(f"Exception in test_db_connection: {e}")
#         print(f"Traceback: {traceback.format_exc()}")
#         return jsonify({
#             "success": False,
#             "error": f"Database connection error: {str(e)}"
#         }), 500


# @pipeline_bp.route("/simple_schema_update", methods=["POST"])
# def simple_schema_update():
#     """
#     Simplified schema update for debugging.
#     """
#     try:
#         data = request.json
#         pipeline_id = data.get("pipeline_id")
#         selected_fields = data.get("selected_fields")
#         db_name = data.get("db", "superset")
#         table_name = data.get("table_name")

#         print(f"Simple schema update - Pipeline: {pipeline_id}, Fields: {selected_fields}")

#         if not all([pipeline_id, selected_fields, table_name]):
#             return jsonify({
#                 "success": False,
#                 "error": "pipeline_id, selected_fields, and table_name are required"
#             }), 400

#         db_manager = DatabaseManager()

#         # Direct database update without complex validation
#         with db_manager.connection.cursor() as cursor:
#             # First check if pipeline exists
#             cursor.execute("SELECT pipeline_id FROM superset_data_pipeline WHERE pipeline_id = %s", (pipeline_id,))
#             if not cursor.fetchone():
#                 return jsonify({
#                     "success": False,
#                     "error": f"Pipeline {pipeline_id} not found"
#                 }), 404

#             # Simple schema structure
#             simple_schema = {
#                 "selected_fields": selected_fields,
#                 "total_columns": len(selected_fields)
#             }

#             # Update with minimal data
#             cursor.execute("""
#                 UPDATE superset_data_pipeline
#                 SET updated_schema = %s, db = %s, table_name = %s, updated_at = CURRENT_TIMESTAMP
#                 WHERE pipeline_id = %s
#             """, (
#                 json.dumps(simple_schema),
#                 db_name,
#                 table_name,
#                 pipeline_id
#             ))

#             affected_rows = cursor.rowcount
#             db_manager.connection.commit()

#             return jsonify({
#                 "success": True,
#                 "message": f"Simple schema update completed. Affected rows: {affected_rows}",
#                 "schema": simple_schema
#             }), 200

#     except Exception as e:
#         import traceback
#         print(f"Exception in simple_schema_update: {e}")
#         print(f"Traceback: {traceback.format_exc()}")
#         return jsonify({
#             "success": False,
#             "error": f"Error: {str(e)}"
#         }), 500


# @pipeline_bp.route("/fix_pipeline_schema/<int:pipeline_id>", methods=["POST"])
# def fix_pipeline_schema(pipeline_id):
#     """
#     Fix pipeline schema by re-fetching data from the API.
#     """
#     try:
#         db_manager = DatabaseManager()

#         # Get pipeline basic info
#         with db_manager.connection.cursor() as cursor:
#             cursor.execute("""
#                 SELECT pipeline_name, api_curl, api_key, headers
#                 FROM superset_data_pipeline
#                 WHERE pipeline_id = %s
#             """, (pipeline_id,))

#             row = cursor.fetchone()
#             if not row:
#                 return jsonify({
#                     "success": False,
#                     "error": f"Pipeline {pipeline_id} not found"
#                 }), 404

#             pipeline_name, api_curl, api_key, headers = row

#         print(f"Fixing schema for pipeline {pipeline_id}: {pipeline_name}")

#         # Re-process the API to get schema
#         data_fetcher = DataFetcher()

#         # Parse headers if they exist
#         parsed_headers = None
#         if headers:
#             try:
#                 if isinstance(headers, str):
#                     parsed_headers = json.loads(headers)
#                 else:
#                     parsed_headers = headers
#             except:
#                 parsed_headers = {}

#         # Fetch and process data
#         api_response = data_fetcher.fetch_api_data(api_curl, api_key, parsed_headers)

#         if not api_response.get("success"):
#             return jsonify({
#                 "success": False,
#                 "error": f"API fetch failed: {api_response.get('error')}"
#             }), 400

#         # Clean and detect schema
#         cleaned_df = data_fetcher.clean_and_normalize_data(api_response["data"])
#         schema = data_fetcher.detect_schema(cleaned_df)

#         # Update the pipeline with the new schema
#         with db_manager.connection.cursor() as cursor:
#             cursor.execute("""
#                 UPDATE superset_data_pipeline
#                 SET updated_schema = %s, fetch_response = %s, updated_at = CURRENT_TIMESTAMP
#                 WHERE pipeline_id = %s
#             """, (
#                 json.dumps(schema),
#                 json.dumps(cleaned_df.head(5).to_dict('records') if not cleaned_df.empty else []),
#                 pipeline_id
#             ))

#             db_manager.connection.commit()

#         return jsonify({
#             "success": True,
#             "message": f"Schema fixed for pipeline {pipeline_id}",
#             "schema": schema,
#             "available_fields": list(schema.get("columns", {}).keys())
#         }), 200

#     except Exception as e:
#         import traceback
#         print(f"Exception in fix_pipeline_schema: {e}")
#         print(f"Traceback: {traceback.format_exc()}")
#         return jsonify({
#             "success": False,
#             "error": f"Error: {str(e)}"
#         }), 500


# @pipeline_bp.route("/test_scheduler", methods=["GET"])
# def test_scheduler():
#     """
#     Test which scheduler is being used.
#     """
#     try:
#         return jsonify({
#             "success": True,
#             "scheduler_type": SCHEDULER_TYPE,
#             "redis_available": REDIS_AVAILABLE,
#             "message": f"Using {SCHEDULER_TYPE} for background tasks"
#         }), 200

#     except Exception as e:
#         import traceback
#         print(f"Exception in test_scheduler: {e}")
#         print(f"Traceback: {traceback.format_exc()}")
#         return jsonify({
#             "success": False,
#             "error": f"Error: {str(e)}"
#         }), 500


# @pipeline_bp.route("/update_api_key/<int:pipeline_id>", methods=["POST"])
# def update_api_key(pipeline_id):
#     """
#     Update API key for a pipeline.
#     """
#     try:
#         data = request.json
#         new_api_key = data.get("api_key")
        
#         if not new_api_key:
#             return jsonify({
#                 "success": False,
#                 "error": "No API key provided"
#             }), 400
            
#         # Get pipeline info
#         db_manager = DatabaseManager()
#         pipeline = db_manager.get_pipeline_by_id(pipeline_id)
        
#         if not pipeline:
#             return jsonify({
#                 "success": False,
#                 "error": f"Pipeline {pipeline_id} not found"
#             }), 404
            
#         # Encrypt the new API key
#         from superset_pipeline.superset_data_fetching import api_key_manager
#         try:
#             encrypted_api_key = api_key_manager.encrypt_api_key(new_api_key)
#             if not encrypted_api_key:
#                 return jsonify({
#                     "success": False,
#                     "error": "Failed to encrypt API key"
#                 }), 500
#         except Exception as e:
#             return jsonify({
#                 "success": False,
#                 "error": f"Error encrypting API key: {str(e)}"
#             }), 500

#         # Update the API key in database
#         with db_manager.connection.cursor() as cursor:
#             cursor.execute("""
#                 UPDATE superset_data_pipeline
#                 SET api_key = %s, updated_at = CURRENT_TIMESTAMP
#                 WHERE pipeline_id = %s
#             """, (encrypted_api_key, pipeline_id))

#             db_manager.connection.commit()
            
#         # Test if the API works with the new key
#         api_url = pipeline.get("api_curl", "").rstrip('"\'')
#         if '{api_key}' in api_url:
#             test_url = api_url.replace('{api_key}', new_api_key)
#             try:
#                 response = requests.get(test_url, timeout=5)
#                 if response.status_code != 200:
#                     return jsonify({
#                         "success": True,
#                         "warning": f"API key updated but test request returned status code {response.status_code}",
#                         "pipeline_id": pipeline_id
#                     }), 200
#             except Exception as e:
#                 return jsonify({
#                     "success": True,
#                     "warning": f"API key updated but test request failed: {str(e)}",
#                     "pipeline_id": pipeline_id
#                 }), 200

#         return jsonify({
#             "success": True,
#             "message": f"API key updated for pipeline {pipeline_id}",
#             "pipeline_id": pipeline_id
#         }), 200

#     except Exception as e:
#         import traceback
#         print(f"Exception in update_api_key: {e}")
#         print(f"Traceback: {traceback.format_exc()}")
#         return jsonify({
#             "success": False,
#             "error": f"Error: {str(e)}"
#         }), 500


# @pipeline_bp.route("/manual_fetch/<int:pipeline_id>", methods=["POST"])
# def manual_fetch(pipeline_id):
#     """
#     Manually trigger a single data fetch for testing.
#     """
#     try:
#         from superset_pipeline.simple_scheduler import SimpleScheduler

#         scheduler = SimpleScheduler()
#         result = scheduler.fetch_and_store_data(pipeline_id)

#         return jsonify({
#             "success": True,
#             "result": result,
#             "message": f"Manual fetch completed for pipeline {pipeline_id}"
#         }), 200

#     except Exception as e:
#         import traceback
#         print(f"Exception in manual_fetch: {e}")
#         print(f"Traceback: {traceback.format_exc()}")
#         return jsonify({
#             "success": False,
#             "error": f"Error: {str(e)}"
#         }), 500


# @pipeline_bp.route("/debug_active_tasks", methods=["GET"])
# def debug_active_tasks():
#     """
#     Debug endpoint to check active background tasks.
#     """
#     try:
#         # Import the active tasks from simple scheduler
#         from superset_pipeline.simple_scheduler import active_tasks, task_threads

#         task_info = {}
#         for pipeline_id, task_data in active_tasks.items():
#             thread = task_threads.get(pipeline_id)
#             task_info[pipeline_id] = {
#                 "task_data": task_data,
#                 "thread_alive": thread.is_alive() if thread else False,
#                 "thread_name": thread.name if thread else None
#             }

#         return jsonify({
#             "success": True,
#             "active_tasks_count": len(active_tasks),
#             "active_tasks": task_info,
#             "scheduler_type": SCHEDULER_TYPE
#         }), 200

#     except Exception as e:
#         import traceback
#         print(f"Exception in debug_active_tasks: {e}")
#         print(f"Traceback: {traceback.format_exc()}")
#         return jsonify({
#             "success": False,
#             "error": f"Error: {str(e)}"
#         }), 500


# @pipeline_bp.route("/test_encryption", methods=["POST"])
# def test_encryption():
#     """
#     Test API key encryption/decryption functionality.
#     """
#     try:
#         data = request.json
#         test_api_key = data.get("api_key", "test_key_12345")

#         from superset_pipeline.superset_data_fetching import api_key_manager

#         # Encrypt the key
#         encrypted = api_key_manager.encrypt_api_key(test_api_key)

#         # Decrypt the key
#         decrypted = api_key_manager.decrypt_api_key(encrypted)

#         return jsonify({
#             "success": True,
#             "original": test_api_key,
#             "encrypted": encrypted,
#             "decrypted": decrypted,
#             "encryption_works": test_api_key == decrypted
#         }), 200

#     except Exception as e:
#         import traceback
#         print(f"Exception in test_encryption: {e}")
#         print(f"Traceback: {traceback.format_exc()}")
#         return jsonify({
#             "success": False,
#             "error": f"Error: {str(e)}"
#         }), 500


# @pipeline_bp.route("/test_api_key_decryption/<int:pipeline_id>", methods=["GET"])
# def test_api_key_decryption(pipeline_id):
    # """
    # Test API key decryption for a specific pipeline.
    # """
    # try:
    #     db_manager = DatabaseManager()
    #     pipeline = db_manager.get_pipeline_by_id(pipeline_id)

    #     if not pipeline:
    #         return jsonify({
    #             "success": False,
    #             "error": f"Pipeline {pipeline_id} not found"
    #         }), 404

    #     encrypted_api_key = pipeline.get("api_key")

    #     if not encrypted_api_key:
    #         return jsonify({
    #             "success": False,
    #             "error": "No API key found in pipeline"
    #         }), 400

    #     # Test decryption
    #     from superset_pipeline.superset_data_fetching import api_key_manager
    #     decrypted_key = api_key_manager.decrypt_api_key(encrypted_api_key)

    #     return jsonify({
    #         "success": True,
    #         "pipeline_id": pipeline_id,
    #         "has_encrypted_key": encrypted_api_key is not None,
    #         "encrypted_key_length": len(encrypted_api_key) if encrypted_api_key else 0,
    #         "decryption_successful": decrypted_key is not None,
    #         "decrypted_key_length": len(decrypted_key) if decrypted_key else 0,
    #         "decrypted_key_preview": decrypted_key[:10] + "..." if decrypted_key else None
    #     }), 200

    # except Exception as e:
    #     import traceback
    #     print(f"Exception in test_api_key_decryption: {e}")
    #     print(f"Traceback: {traceback.format_exc()}")
    #     return jsonify({
    #         "success": False,
    #         "error": f"Error: {str(e)}"
    #     }), 500


# @pipeline_bp.route("/create_test_pipeline", methods=["POST"])
# def create_test_pipeline():
    # """
    # Create a test pipeline with a free API for testing.
    # """
    # try:
    #     data_fetcher = DataFetcher()

    #     # Create pipeline with free API
    #     result = data_fetcher.process_api_pipeline(
    #         pipeline_name="test_free_api",
    #         api_url="https://jsonplaceholder.typicode.com/users/1",
    #         api_key=None,
    #         headers={"Content-Type": "application/json"}
    #     )

    #     if result["success"]:
    #         return jsonify({
    #             "success": True,
    #             "pipeline_id": result["pipeline_id"],
    #             "schema": result["schema"],
    #             "sample_data": result["sample_data"],
    #             "message": "Test pipeline created successfully",
    #             "next_steps": [
    #                 f"1. Configure schema: POST /pipeline/schema_selection_and_table_creation",
    #                 f"2. Start pipeline: POST /pipeline/start"
    #             ]
    #         }), 200
    #     else:
    #         return jsonify({
    #             "success": False,
    #             "error": result["error"]
    #         }), 400

    # except Exception as e:
    #     import traceback
    #     print(f"Exception in create_test_pipeline: {e}")
    #     print(f"Traceback: {traceback.format_exc()}")
    #     return jsonify({
    #         "success": False,
    #         "error": f"Error: {str(e)}"
    #     }), 500


# @pipeline_bp.route("/debug_pipeline_schema/<int:pipeline_id>", methods=["GET"])
# def debug_pipeline_schema(pipeline_id):
#     """
#     Debug endpoint to show complete pipeline schema information.
#     """
#     try:
#         db_manager = DatabaseManager()
#         pipeline = db_manager.get_pipeline_by_id(pipeline_id)

#         if not pipeline:
#             return jsonify({
#                 "success": False,
#                 "error": f"Pipeline {pipeline_id} not found"
#             }), 404

#         # Get current schemas
#         current_schema = pipeline.get("updated_schema", {})
#         unselected_schema = pipeline.get("unselected_schema", {})

#         # Merge all available columns
#         all_columns = current_schema.get("columns", {}).copy()
#         unselected_columns = unselected_schema.get("columns", {})
#         all_columns.update(unselected_columns)

#         return jsonify({
#             "success": True,
#             "pipeline_id": pipeline_id,
#             "pipeline_name": pipeline.get("pipeline_name"),
#             "current_selected_fields": current_schema.get("selected_fields", []),
#             "current_unselected_fields": unselected_schema.get("unselected_fields", []),
#             "all_available_fields": list(all_columns.keys()),
#             "current_schema_columns": list(current_schema.get("columns", {}).keys()),
#             "unselected_schema_columns": list(unselected_columns.keys()),
#             "table_name": pipeline.get("table_name"),
#             "total_available_fields": len(all_columns)
#         }), 200

#     except Exception as e:
#         import traceback
#         print(f"Exception in debug_pipeline_schema: {e}")
#         print(f"Traceback: {traceback.format_exc()}")
#         return jsonify({
#             "success": False,
#             "error": f"Error: {str(e)}"
#         }), 500


# @pipeline_bp.route("/get_table_info/<table_name>", methods=["GET"])
# def get_table_info(table_name):
#     """
#     Get information about an existing table structure.
#     """
#     try:
#         db_manager = DatabaseManager()

#         # Check if table exists
#         with db_manager.connection.cursor() as cursor:
#             cursor.execute("""
#                 SELECT EXISTS (
#                     SELECT FROM information_schema.tables
#                     WHERE table_name = %s AND table_schema = 'public'
#                 );
#             """, (table_name,))

#             table_exists = cursor.fetchone()[0]

#             if not table_exists:
#                 return jsonify({
#                     "success": False,
#                     "error": f"Table '{table_name}' does not exist"
#                 }), 404

#             # Get table columns with types
#             cursor.execute("""
#                 SELECT column_name, data_type, is_nullable
#                 FROM information_schema.columns
#                 WHERE table_name = %s AND table_schema = 'public'
#                 ORDER BY ordinal_position;
#             """, (table_name,))

#             columns = []
#             for row in cursor.fetchall():
#                 columns.append({
#                     "column_name": row[0],
#                     "data_type": row[1],
#                     "is_nullable": row[2]
#                 })

#             # Get row count
#             cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
#             row_count = cursor.fetchone()[0]

#         return jsonify({
#             "success": True,
#             "table_name": table_name,
#             "table_exists": table_exists,
#             "columns": columns,
#             "total_columns": len(columns),
#             "row_count": row_count
#         }), 200

#     except Exception as e:
#         import traceback
#         print(f"Exception in get_table_info: {e}")
#         print(f"Traceback: {traceback.format_exc()}")
#         return jsonify({
#             "success": False,
#             "error": f"Error: {str(e)}"
#         }), 500


@pipeline_bp.route("/get_dbs", methods=["get"])
def dbs():
    """
    Get all databases from superset
    """
    try:
        db_manager = DatabaseManager()
        with db_manager.connection.cursor() as cursor:
            cursor.execute("SELECT database_name FROM dbs")
            dbs = [row[0] for row in cursor.fetchall()]  # ✅ flatten the result
        return jsonify({
            "success": True,
            "dbs": dbs
        }), 200
    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}"
        }), 500

@pipeline_bp.route("/status", methods=["GET"])
def status():
    """
    Get status of all pipelines and running tasks with pagination.
    """
    try:
        db_manager = DatabaseManager()

        # Get and validate pagination params
        page = int(request.args.get("page_index", 0))
        limit = max(int(request.args.get("page_size", 10)), 1)

        page = page + 1
        
        offset = (page - 1) * limit

        # Query total count for pagination
        with db_manager.connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM superset_data_pipeline")
            total_count = cursor.fetchone()[0]

        # Get paginated pipelines
        with db_manager.connection.cursor() as cursor:
            cursor.execute("""
                SELECT pipeline_id, pipeline_name, status, health, unit,
                       frequency, table_name, updated_at
                FROM superset_data_pipeline
                ORDER BY pipeline_id
                LIMIT %s OFFSET %s
            """, (limit, offset))

            pipelines = []
            for row in cursor.fetchall():
                pipelines.append({
                    "pipeline_id": row[0],
                    "pipeline_name": row[1],
                    "status": row[2],
                    "health": row[3],
                    "unit": row[4],
                    "frequency": row[5],
                    "table_name": row[6],
                    "updated_at": row[7].isoformat() if row[7] else None
                })

        # Get task status
        task_status = get_pipeline_status()

        return jsonify({
            "success": True,
            "pipelines": pipelines,
            "task_status": task_status,
            "scheduler_type": SCHEDULER_TYPE,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total_count,
                "total_pages": (total_count + limit - 1) // limit
            }
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}"
        }), 500


@pipeline_bp.route("/delete/<int:pipeline_id>", methods=["DELETE"])
def delete_pipeline(pipeline_id):
    """
    Delete a pipeline and all related data.
    
    Functionality:
        - Stop the pipeline if it's running
        - Drop the associated table (can be disabled with drop_table=false)
        - Delete the pipeline record from database
    """
    try:
        # Always drop table by default
        drop_table = request.args.get('drop_table', 'true').lower() == 'true'
        
        # First stop the pipeline if it's running
        stop_result = stop_pipeline(pipeline_id)
        if not stop_result["success"] and "not found" not in stop_result.get("error", ""):
            return jsonify({
                "success": False,
                "error": f"Failed to stop pipeline: {stop_result['error']}"
            }), 400
        
        # Get pipeline info before deletion
        db_manager = DatabaseManager()
        pipeline = db_manager.get_pipeline_by_id(pipeline_id)
        
        if not pipeline:
            return jsonify({
                "success": False,
                "error": f"Pipeline {pipeline_id} not found"
            }), 404
        
        pipeline_name = pipeline.get("pipeline_name")
        table_name = pipeline.get("table_name")
        
        # Drop the associated table if it exists
        table_dropped = False
        if table_name and drop_table:
            try:
                with db_manager.connection.cursor() as cursor:
                    # Force drop the table with CASCADE to handle dependencies
                    drop_sql = f'DROP TABLE IF EXISTS "{table_name}" CASCADE'
                    print(f"Executing: {drop_sql}")
                    cursor.execute(drop_sql)
                    db_manager.connection.commit()
                    
                    # Verify table was dropped
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_name = %s AND table_schema = 'public'
                        );
                    """, (table_name,))
                    
                    table_still_exists = cursor.fetchone()[0]
                    table_dropped = not table_still_exists
                    
                    if table_still_exists:
                        print(f"WARNING: Table '{table_name}' still exists after DROP command")
                    else:
                        print(f"Table '{table_name}' dropped successfully")
            except Exception as e:
                import traceback
                print(f"Error dropping table {table_name}: {e}")
                print(traceback.format_exc())
        
        # Delete the pipeline from database
        with db_manager.connection.cursor() as cursor:
            cursor.execute("""
                DELETE FROM superset_data_pipeline
                WHERE pipeline_id = %s
            """, (pipeline_id,))
            
            if cursor.rowcount == 0:
                return jsonify({
                    "success": False,
                    "error": f"Pipeline {pipeline_id} not found or already deleted"
                }), 404
                
            db_manager.connection.commit()
        
        return jsonify({
            "success": True,
            "message": f"Pipeline '{pipeline_name}' (ID: {pipeline_id}) deleted successfully",
            "pipeline_id": pipeline_id,
            "pipeline_name": pipeline_name,
            "table_dropped": table_dropped,
            "table_name": table_name
        }), 200
            
    except Exception as e:
        import traceback
        print(f"Exception in delete_pipeline: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}"
        }), 500
