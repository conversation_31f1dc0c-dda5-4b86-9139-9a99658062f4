import os
import json
import logging
import tempfile
from bson import Binary
from datetime import datetime
from dotenv import load_dotenv
from werkzeug.utils import secure_filename
from mongoengine import NotUniqueError
from utils.file_upload import UploadedFile, export_mongodb_dump
from utils.delete_file import delete_file, delete_all_files
from utils import insert_chat_history, chat_search_live
from utils.common import superset_connection
from flask import Blueprint, request, Response, jsonify, send_file
from agents.architect import AgentOrchestrator, AgentState
from utils.collect_dashboard_data import file_upload_agent

load_dotenv()

agents_bp = Blueprint("agent", __name__)
MODEL_PROVIDERS = json.loads(os.environ.get("MODEL_PROVIDERS"))


ALLOWED_EXTENSIONS = {"csv", "pdf", "jpg", "jpeg", "png", "gif"}


def allowed_file(filename):
    return "." in filename and filename.rsplit(".", 1)[1].lower() in ALLOWED_EXTENSIONS


def save_uploaded_file(file, metadata={}, dashboard_id=None):
    """Save uploaded file to MongoDB with metadata."""

    logging.info(f"SAVE FILE---> {file.filename} metadata {metadata}")
    try:
        existing_file = UploadedFile.objects(file_name=file.filename, extra_metadata__dashboard_id=dashboard_id).first()
        if existing_file:
            return str(existing_file.id)

        file.seek(0)  # Ensure pointer is at the beginning
        file_data = file.read()
        if not file_data:
            logging.warning(f"[WARNING] File {file.filename} is empty or unreadable.")
            return None

        uploaded_file = UploadedFile(
            file_name=file.filename,
            file_data=Binary(file_data),
            content_type=file.content_type,
            extra_metadata=metadata,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        uploaded_file.save()
        logging.info(f"File saved to MongoDB: {uploaded_file.id}")
        return str(uploaded_file.id)
    except NotUniqueError:
        logging.warning(f"File {file.filename} already exists in MongoDB.")
        existing_file = UploadedFile.objects(file_name=file.filename).first()
        return str(existing_file.id) if existing_file else None


def stream_query_response(query, dashboard_id, model_name, model_providers, file_paths):
    """Stream the entire response as a single JSON object after collecting all chunks."""

    csv_file_paths = (
        [path for path in file_paths if path.lower().endswith(".csv")]
        if isinstance(file_paths, list)
        else []
    )
    pdf_file_paths = (
        [path for path in file_paths if path.lower().endswith(".pdf")]
        if isinstance(file_paths, list)
        else []
    )

    initial_state = AgentState(
        query=query,
        dashboard_id=dashboard_id,
        model_providers=model_providers,
        model_name=model_name,
        agent_outputs={},
        verbose={},
        csv_file_paths=csv_file_paths,
        pdf_file_path=pdf_file_paths,
    )

    graph = AgentOrchestrator(
        dashboard_id=dashboard_id,
        model_name=model_name,
        model_providers=model_providers,
        file_paths=file_paths,
    )
    compiled_graph = graph._build_graph()
    response_data = []
    for chunk in compiled_graph.stream(initial_state):
        response_data.append(chunk)

    final_response = json.dumps({"data": response_data}, default=str)

    return Response(final_response, content_type="application/json")


@agents_bp.route("/file_upload", methods=["POST"])
def file_upload():
    """
    Upload files with metadata. Handles both dashboard association and chat-based ad-hoc uploads.
    Required:
        - 'files': one or more files
        - 'description': JSON mapping file names to their metadata
        - 'upload_type': 'dashboard' or 'chat'
    Optional:
        - 'dashboard_id': required if upload_type is 'dashboard'
    """
    logging.info("----" * 30)
    logging.info("File upload request received")
    # Validate request
    if (
        "files" not in request.files
        or "description" not in request.form
        or "upload_type" not in request.form
    ):
        return jsonify({"error": "Missing files, description, or upload_type"}), 400

    upload_type = request.form["upload_type"]
    description_json = json.loads(request.form["description"])
    files = request.files.getlist("files")
    dashboard_id = request.form.get("dashboard_id")
    logging.info("File upload request body")
    logging.info("----" * 10)
    logging.info(
        f"upload_type: {upload_type}, dashboard_id: {dashboard_id}, description_json: {description_json}, files: {files}"
    )
    logging.info("----" * 10)
    if upload_type == "dashboard" and not dashboard_id:
        return jsonify({"error": "dashboard_id is required for dashboard upload"}), 400

    path_uploaded_files = file_upload_agent(
        description_json=description_json,
        files=files,
        upload_type=upload_type,
        dashboard_id=dashboard_id,
    )
    uploaded_files = []
    file_ids = []
    for file in files:
        file_metadata = {}
        logging.info("----" * 10)
        file_name = file.filename
        logging.info(f"File name: {file_name}, upload_type: {upload_type}")
        # Get metadata for this file
        file_metadata["file_name"] = description_json.get(file_name, "")
        file_metadata["upload_type"] = upload_type
        file_metadata["file_path"] = next(
            (
                f["file_path"]
                for f in path_uploaded_files
                if f["file_name"] == secure_filename(file.filename)
            ),
            None,
        )
        if dashboard_id:
            file_metadata["dashboard_id"] = dashboard_id

        file_id = save_uploaded_file(file, metadata=file_metadata, dashboard_id=dashboard_id
        )
        file_ids.append(file_id)
        uploaded_files.append(
            {
                "file_name": file_name,
                "file_path": next(
                    (
                        f["file_path"]
                        for f in path_uploaded_files
                        if f["file_name"] == secure_filename(file.filename)
                    ),
                    None,
                ),
                "file_id": file_id,
                "associated_dashboard": (
                    dashboard_id if upload_type == "dashboard" else None
                ),
            }
        )
    logging.info("^^^^" * 10)
    logging.info(f"Uploaded files: {uploaded_files}, file_ids: {file_ids}")
    logging.info("^^^^" * 10)
    logging.info("File upload request completed")
    logging.info("----" * 30)
    return jsonify({"uploaded_files": uploaded_files})


@agents_bp.route("/preview-file", methods=["GET"])
def preview_file():
    file_path = request.args.get("file_path")

    if not file_path:
        return jsonify({"error": "Missing file_path"}), 400

    try:
        uploaded_file = UploadedFile.objects.get(extra_metadata__file_path=file_path)
        file_path = uploaded_file.extra_metadata.get("file_path")

        if not file_path or not os.path.exists(file_path):
            return jsonify({"error": "File not found on server"}), 404

        return send_file(
            file_path,
            mimetype=uploaded_file.content_type,
            as_attachment=False,
            download_name=uploaded_file.file_name,
        )

    except UploadedFile.DoesNotExist:
        return jsonify({"error": "File not found in database"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@agents_bp.route("/query", methods=["POST"])
def query():
    """Flask endpoint to process user queries via the compiled agent graph."""

    data = request.json

    # Extract and validate required fields
    query_text = data.get("query")
    model_name = data.get("model_name")
    dashboard_id = data.get("dashboard_id", 0)
    model_provider = data.get("model_provider", "openai")
    file_paths = data.get("file_paths", [])

    if not query_text:
        return jsonify({"error": "Query is required"}), 400

    try:
        dashboard_id = int(dashboard_id)
    except (TypeError, ValueError):
        return jsonify({"error": "dashboard_id must be an integer"}), 400

    os.environ["MODEL_PROVIDER"] = model_provider
    os.environ["MODEL_NAME"] = model_name

    if model_name == "gpt-4o-mini":
        model_provider = "openai"

    insert_chat_history(model_name, "user", query_text, dashboard_id=dashboard_id)

    response = stream_query_response(
        query_text,
        dashboard_id,
        model_name,
        model_provider,
        file_paths,
    )

    return response


# ------------------------------------------------- Model APIS ----------------------------------------------------------


@agents_bp.route("/model_providers", methods=["GET"])
def get_model_providers():
    return jsonify({"providers": list(MODEL_PROVIDERS.keys())})


@agents_bp.route("/models", methods=["GET"])
def get_models_by_provider():
    provider = request.args.get("provider")

    try:
        with superset_connection.cursor() as cur:
            if not provider:
                return jsonify({"error": "Provider parameter is required"}), 400

            if provider == "-1":
                cur.execute(
                    "SELECT provider, model_name, display_name FROM model_providers"
                )
                rows = cur.fetchall()

                provider_map = {}
                for prov, model, display in rows:
                    if prov not in provider_map:
                        provider_map[prov] = {}
                    provider_map[prov][display] = model

                return jsonify({"provider": "all", "models": provider_map}), 200

            # Query specific provider
            cur.execute(
                "SELECT model_name, display_name FROM model_providers WHERE provider = %s",
                (provider,),
            )
            rows = cur.fetchall()

            if not rows:
                return (
                    jsonify({"error": f"No models found for provider '{provider}'"}),
                    404,
                )

            display_map = {display: model for model, display in rows}

            return jsonify({"provider": provider, "models": display_map}), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@agents_bp.route("/add_model", methods=["POST"])
def add_model():
    data = request.get_json()
    provider = data.get("provider")
    model_name = data.get("model_name")
    display_name = data.get("display_name")

    if not all([provider, model_name, display_name]):
        return (
            jsonify({"error": "provider, model_name, and display_name are required"}),
            400,
        )

    try:
        with superset_connection.cursor() as cur:
            cur.execute(
                """
                INSERT INTO model_providers (provider, model_name, display_name)
                VALUES (%s, %s, %s)
                ON CONFLICT (provider, model_name) DO NOTHING
                """,
                (provider, model_name, display_name),
            )

            if cur.rowcount == 0:
                return jsonify({"message": "Model already exists."}), 200

        superset_connection.commit()
        return jsonify({"message": "Model added successfully"}), 201

    except Exception as e:
        superset_connection.rollback()
        return jsonify({"error": str(e)}), 500


@agents_bp.route("/update-display-name", methods=["POST"])
def update_display_name():
    data = request.get_json()
    provider = data.get("provider")
    model_name = data.get("model_name")
    new_display_name = data.get("display_name")

    if not all([provider, model_name, new_display_name]):
        return (
            jsonify({"error": "provider, model_name, and display_name are required"}),
            400,
        )

    try:
        with superset_connection.cursor() as cur:
            cur.execute(
                """
                UPDATE model_providers
                SET display_name = %s, updated_on = CURRENT_TIMESTAMP
                WHERE provider = %s AND model_name = %s
                """,
                (new_display_name, provider, model_name),
            )

            if cur.rowcount == 0:
                superset_connection.rollback()
                return jsonify({"error": "No matching record found"}), 404

        superset_connection.commit()
        return jsonify({"message": "Display name updated successfully"}), 200

    except Exception as e:
        superset_connection.rollback()
        return jsonify({"error": str(e)}), 500


# ------------------------------------------------- Model APIS ----------------------------------------------------------


@agents_bp.route("/search_chat", methods=["GET"])
def search_chat():
    """Live search API for user chat history content, filtered by dashboard_id/dataset_id."""
    search_query = request.args.get("q", "").strip()
    dashboard_id = request.args.get("dashboard_id", "").strip()
    dataset_id = request.args.get("dataset_id", "").strip()

    # Ensure at least one of dashboard_id or dataset_id is provided
    if not (dashboard_id or dataset_id):
        return jsonify({"results": []})

    try:
        results = chat_search_live(
            search_query=search_query, dashboard_id=dashboard_id, dataset_id=dataset_id
        )
        return jsonify({"results": results})

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@agents_bp.route("/get_file_db", methods=["GET"])
def get_file_db():
    """Export MongoDB data and return it as a downloadable JSON file."""

    dump_json = export_mongodb_dump()

    if not dump_json:
        return jsonify({"error": "No data found in MongoDB"}), 404

    response = Response(
        dump_json,
        mimetype="application/json",
        headers={"Content-Disposition": "attachment; filename=mongodb_dump.json"},
    )

    return response


@agents_bp.route("/delete_file", methods=["POST"])
def delete_file_api():
    """
    API to delete a specific file or all files.
    If `file_name` is `-1`, delete all files.
    If `dashboard_id` is provided, delete file associated with that dashboard.
    """
    data = request.get_json()
    file_path = data.get("file_path")
    dashboard_id = data.get("dashboard_id")

    if not file_path:
        return jsonify({"error": "Missing 'file_path' in request."}), 400

    try:
        if file_path == "-1":
            result = delete_all_files(dashboard_id)
        else:
            result = delete_file(file_path, dashboard_id)

        return jsonify(result)
    except Exception as e:
        logging.error(f"Error deleting file: {str(e)}")
        return jsonify({"error": f"Failed to delete file(s): {str(e)}"}), 500
    
# ------------------------------------------------- assets API ----------------------------------------------------------

@agents_bp.route("/upload_logo", methods=["POST"])
def upload_logo():
    """
    Upload a logo image file or loading gif.
    Required:
        - 'logo': the logo image file to upload
        - 'upload_type': must be 'logo' or 'loading_gif'
    Returns:
        - JSON with the uploaded file details
    """
    logging.info("Logo/Loading gif upload request received")
   
    # Check if logo file is in the request
    if "logo" not in request.files:
        return jsonify({"error": "No file provided"}), 400
   
    # Check if upload_type is provided
    upload_type = request.form.get("upload_type")
    if not upload_type:
        return jsonify({"error": "upload_type is required"}), 400
   
    # Verify upload_type is valid
    if upload_type not in ["logo", "loading_gif"]:
        return jsonify({"error": "upload_type must be 'logo' or 'loading_gif'"}), 400
   
    logo_file = request.files["logo"]
   
    # Check if a valid file was selected
    if logo_file.filename == "":
        return jsonify({"error": "No file selected"}), 400
   
    # Check if the file extension is allowed
    if not allowed_file(logo_file.filename):
        allowed_types = ', '.join(ext for ext in ALLOWED_EXTENSIONS if ext in ['jpg', 'jpeg', 'png', 'gif'])
        return jsonify({"error": f"File type not allowed. Supported types: {allowed_types}"}), 400
   
    try:
        # Define target paths based on upload type
        if upload_type == "logo":
            target_paths = [
                "superset/static/assets/images/logo.png",
                "superset/static/assets/images/superset-logo-horiz copy.png",
                "superset/static/assets/images/favicon.png"
            ]
        else:  # loading_gif
            target_paths = [
                "superset/static/assets/images/loading.gif"
            ]
        
        # Replace files directly in target locations
        replaced_files = []
        for target_path in target_paths:
            # Ensure directory exists
            target_dir = os.path.dirname(target_path)
            os.makedirs(target_dir, exist_ok=True)
            
            # Save uploaded file directly to target location
            logo_file.seek(0)  # Reset file pointer
            logo_file.save(target_path)
            replaced_files.append(target_path)
            logging.info(f"Replaced file: {target_path}")
       
        with superset_connection.cursor() as cursor:
            # Check if record already exists
            cursor.execute("SELECT id, file_path FROM logo WHERE upload_type = %s", (upload_type,))
            existing_record = cursor.fetchone()
           
            if existing_record:
                # Update existing record
                cursor.execute(
                    """
                    UPDATE logo
                    SET file_name = %s, file_path = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE upload_type = %s
                    """,
                    (logo_file.filename, ", ".join(replaced_files), upload_type)
                )
                message = f"{upload_type.replace('_', ' ').title()} updated successfully"
            else:
                # Insert new record
                cursor.execute(
                    """
                    INSERT INTO logo (file_name, file_path, upload_type)
                    VALUES (%s, %s, %s)
                    """,
                    (logo_file.filename, ", ".join(replaced_files), upload_type)
                )
                message = f"{upload_type.replace('_', ' ').title()} uploaded successfully"
           
        superset_connection.commit()
       
        return jsonify({
            "message": message,
            "upload_type": upload_type,
            "replaced_files": replaced_files
        }), 200
       
    except Exception as e:
        superset_connection.rollback()
        logging.error(f"Error uploading {upload_type}: {str(e)}")
        return jsonify({"error": f"Failed to upload {upload_type}: {str(e)}"}), 500

@agents_bp.route("/image_upload", methods=["POST"])
def image_upload():
    """
    Upload an image file using only the image path.
    Required:
        - 'image': the image file to upload
    Returns:
        - JSON with the uploaded image details
    """
    logging.info("Image upload request received")
   
    # Check if image file is in the request
    if "image" not in request.files:
        return jsonify({"error": "No image file provided"}), 400
   
    image = request.files["image"]
   
    # Check if a valid file was selected
    if image.filename == "":
        return jsonify({"error": "No image selected"}), 400
   
    # Check if the file extension is allowed
    if not allowed_file(image.filename):
        return jsonify({"error": f"File type not allowed. Supported types: {', '.join(ext for ext in ALLOWED_EXTENSIONS if ext in ['jpg', 'jpeg', 'png', 'gif'])}"}), 400
   
    # Create temp directory and save file
    temp_dir = tempfile.mkdtemp()
    file_path = os.path.join(temp_dir, secure_filename(image.filename))
    image.save(file_path)
   
    # Save metadata to MongoDB
    file_metadata = {"upload_type": "image", "file_path": file_path}
    file_id = save_uploaded_file(image, metadata=file_metadata)
   
    if not file_id:
        return jsonify({"error": "Failed to save image"}), 500
   
    return jsonify({
        "message": "Image uploaded successfully",
        "file_name": image.filename,
        "file_id": file_id,
        "file_path": file_path
    })    
    
@agents_bp.route("/preview-image", methods=["GET"])
def preview_image():
    file_path = request.args.get("file_path")
 
    if not file_path:
        return jsonify({"error": "Missing file_path"}), 400
 
    try:
        if not os.path.exists(file_path):
            return jsonify({"error": "Image not found on server"}), 404
 
        # Determine the mimetype based on file extension
        file_extension = os.path.splitext(file_path)[1].lower()
        mimetype = None
        if file_extension in ['.jpg', '.jpeg']:
            mimetype = 'image/jpeg'
        elif file_extension == '.png':
            mimetype = 'image/png'
        elif file_extension == '.gif':
            mimetype = 'image/gif'
        else:
            mimetype = 'application/octet-stream'
 
        return send_file(
            file_path,
            mimetype=mimetype,
            as_attachment=False,
            download_name=os.path.basename(file_path),
        )
 
    except Exception as e:
        return jsonify({"error": str(e)}), 500
 