# routers/agents.py - Enhanced version with reinforcement learning

import os
import json
import logging
import time
import traceback as tb
from psycopg2.extras import RealDictCursor
from flask import Blueprint, request, jsonify, Response
from agents.reinforcement_agent import (
    FeedbackLearningSystem,
    EnhancedAgentOrchestrator,
    AgentState,
    AgentOrchestrator,
)
from utils.common import insert_chat_history

reinforced_agents = Blueprint("reinforced_agents", __name__)


learning_system = None


def initialize_learning_system():
    """Initialize the learning system once when the app starts"""
    global learning_system
    if learning_system is None:
        try:
            # You'll need to pass your database connection here
            from config import Config

            config = Config()

            # Assuming you have a way to get database connection
            db_connection = (
                get_database_connection()
            )  # Implement this based on your DB setup

            learning_system = FeedbackLearningSystem(db_connection)
            learning_system.initialize_from_feedback_data()
            logging.info("Reinforcement learning system initialized successfully")
        except Exception as e:
            logging.error(f"Failed to initialize learning system: {e}")
            learning_system = None

    return learning_system


@reinforced_agents.route("/query", methods=["POST"])
def query():
    """Enhanced Flask endpoint with reinforcement learning capabilities"""

    logging.info("----" * 30)
    logging.info("Query request received")
    data = request.json

    logging.info(data)
    # Extract and validate required fields
    query_text = data.get("query")
    model_name = data.get("model_name")
    dashboard_id = data.get("dashboard_id", 0)
    model_provider = data.get("model_provider", "openai")
    file_paths = data.get("file_paths", [])

    if not query_text:
        return jsonify({"error": "Query is required"}), 400

    try:
        dashboard_id = int(dashboard_id)
    except (TypeError, ValueError):
        return jsonify({"error": "dashboard_id must be an integer"}), 400

    os.environ["MODEL_PROVIDER"] = model_provider
    os.environ["MODEL_NAME"] = model_name

    if model_name == "gpt-4o-mini":
        model_provider = "openai"

    # Insert user message to chat history
    user_message_id = insert_chat_history(
        model_name, "user", query_text, dashboard_id=dashboard_id
    )

    # Initialize learning system if not already done
    current_learning_system = initialize_learning_system()

    # Stream response with enhanced orchestrator
    response = stream_query_response_enhanced(
        query_text,
        dashboard_id,
        model_name,
        model_provider,
        file_paths,
        current_learning_system,
        user_message_id,
    )

    return response


def stream_query_response_enhanced(
    query,
    dashboard_id,
    model_name,
    model_providers,
    file_paths,
    learning_system,
    user_message_id,
):
    """Enhanced streaming response with reinforcement learning"""

    csv_file_paths = (
        [path for path in file_paths if path.lower().endswith(".csv")]
        if isinstance(file_paths, list)
        else []
    )
    pdf_file_paths = (
        [path for path in file_paths if path.lower().endswith(".pdf")]
        if isinstance(file_paths, list)
        else []
    )

    initial_state = AgentState(
        query=query,
        dashboard_id=dashboard_id,
        model_providers=model_providers,
        model_name=model_name,
        agent_outputs={},
        verbose={},
        csv_file_paths=csv_file_paths,
        pdf_file_path=pdf_file_paths,
        user_message_id=user_message_id,
    )

    # Use enhanced orchestrator if learning system is available
    if learning_system and learning_system.reinforced_master:
        try:
            # Create enhanced orchestrator
            graph = EnhancedAgentOrchestrator(
                performance_tracker=learning_system.performance_tracker,
                reinforced_master=learning_system.reinforced_master,
                dashboard_id=dashboard_id,
                model_name=model_name,
                model_providers=model_providers,
                file_paths=file_paths,
            )

            # Log that we're using enhanced version
            logging.info("Using enhanced orchestrator with reinforcement learning")

        except Exception as e:
            logging.error(f"Failed to create enhanced orchestrator: {e}")
            # Fallback to original orchestrator
            graph = AgentOrchestrator(
                dashboard_id=dashboard_id,
                model_name=model_name,
                model_providers=model_providers,
                file_paths=file_paths,
            )
            logging.info("Falling back to original orchestrator")
    else:
        # Use original orchestrator as fallback
        graph = AgentOrchestrator(
            dashboard_id=dashboard_id,
            model_name=model_name,
            model_providers=model_providers,
            file_paths=file_paths,
        )
        logging.info("Using original orchestrator (learning system not available)")

    # Build and execute graph
    compiled_graph = graph._build_graph()
    response_data = []

    # Collect performance metrics during execution
    execution_start_time = time.time()
    agent_path = []

    try:
        for chunk in compiled_graph.stream(initial_state):
            response_data.append(chunk)

            # Track agent execution path for learning
            for agent_name, agent_data in chunk.items():
                if agent_data is not None and agent_name not in agent_path:
                    agent_path.append(agent_name)

        execution_time = time.time() - execution_start_time

        # Prepare final response with metadata
        final_response_data = {
            "data": response_data,
            "metadata": {
                "execution_time": execution_time,
                "agent_path": agent_path,
                "query_classification": (
                    learning_system.performance_tracker._classify_query(query)
                    if learning_system
                    else "unknown"
                ),
                "model_used": model_name,
                "reinforcement_enabled": learning_system is not None,
            },
        }

        # Store bot response in chat history
        bot_message_id = insert_chat_history(
            model_name,
            "bot",
            json.dumps({"data": response_data}, default=str),
            dashboard_id=dashboard_id,
        )

        # Add message ID to metadata for frontend feedback handling
        final_response_data["metadata"]["message_id"] = bot_message_id
        final_response_data["metadata"]["user_message_id"] = user_message_id

        final_response = json.dumps(final_response_data, default=str)

        logging.info(f"Query processed successfully. Agent path: {agent_path}")

        return Response(final_response, content_type="application/json")

    except Exception as e:
        logging.error(f"Error during query execution: {e} \n\n {tb.format_exc()}")
        error_response = {
            "error": "Query execution failed",
            "details": str(e),
            "metadata": {"reinforcement_enabled": learning_system is not None},
        }
        return Response(
            json.dumps(error_response), content_type="application/json", status=500
        )


@reinforced_agents.route("/feedback", methods=["POST"])
def handle_feedback():
    """New endpoint to handle user feedback (likes, dislikes, comments)"""

    data = request.json
    message_id = data.get("message_id")
    liked = data.get("liked", False)
    disliked = data.get("disliked", False)
    comment = data.get("comment", "")

    if not message_id:
        return jsonify({"error": "message_id is required"}), 400

    try:
        # Update database with feedback
        update_feedback_in_db(message_id, liked, disliked, comment)

        # Update learning system if available
        current_learning_system = initialize_learning_system()
        if current_learning_system:
            # Fetch the full chat record and update learning system
            chat_record = get_chat_record_by_id(message_id)
            if chat_record:
                current_learning_system.update_with_new_feedback(chat_record)
                logging.info(
                    f"Updated learning system with feedback for message {message_id}"
                )

        return jsonify(
            {
                "success": True,
                "message": "Feedback recorded successfully",
                "learning_system_updated": current_learning_system is not None,
            }
        )

    except Exception as e:
        logging.error(f"Error handling feedback: {e}")
        return jsonify({"error": "Failed to record feedback"}), 500


@reinforced_agents.route("/performance", methods=["GET"])
def get_performance_metrics():
    """Endpoint to get agent performance analytics"""

    current_learning_system = initialize_learning_system()
    if not current_learning_system:
        return jsonify({"error": "Learning system not available"}), 503

    try:
        metrics = (
            current_learning_system.performance_tracker.get_agent_performance_metrics()
        )

        # Format metrics for frontend consumption
        formatted_metrics = {
            "agents": {},
            "summary": {
                "total_agents": len(metrics),
                "high_performers": len(
                    [
                        m
                        for m in metrics.values()
                        if m["performance_grade"] in ["A", "B"]
                    ]
                ),
                "needs_improvement": len(
                    [
                        m
                        for m in metrics.values()
                        if m["performance_grade"] in ["D", "F"]
                    ]
                ),
            },
        }

        for agent_name, agent_metrics in metrics.items():
            formatted_metrics["agents"][agent_name] = {
                "grade": agent_metrics["performance_grade"],
                "satisfaction_score": round(agent_metrics["satisfaction_score"], 3),
                "like_rate": round(agent_metrics["like_rate"], 3),
                "dislike_rate": round(agent_metrics["dislike_rate"], 3),
                "total_interactions": agent_metrics["total_interactions"],
                "confidence": round(agent_metrics["confidence"], 3),
            }

        return jsonify(formatted_metrics)

    except Exception as e:
        logging.error(f"Error getting performance metrics: {e}")
        return jsonify({"error": "Failed to get performance metrics"}), 500


# Helper functions
def update_feedback_in_db(message_id, liked, disliked, comment):
    """Update feedback in database"""
    from utils.reinforcement_helpers import update_feedback_in_db as update_feedback

    return update_feedback(message_id, liked, disliked, comment)


def get_chat_record_by_id(message_id):
    """Fetch complete chat record by ID"""
    from utils.reinforcement_helpers import get_chat_record_by_id as get_record

    return get_record(message_id)


def get_database_connection():
    """Get database connection for learning system"""
    from utils.reinforcement_helpers import get_database_connection as get_connection

    return get_connection()


# Add a new endpoint for detailed agent analytics
@reinforced_agents.route("/agent_analytics/<agent_name>", methods=["GET"])
def get_agent_analytics(agent_name):
    """Get detailed analytics for a specific agent"""
    current_learning_system = initialize_learning_system()
    if not current_learning_system:
        return jsonify({"error": "Learning system not available"}), 503

    try:
        # Get all metrics
        metrics = (
            current_learning_system.performance_tracker.get_agent_performance_metrics()
        )

        # Check if agent exists
        if agent_name not in metrics:
            return jsonify({"error": f"Agent '{agent_name}' not found"}), 404

        # Get feedback patterns for this agent
        feedback_patterns = (
            current_learning_system.performance_tracker.feedback_patterns
        )

        # Filter patterns that include this agent
        agent_patterns = {}
        for pattern, feedbacks in feedback_patterns.items():
            if agent_name in pattern:
                pattern_key = " → ".join(pattern)
                agent_patterns[pattern_key] = {
                    "count": len(feedbacks),
                    "avg_score": (
                        sum(f["score"] for f in feedbacks) / len(feedbacks)
                        if feedbacks
                        else 0
                    ),
                    "query_types": {},
                }

                # Count query types
                for feedback in feedbacks:
                    query_type = feedback["query_type"]
                    if query_type not in agent_patterns[pattern_key]["query_types"]:
                        agent_patterns[pattern_key]["query_types"][query_type] = 0
                    agent_patterns[pattern_key]["query_types"][query_type] += 1

        # Prepare response
        agent_analytics = {
            "agent_name": agent_name,
            "performance": metrics[agent_name],
            "execution_patterns": agent_patterns,
            "recommendations": {"improvement_areas": [], "strengths": []},
        }

        # Add recommendations based on performance
        if metrics[agent_name]["performance_grade"] in ["D", "F"]:
            agent_analytics["recommendations"]["improvement_areas"].append(
                "Low satisfaction score - consider retraining or refining agent logic"
            )

        if metrics[agent_name]["like_rate"] > 0.7:
            agent_analytics["recommendations"]["strengths"].append(
                "High like rate - this agent performs well for users"
            )

        if metrics[agent_name]["total_interactions"] < 10:
            agent_analytics["recommendations"]["improvement_areas"].append(
                "Limited data - need more interactions for reliable performance assessment"
            )

        return jsonify(agent_analytics)

    except Exception as e:
        logging.error(f"Error getting agent analytics: {e}")
        return jsonify({"error": "Failed to get agent analytics"}), 500


# Add a new endpoint for feedback statistics
@reinforced_agents.route("/feedback_stats", methods=["GET"])
def get_feedback_stats():
    """Get statistics about user feedback"""
    try:
        connection = get_database_connection()
        if not connection:
            return jsonify({"error": "Database connection failed"}), 500

        with connection.cursor(cursor_factory=RealDictCursor) as cursor:
            # Get overall feedback stats
            cursor.execute(
                """
            SELECT 
                COUNT(*) as total_feedback,
                SUM(CASE WHEN liked = TRUE THEN 1 ELSE 0 END) as likes,
                SUM(CASE WHEN disliked = TRUE THEN 1 ELSE 0 END) as dislikes,
                SUM(CASE WHEN comment != '' THEN 1 ELSE 0 END) as comments,
                ROUND(AVG(CASE WHEN liked = TRUE THEN 1 WHEN disliked = TRUE THEN 0 ELSE NULL END)::numeric, 2) as satisfaction_rate
            FROM chat_history
            WHERE role = 'bot' AND (liked = TRUE OR disliked = TRUE)
            """
            )

            overall_stats = dict(cursor.fetchone() or {})

            # Get feedback trend over time
            cursor.execute(
                """
            SELECT 
                DATE_TRUNC('day', updated_at) as date,
                COUNT(*) as total,
                SUM(CASE WHEN liked = TRUE THEN 1 ELSE 0 END) as likes,
                SUM(CASE WHEN disliked = TRUE THEN 1 ELSE 0 END) as dislikes
            FROM chat_history
            WHERE role = 'bot' AND (liked = TRUE OR disliked = TRUE)
            GROUP BY DATE_TRUNC('day', updated_at)
            ORDER BY date DESC
            LIMIT 30
            """
            )

            trend_data = [dict(row) for row in cursor.fetchall()]

            # Get model performance comparison
            cursor.execute(
                """
            SELECT 
                model_name,
                COUNT(*) as total,
                SUM(CASE WHEN liked = TRUE THEN 1 ELSE 0 END) as likes,
                SUM(CASE WHEN disliked = TRUE THEN 1 ELSE 0 END) as dislikes,
                ROUND(AVG(CASE WHEN liked = TRUE THEN 1 WHEN disliked = TRUE THEN 0 ELSE NULL END)::numeric, 2) as satisfaction_rate
            FROM chat_history
            WHERE role = 'bot' AND (liked = TRUE OR disliked = TRUE)
            GROUP BY model_name
            ORDER BY total DESC
            """
            )

            model_comparison = [dict(row) for row in cursor.fetchall()]

        connection.close()

        return jsonify(
            {"overall": overall_stats, "trend": trend_data, "models": model_comparison}
        )

    except Exception as e:
        logging.error(f"Error getting feedback stats: {e}")
        return jsonify({"error": "Failed to get feedback statistics"}), 500


# Optional: Endpoint to manually retrain the system
@reinforced_agents.route("/retrain", methods=["POST"])
def retrain_system():
    """Manually retrain the reinforcement learning system"""

    try:
        global learning_system
        learning_system = None  # Reset
        current_learning_system = initialize_learning_system()

        if current_learning_system:
            return jsonify(
                {
                    "success": True,
                    "message": "System retrained successfully",
                    "timestamp": time.time(),
                }
            )
        else:
            return jsonify({"error": "Failed to retrain system"}), 500

    except Exception as e:
        logging.error(f"Error retraining system: {e}")
        return jsonify({"error": "Retraining failed"}), 500
