import os
import json
import random
import psycopg2
import logging
from config import Config, Database
from utils import insert_chat_history
from flask import Blueprint, request, Response, jsonify


chat_bp = Blueprint("chat", __name__)
MODEL_PROVIDERS = json.loads(os.environ.get("MODEL_PROVIDERS"))
GREETINGS = {"hi", "hello", "hey", "good morning", "good afternoon", "good evening"}
is_docker = os.getenv("IS_DOCKER", "false").lower() == "true"
host = "db" if is_docker else "localhost"
BACKUP_QUESTIONS = [
    "What is the overall battalion lethality rating, how did you reach that answer, and provide recommendations on how to increase the rating?",
    "What is FRT and which is my best FRT based on Proficiency ?",
    "How many unserviceable vehicles do I have, and how could I improve this?",
    "I have a space on a Challenger course, who should I send and why, and what effect will that have on Battalion lethality index?",
    "Please provide information on the consequences of sending Sgt Class 1 VM vehicle inspector to the Falkland Islands for 3 months, and effect on lethality rating.",
    "Who is the most experienced armourer, and why?",
    "Who is the most experienced VM and why?",
    "Who is the most experienced technician and why?",
    "Given current equipment availability, how can I improve overall availability?",
    "How would you suggest reorganizing the FRTs to increase overall lethality rating?",
    "Are the RECs up to date? If not, indicate which ones require updating.",
]


config = Config()
db_instance = Database(config.CONNECTION_STRING)
superset_db = {
    "dbname": "superset",
    "user": "superset",
    "password": "superset",
    "host": host,
    "port": 5432,
}

superset_connection = psycopg2.connect(**superset_db)
# adjust import as needed


@chat_bp.route("/last_two_messages", methods=["GET"])
def get_last_two_conversations():
    # Get parameters from query string
    message_id = request.args.get("message_id", type=int)
    dashboard_id = request.args.get("dashboard_id", type=int)
    model_name = request.args.get("model_name")

    # Validate required parameters
    if not all([message_id, dashboard_id, model_name]):
        return (
            jsonify(
                {
                    "error": "Missing required parameters: message_id, dashboard_id, model_name"
                }
            ),
            400,
        )

    try:
        with superset_connection.cursor() as cursor:
            # Fetch all chat messages for the dashboard and model
            cursor.execute(
                """
                SELECT id, role, content, datetime, is_liked, is_disliked, comment
                FROM chat_history
                WHERE dashboard_id = %s AND model_name = %s
                ORDER BY datetime ASC
                """,
                (dashboard_id, model_name),
            )
            rows = cursor.fetchall()

            # Convert to dict for easier handling
            messages = [
                {
                    "id": row[0],
                    "role": row[1],
                    "content": row[2],
                    "datetime": row[3],
                    "is_liked": row[4],
                    "is_disliked": row[5],
                    "comment": row[6] if row[6] else "",
                }
                for row in rows
            ]

            # Find index of the current user message (we want to exclude this)
            current_user_index = next(
                (
                    i
                    for i, msg in enumerate(messages)
                    if msg["id"] == message_id and msg["role"] == "user"
                ),
                None,
            )

            if current_user_index is None:
                return jsonify({"error": "User message not found"}), 404

            # Get messages before the current user message
            previous_messages = messages[:current_user_index]

            # Find conversation pairs (user-bot pairs) in reverse order
            conversation = []
            pairs_collected = 0
            i = len(previous_messages) - 1

            # Look for pairs in reverse chronological order
            while i >= 1 and pairs_collected < 2:
                # Check if we have a bot message followed by a user message (in reverse chronological order)
                if (
                    previous_messages[i]["role"] == "bot"
                    and previous_messages[i - 1]["role"] == "user"
                ):

                    user_msg = previous_messages[i - 1].copy()
                    bot_msg = previous_messages[i].copy()

                    # Transform the messages to required schema
                    # For user message: role, content, comment (if present)
                    transformed_user = {
                        "role": user_msg["role"],
                        "content": user_msg["content"],
                    }
                    if user_msg["comment"]:
                        transformed_user["comment"] = user_msg["comment"]

                    # For bot message: extract the final response and create schema
                    bot_content = ""
                    try:
                        import json

                        bot_data = json.loads(bot_msg["content"])
                        # Extract the final response from combiner_agent
                        final_response = (
                            bot_data.get("data", [])[-1]
                            .get("combiner_agent", {})
                            .get("agent_outputs", {})
                            .get("combiner_agent", {})
                            .get("response", "")
                        )
                        bot_content = final_response
                    except (json.JSONDecodeError, IndexError, KeyError, AttributeError):
                        # Fallback to original content if parsing fails
                        bot_content = bot_msg["content"][:500]

                    transformed_bot = {"role": bot_msg["role"], "content": bot_content}
                    if bot_msg["comment"]:
                        transformed_bot["comment"] = bot_msg["comment"]
                        if bot_msg["is_liked"]:
                            transformed_bot["is_liked"] = bot_msg["is_liked"]
                        if bot_msg["is_disliked"]:
                            transformed_bot["is_disliked"] = bot_msg["is_disliked"]

                    # Insert at beginning to maintain chronological order
                    conversation.insert(0, transformed_bot)
                    conversation.insert(0, transformed_user)

                    pairs_collected += 1
                    i -= 2  # Move back by 2 to find next pair
                else:
                    i -= 1  # Keep searching in reverse

            return jsonify({"chat_history": conversation})

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@chat_bp.route("/update_reaction", methods=["POST"])
def update_reaction():
    try:
        data = request.json
        message_id = data.get("id")
        like = data.get("like")
        dislike = data.get("dislike")

        if message_id is None:
            return jsonify({"error": "Message ID is required"}), 400

        if like and dislike:
            return (
                jsonify({"error": "A message cannot be both liked and disliked"}),
                400,
            )

        cursor = superset_connection.cursor()

        update_query = """
        UPDATE chat_history
        SET is_liked = %s, is_disliked = %s
        WHERE id = %s
        """
        cursor.execute(update_query, (like, dislike, message_id))
        superset_connection.commit()

        cursor.close()

        return jsonify({"message": "Reaction updated successfully"})

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@chat_bp.route("/update_comment", methods=["POST"])
def update_comment():
    try:
        data = request.json
        message_id = data.get("id")
        comment = data.get("comment")

        if message_id is None:
            return jsonify({"error": "Message ID is required"}), 400

        cursor = superset_connection.cursor()

        update_query = """
        UPDATE chat_history
        SET comment = %s
        WHERE id = %s
        """
        cursor.execute(update_query, (comment, message_id))
        superset_connection.commit()

        cursor.close()

        return jsonify({"message": "Comment updated successfully"})

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@chat_bp.route("/chat_history", methods=["GET"])
def get_chat_history():
    dashboard_id = request.args.get("dashboard_id")
    # dataset_id = request.args.get("dataset_id")
    model_name = request.args.get("model_name")

    if not dashboard_id or not model_name:
        return jsonify({"error": "dashboard_id and model_name are required"}), 400

    try:
        with superset_connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT id, role, content, datetime, is_liked, is_disliked, comment
                FROM chat_history
                WHERE dashboard_id = %s AND model_name = %s
                ORDER BY datetime ASC, role DESC
                """,
                (dashboard_id, model_name),
            )
            rows = cursor.fetchall()

            messages = [
                {
                    "id": row[0],
                    "role": row[1],
                    "content": row[2],
                    "datetime": row[3],
                    "is_liked": row[4],
                    "is_disliked": row[5],
                    "comment": row[6] if row[6] else "",
                }
                for row in rows
            ]
            return jsonify({"chat_history": messages})

    except Exception as e:
        print(f"Error fetching chat history: {e}")
        return jsonify({"error": "Failed to fetch chat history"}), 500


@chat_bp.route("/response_add", methods=["POST"])
def add_response():

    data = request.json
    dashboard_id = data.get("dashboard_id")
    # dataset_id = request.args.get("dataset_id")
    model_name = data.get("model_name")
    response_data = data.get("response_data")

    if not all([dashboard_id, model_name, response_data]):
        return (
            jsonify({"error": "dashboard_id, model_name, and user_query are required"}),
            400,
        )
    try:
        # message_id = insert_chat_history(
        #     model_name, "bot", json.dumps(response_data), dashboard_id=dashboard_id
        # )
        return jsonify(
            {"message": "Message Added Successfully", "message_id": dashboard_id}
        )
    except BaseException as e:
        return (
            jsonify({"error": "Error while adding responsre data to DB"}),
            400,
        )


@chat_bp.route("/chat/message/<int:message_id>", methods=["GET"])
def get_message(message_id):
    try:
        cursor = superset_connection.cursor()

        query = """
        SELECT id, dashboard_id, dataset_id, model_name, role, content, datetime, is_liked, is_disliked, comment
        FROM chat_history
        WHERE id = %s
        """
        cursor.execute(query, (message_id,))
        message = cursor.fetchone()

        cursor.close()

        if not message:
            return jsonify({"error": "Message not found"}), 404

        return jsonify(
            {
                "id": message[0],
                "dashboard_id": message[1],
                "dataset_id": message[2],
                "model_name": message[3],
                "role": message[4],
                "content": message[5],
                "datetime": message[6],
                "is_liked": message[7],
                "is_disliked": message[8],
                "comment": message[9],
            }
        )

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@chat_bp.route("/initial_question_recommendation", methods=["GET"])
def initial_question_recommendation():
    dashboard_id = request.args.get("dashboard_id")

    if not dashboard_id:
        return jsonify({"error": "dashboard_id is required"}), 400

    try:
        with superset_connection.cursor() as cursor:
            query = """
                SELECT DISTINCT content
                FROM chat_history
                WHERE dashboard_id = %s AND role = 'user'
            """
            cursor.execute(query, (dashboard_id,))
            rows = cursor.fetchall()

            unique_questions = set()
            all_questions = []

            for row in rows:
                question = row[0].strip()
                normalized_question = question.lower()
                if (
                    normalized_question not in GREETINGS
                    and normalized_question not in unique_questions
                ):
                    unique_questions.add(normalized_question)
                    all_questions.append(question)

            for backup in BACKUP_QUESTIONS:
                if backup.lower() not in unique_questions:
                    unique_questions.add(backup.lower())
                    all_questions.append(backup)

            recommendations = random.sample(all_questions, min(4, len(all_questions)))

            return jsonify({"recommendations": recommendations})

    except Exception as e:
        print(f"Error fetching recommendations: {e}")
        return jsonify({"error": "Failed to fetch recommendations"}), 500


def get_message_data(message_id):
    try:
        with superset_connection.cursor() as cursor:

            query = "SELECT content FROM chat_history WHERE id = %s"
            cursor.execute(query, (message_id,))
            result = cursor.fetchone()
            return result[0] if result else None

    except Exception as e:
        print(f"Error fetching message data: {e}")
        return None


from uuid import uuid4
import json


def generate_graph_structure(message_data):
    """
    Generates a graph structure (nodes and edges) from the message data to visualize the query flow.
    Places rag_agent derivatives (dashboard_agent, dashboard_file_agent) above the rag_agent node.
    Only connects rag_agent's derivatives to the final_response node, not rag_agent itself.

    Args:
        message_data (dict): The JSON payload containing the agentic flow data.

    Returns:
        dict: A dictionary containing nodes and edges for the graph.
    """
    nodes = []
    edges = []

    # Constants for positioning
    X_STEP = 300  # Horizontal spacing between layers
    Y_STEP = 150  # Vertical spacing between nodes in the same layer
    X_START = 0  # Starting x-coordinate
    Y_BASE = 300  # Base y-coordinate for centering
    FILE_SPACING = 100  # Spacing for file nodes

    # Helper function to calculate y-positions, stacking upward for rag sub-agents
    def calculate_y_positions(num_nodes, base_y=Y_BASE, y_step=Y_STEP, upward=False):
        if num_nodes == 0:
            return []
        if num_nodes == 1:
            return [base_y]
        if upward:
            # Stack nodes upward from base_y
            return [base_y - (i * y_step) for i in range(num_nodes)]
        else:
            # Center nodes around base_y
            start_y = base_y - ((num_nodes - 1) * y_step) / 2
            return [start_y + (i * y_step) for i in range(num_nodes)]

    # Helper function to add a node
    def add_node(
        node_id, label, response, is_agent=True, node_type=None, x=X_START, y=Y_BASE
    ):
        node = {
            "id": node_id,
            "data": {"label": label, "response": response},
            "isagent": is_agent,
            "position": {"x": x, "y": y},
        }
        if node_type:
            node["type"] = node_type
        nodes.append(node)

    # Helper function to add an edge
    def add_edge(source, target):
        edge_id = f"e_{source}_{target}_{str(uuid4())[:8]}"
        edges.append({"id": edge_id, "source": source, "target": target})

    # Step 1: Start Node (User Query)
    master_data = next(
        item["master_agent"] for item in message_data["data"] if "master_agent" in item
    )
    user_query = master_data["query"]
    add_node(
        "start",
        "Start",
        user_query,
        is_agent=False,
        node_type="input",
        x=X_START,
        y=Y_BASE,
    )

    # Step 2: Master Agent Node
    selected_agents = master_data.get("selected_agents", [])
    master_response = (
        f"Selected Agents: {', '.join(selected_agents)}"
        if selected_agents
        else "No Agents Selected"
    )
    add_node(
        "master_agent", "Master Agent", master_response, x=X_START + X_STEP, y=Y_BASE
    )
    add_edge("start", "master_agent")

    # Step 3: Sub-Agent Nodes (e.g., rag_agent, pdf_agent)
    combiner_data = next(
        item["combiner_agent"]
        for item in message_data["data"]
        if "combiner_agent" in item
    )
    agent_outputs = combiner_data.get("agent_outputs", {})
    x_sub_agents = X_START + 2 * X_STEP
    y_positions = calculate_y_positions(len(selected_agents))

    agent_nodes = {}
    file_y_positions = {}  # To track file node positions for pdf/csv

    for index, agent in enumerate(selected_agents):
        agent_id = agent
        agent_response = agent_outputs.get(agent, "No response available")
        if isinstance(agent_response, dict):
            # Handle dictionary responses (e.g., dashboard_file_agent with file paths)
            agent_response = "\n".join([f"{k}: {v}" for k, v in agent_response.items()])
        y_pos = y_positions[index]
        add_node(
            agent_id,
            agent.replace("_", " ").title(),
            agent_response,
            x=x_sub_agents,
            y=y_pos,
        )
        add_edge("master_agent", agent_id)
        agent_nodes[agent] = agent_id
        if agent in ["pdf_agent", "csv_agent"]:
            file_y_positions[agent] = {"x": x_sub_agents, "y": y_pos}

    # Step 4: RAG Agent Sub-Agents (dashboard_agent, dashboard_file_agent) - Place above rag_agent
    rag_data = next(
        (item["rag_agent"] for item in message_data["data"] if "rag_agent" in item),
        None,
    )
    if rag_data and "rag_agent" in agent_nodes:
        rag_selected_agents = rag_data.get("rag_selected_agents", [])
        x_rag_sub_agents = x_sub_agents + X_STEP
        # Find rag_agent's y-position
        rag_y = next(
            node["position"]["y"] for node in nodes if node["id"] == "rag_agent"
        )
        # Stack rag sub-agents upward from rag_agent's y-position
        y_rag_positions = calculate_y_positions(
            len(rag_selected_agents), base_y=rag_y, upward=True
        )

        for index, rag_agent in enumerate(rag_selected_agents):
            rag_agent_id = rag_agent
            rag_response = agent_outputs.get(rag_agent, "No response available")
            if isinstance(rag_response, dict):
                # Handle dictionary responses (e.g., dashboard_file_agent with file paths)
                rag_response = "\n".join([f"{k}: {v}" for k, v in rag_response.items()])
            y_pos = y_rag_positions[index]
            add_node(
                rag_agent_id,
                rag_agent.replace("_", " ").title(),
                rag_response,
                x=x_rag_sub_agents,
                y=y_pos,
            )
            add_edge("rag_agent", rag_agent_id)
            agent_nodes[rag_agent] = rag_agent_id

    # Step 5: File Nodes (PDF/CSV)
    pdf_files = combiner_data.get("pdf_file_path", [])
    csv_files = combiner_data.get("csv_file_paths", [])
    x_files = (
        x_sub_agents - X_STEP
    )  # Place file nodes to the left of their respective agents

    # PDF Files
    for pdf in pdf_files:
        if "pdf_agent" in agent_nodes:
            pdf_id = pdf.split("/")[-1]
            pdf_position = file_y_positions.get(
                "pdf_agent", {"x": x_files, "y": Y_BASE}
            )
            pdf_position["y"] += (
                FILE_SPACING if pdf_position["y"] >= Y_BASE else -FILE_SPACING
            )
            add_node(
                pdf,
                pdf_id,
                f"PDF File: {pdf_id}",
                is_agent=False,
                x=x_files,
                y=pdf_position["y"],
            )
            add_edge(pdf, "pdf_agent")

    # CSV Files
    for csv in csv_files:
        if "csv_agent" in agent_nodes:
            csv_id = csv.split("/")[-1]
            csv_position = file_y_positions.get(
                "csv_agent", {"x": x_files, "y": Y_BASE}
            )
            csv_position["y"] += (
                FILE_SPACING if csv_position["y"] >= Y_BASE else -FILE_SPACING
            )
            add_node(
                csv,
                csv_id,
                f"CSV File: {csv_id}",
                is_agent=False,
                x=x_files,
                y=csv_position["y"],
            )
            add_edge(csv, "csv_agent")

    # Step 6: Dashboard File Agent Files
    if "dashboard_file_agent" in agent_outputs and isinstance(
        agent_outputs["dashboard_file_agent"], dict
    ):
        file_responses = agent_outputs["dashboard_file_agent"]
        x_file_nodes = x_rag_sub_agents - X_STEP
        # Stack file nodes upward from dashboard_file_agent's y-position
        dashboard_file_y = next(
            node["position"]["y"]
            for node in nodes
            if node["id"] == "dashboard_file_agent"
        )
        file_y_positions = calculate_y_positions(
            len(file_responses),
            base_y=dashboard_file_y,
            y_step=FILE_SPACING,
            upward=True,
        )

        for index, (file_path, response) in enumerate(file_responses.items()):
            file_id = file_path.split("/")[-1]
            y_pos = file_y_positions[index]
            add_node(
                file_id,
                file_id,
                f"File: {file_id}\nResponse: {response}",
                is_agent=False,
                x=x_file_nodes,
                y=y_pos,
            )
            add_edge(file_id, "dashboard_file_agent")

    # Step 7: Final Response Node (Combiner Agent)
    final_response = agent_outputs.get("combiner_agent", {}).get(
        "response", "No final response available"
    )
    x_final = max(node["position"]["x"] for node in nodes) + X_STEP
    add_node("final_response", "Final Response", final_response, x=x_final, y=Y_BASE)

    # Connect top-level agents (except rag_agent) and RAG sub-agents to final response
    for agent in agent_nodes:
        if agent != "rag_agent":  # Exclude rag_agent from connecting to final_response
            add_edge(agent_nodes[agent], "final_response")

    # Step 8: End Node
    add_node(
        "end",
        "End",
        "Query Processing Complete",
        is_agent=False,
        node_type="output",
        x=x_final + X_STEP,
        y=Y_BASE,
    )
    add_edge("final_response", "end")

    return {"nodes": nodes, "edges": edges}


@chat_bp.route("/get-graph", methods=["GET"])
def get_graph():
    """
    Flask endpoint to generate and return the graph structure for a given message ID.

    Query Parameters:
        message_id (str): The ID of the message to generate the graph for.

    Returns:
        JSON: The graph structure containing nodes and edges, or an error message.
    """
    message_id = request.args.get("message_id")
    if not message_id:
        return jsonify({"error": "Message ID is required"}), 400

    message_data = get_message_data(message_id)
    if not message_data:
        return jsonify({"error": "Message not found"}), 404

    try:
        graph_data = generate_graph_structure(json.loads(message_data))
        return jsonify(graph_data)
    except Exception as e:
        return jsonify({"error": f"Failed to generate graph: {str(e)}"}), 500


# def generate_graph_structure(message_data):
#     nodes = []
#     edges = []

#     # Start Node
#     nodes.append(
#         {
#             "id": "start",
#             "data": {
#                 "label": "Start",
#                 "response": message_data["data"][0]["master_agent"]["query"],
#             },
#             "type": "input",
#             "isagent": True,
#             "position": {"x": 0, "y": 300},
#         }
#     )

#     # Master Agent Node
#     master_node_id = "master_agent"
#     master_data = message_data["data"][0]["master_agent"]
#     selected_agents = master_data.get("selected_agents", [])
#     master_response = (
#         f"Selected Agents are {selected_agents}"
#         if selected_agents
#         else "No Agents Selected"
#     )
#     nodes.append(
#         {
#             "id": master_node_id,
#             "data": {"label": "Master Agent", "response": master_response},
#             "isagent": True,
#             "position": {"x": 300, "y": 300},
#         }
#     )
#     edges.append({"id": "e_start_master", "source": "start", "target": master_node_id})

#     agent_nodes = {}
#     combiner_agent_data = message_data["data"][-1]["combiner_agent"]
#     agent_outputs = combiner_agent_data.get("agent_outputs", {})

#     x_offset = 600
#     y_base = 300
#     y_step = 300

#     # Use selected_agents from master_agent
#     if len(selected_agents) == 1:
#         start_y = y_base
#     else:
#         start_y = y_base - ((len(selected_agents) - 1) * y_step // 2)

#     file_y_positions = {}

#     for index, agent in enumerate(selected_agents):
#         agent_id = agent
#         agent_response = agent_outputs.get(agent, "No response available")

#         # Set response based on agent type
#         if agent == "dashboard_file_agent":
#             agent_response = "Aggregates responses from source files"
#         elif agent in ["pdf_agent", "csv_agent"]:
#             agent_response = agent_outputs.get(
#                 agent, f"Processes {agent.replace('_agent', '')} files"
#             )
#         elif isinstance(agent_response, dict):
#             agent_response = str(agent_response)

#         nodes.append(
#             {
#                 "id": agent_id,
#                 "data": {
#                     "label": agent.replace("_", " ").title(),
#                     "response": agent_response,
#                 },
#                 "isagent": True,
#                 "position": {"x": x_offset, "y": start_y + (index * y_step)},
#             }
#         )
#         edges.append(
#             {"id": f"e_master_{agent_id}", "source": master_node_id, "target": agent_id}
#         )
#         file_y_positions[agent] = {"x": x_offset, "y": start_y + (index * y_step)}
#         agent_nodes[agent] = agent_id

#     # File nodes for dashboard_file_agent
#     x_spacing = 200  # Horizontal spacing between file nodes
#     file_paths = []
#     if "dashboard_file_agent" in agent_outputs and isinstance(
#         agent_outputs["dashboard_file_agent"], dict
#     ):
#         file_paths = list(agent_outputs["dashboard_file_agent"].keys())

#     file_y_offset = file_y_positions.get("dashboard_file_agent", {"y": y_base})["y"]
#     file_x_base = x_offset - x_spacing  # Start to the left of dashboard_file_agent

#     for index, file_path in enumerate(file_paths):
#         if "dashboard_file_agent" in agent_nodes:
#             file_id = file_path.split("/")[-1]
#             file_position = {
#                 "x": file_x_base - (index * x_spacing),  # Move left for each file
#                 "y": file_y_offset,
#             }
#             file_response = agent_outputs["dashboard_file_agent"].get(
#                 file_path, "No response available"
#             )
#             nodes.append(
#                 {
#                     "id": file_path,
#                     "data": {"label": file_id, "response": file_response},
#                     "isagent": False,
#                     "position": file_position,
#                 }
#             )
#             edges.append(
#                 {
#                     "id": f"e_{file_id}_dashboard_file_agent",
#                     "source": file_path,
#                     "target": agent_nodes["dashboard_file_agent"],
#                 }
#             )

#     # File nodes for pdf_agent
#     pdf_files = combiner_agent_data.get("pdf_file_path", [])
#     file_y_offset = file_y_positions.get("pdf_agent", {"y": y_base})["y"]
#     file_x_base = x_offset - x_spacing  # Start to the left of pdf_agent

#     for index, pdf in enumerate(pdf_files):
#         if "pdf_agent" in agent_nodes:
#             pdf_id = pdf.split("/")[-1]
#             file_position = {
#                 "x": file_x_base - (index * x_spacing),  # Move left for each file
#                 "y": file_y_offset,
#             }
#             # Use pdf_agent's response for the file, as it’s document-specific
#             pdf_response = agent_outputs.get("pdf_agent", "No response available")
#             nodes.append(
#                 {
#                     "id": pdf,
#                     "data": {"label": pdf_id, "response": pdf_response},
#                     "isagent": False,
#                     "position": file_position,
#                 }
#             )
#             edges.append(
#                 {
#                     "id": f"e_{pdf_id}_pdf_agent",
#                     "source": pdf,
#                     "target": agent_nodes["pdf_agent"],
#                 }
#             )

#     # File nodes for csv_agent
#     csv_files = combiner_agent_data.get("csv_file_paths", [])
#     file_y_offset = file_y_positions.get("csv_agent", {"y": y_base})["y"]
#     file_x_base = x_offset - x_spacing  # Start to the left of csv_agent

#     for index, csv in enumerate(csv_files):
#         if "csv_agent" in agent_nodes:
#             csv_id = csv.split("/")[-1]
#             file_position = {
#                 "x": file_x_base - (index * x_spacing),  # Move left for each file
#                 "y": file_y_offset,
#             }
#             csv_response = agent_outputs.get("csv_agent", "No response available")
#             nodes.append(
#                 {
#                     "id": csv,
#                     "data": {"label": csv_id, "response": csv_response},
#                     "isagent": False,
#                     "position": file_position,
#                 }
#             )
#             edges.append(
#                 {
#                     "id": f"e_{csv_id}_csv_agent",
#                     "source": csv,
#                     "target": agent_nodes["csv_agent"],
#                 }
#             )

#     # Final Response Node
#     response_node_id = "final_response"
#     final_response = agent_outputs.get("combiner_agent", {}).get(
#         "response", "No final response available"
#     )
#     nodes.append(
#         {
#             "id": response_node_id,
#             "data": {"label": "Final Response", "response": final_response},
#             "isagent": True,
#             "position": {"x": 900, "y": 300},
#         }
#     )

#     # Connect agents to final response
#     for agent in selected_agents:
#         edges.append(
#             {"id": f"e_{agent}_final", "source": agent, "target": response_node_id}
#         )

#     edges.append({"id": "e_response_end", "source": response_node_id, "target": "end"})

#     # End Node
#     nodes.append(
#         {
#             "id": "end",
#             "data": {"label": "End"},
#             "type": "output",
#             "isagent": False,
#             "position": {"x": 1200, "y": 300},
#         }
#     )

#     return {"nodes": nodes, "edges": edges}
