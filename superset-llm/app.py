import os
import logging
from flask import Flask
from flask_cors import CORS
from routers.chat import chat_bp
from routers.agents import agents_bp
from routers.dataset import dataset_bp
from routers.dashboard import dashboard_bp
from utils.common import database_migrations
from routers.preference import preference_bp
# from routers.pdf_csv_agent import individual_agents_bp
from routers.reinforced_agents import reinforced_agents
from utils.reinforcement_helpers import initialize_reinforcement_system
from agents.reinforcement_agent import FeedbackLearningSystem
from routers.pipeline_routers import pipeline_bp


app = Flask(__name__)
# cors = CORS(app)
cors = CORS(app, origins=["*"])


# Global learning system instance
learning_system = None


def initialize_app():
    """Initialize the application components"""
    global learning_system

    # Initialize database tables for feedback
    initialize_reinforcement_system()

    # Initialize learning system
    try:
        from utils.reinforcement_helpers import get_database_connection

        db_connection = get_database_connection()
        if db_connection:
            learning_system = FeedbackLearningSystem(db_connection)
            learning_system.initialize_from_feedback_data()
            logging.info("Reinforcement learning system initialized successfully")
        else:
            logging.error(
                "Failed to initialize learning system: database connection failed"
            )
    except Exception as e:
        logging.error(f"Failed to initialize learning system: {e}")
        learning_system = None


# Call this function during app startup

app.register_blueprint(reinforced_agents, url_prefix="/reinforced_agent")
app.register_blueprint(dashboard_bp, url_prefix="/dashboard")
app.register_blueprint(dataset_bp, url_prefix="/dataset")
app.register_blueprint(preference_bp, url_prefix="/preference")
app.register_blueprint(agents_bp, url_prefix="/agent")
app.register_blueprint(chat_bp, url_prefix="/chat")
# app.register_blueprint(individual_agents_bp, url_prefix="/individual_agents")
app.register_blueprint(pipeline_bp, url_prefix="/pipeline")



if __name__ == "__main__":
    os.makedirs("/tmp/indexes", exist_ok=True)
    database_migrations()
    initialize_app()
    app.run(host="0.0.0.0", port=8111)
